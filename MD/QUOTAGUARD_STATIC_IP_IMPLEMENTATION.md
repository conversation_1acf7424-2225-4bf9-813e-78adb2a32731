# QuotaGuard Static IP Implementation Guide

## Table of Contents
1. [Overview](#overview)
2. [Problem Statement](#problem-statement)
3. [QuotaGuard Static IP Solution](#quotaguard-static-ip-solution)
4. [Current HTTP Request Analysis](#current-http-request-analysis)
5. [Implementation Plan](#implementation-plan)
6. [Code Changes Required](#code-changes-required)
7. [Environment Configuration](#environment-configuration)
8. [Testing & Verification](#testing--verification)
9. [Deployment Instructions](#deployment-instructions)
10. [Troubleshooting](#troubleshooting)
11. [Cost Considerations](#cost-considerations)

## Overview

This document provides a comprehensive guide for implementing QuotaGuard Static IP for the CardioWell backend application deployed on Heroku. The implementation ensures all outbound HTTP requests use a fixed IP address, which is essential for connecting to external services that require IP whitelisting.

### Key Benefits
- **Fixed IP Address**: All outbound requests appear to come from a single, static IP
- **External Service Compatibility**: Enables integration with APIs requiring IP whitelisting
- **Heroku Integration**: Seamless add-on integration with automatic configuration
- **Security Enhancement**: Predictable outbound traffic for firewall rules

## Problem Statement

### Current State
By default, Heroku applications use dynamic IP addresses that change whenever:
- Dynos restart
- Application is redeployed
- Heroku performs maintenance

### Issues This Creates
1. **API Integration Failures**: External services requiring IP whitelisting cannot reliably connect
2. **Security Compliance**: Some client environments require fixed IP addresses for data exchange
3. **Database Access**: External databases with IP restrictions cannot be accessed
4. **Third-party Services**: Payment processors, communication APIs may require static IPs

### Affected Services in CardioWell
- **iGlucose API**: Glucose meter data integration
- **Withings API**: Blood pressure and health device integration
- **Twilio**: SMS messaging services
- **SendGrid**: Email delivery services
- **OpenAI**: AI assistant functionality
- **Customer Endpoints**: Data forwarding to client systems

## QuotaGuard Static IP Solution

### How It Works
QuotaGuard Static IP provides a proxy service that:
1. Routes all outbound HTTP/HTTPS traffic through static IP addresses
2. Integrates seamlessly with Heroku as a managed add-on
3. Requires minimal code changes to implement
4. Supports multiple protocols and authentication methods

### Architecture Overview
```
CardioWell App → QuotaGuard Proxy → External API
(Dynamic IP)   → (Static IP)     → (Whitelisted IP)
```

## Current HTTP Request Analysis

### HTTP Libraries Used
The CardioWell backend currently uses multiple HTTP client libraries:

| Library | Version | Usage | Files Affected |
|---------|---------|-------|----------------|
| `axios` | 1.6.8 | Primary HTTP client | 5 files |
| `node-fetch` | 2.6.1 | Withings API integration | 4 files |
| `@sendgrid/mail` | 6.5.5 | Email services | 2 files |
| `twilio` | 3.43.0 | SMS services | 3 files |
| `openai` | 4.78.1 | AI assistant | 1 file |

### External API Endpoints Currently Used

#### Critical APIs Requiring Static IP
1. **iGlucose API** (`https://api.iglucose.com/`)
   - Used for glucose meter data retrieval
   - File: `controller/Iglucose.js`

2. **Withings Medical API** (`https://wbsapi.us.withingsmed.net/`)
   - Blood pressure and health device integration
   - Files: Multiple in `withings/service/` directory

3. **Customer Data Forwarding**
   - Dynamic endpoints configured per customer
   - File: `device/service/forwardMessage.mjs`

#### Third-party Service APIs
4. **Twilio API** (SDK-based)
   - SMS messaging and webhooks
   - Files: `deviceUpdates/service/sendText.mjs`, `users/service/sendText.mjs`

5. **SendGrid API** (SDK-based)
   - Email delivery services
   - Files: `users/service/sendEmail.mjs`, `routes/users.mjs`

6. **OpenAI API** (SDK-based)
   - AI assistant functionality
   - File: `patient-assistant/assistant.mjs`

## Implementation Plan

### Phase 1: Infrastructure Setup
1. **Add QuotaGuard Static to Heroku**
   - Install add-on via Heroku dashboard
   - Obtain proxy URL from config vars

2. **Install Required Dependencies**
   - Add `https-proxy-agent` to package.json
   - Update yarn.lock file

### Phase 2: Code Implementation
1. **Create Proxy Configuration Utility**
   - Centralized proxy agent configuration
   - Support for both axios and fetch

2. **Update HTTP Client Configurations**
   - Modify axios calls to use proxy agent
   - Update fetch calls with proxy agent
   - Configure third-party SDKs where possible

### Phase 3: Testing & Verification
1. **Local Testing**
   - Test with local proxy configuration
   - Verify existing functionality

2. **Staging Deployment**
   - Deploy to staging environment
   - Verify static IP usage

3. **Production Deployment**
   - Deploy to production
   - Monitor for issues

## Code Changes Required

### Files Requiring Modifications

| File Path | Library Used | Change Type | Priority |
|-----------|--------------|-------------|----------|
| `utils/proxyConfig.mjs` | N/A | NEW FILE | High |
| `package.json` | N/A | Dependency | High |
| `device/service/forwardMessage.mjs` | axios | Proxy Config | High |
| `controller/Iglucose.js` | axios | Proxy Config | High |
| `customer/service/testEndpointTelemetryMessage.mjs` | axios | Proxy Config | Medium |
| `customer/service/testEndpointStatusMessage.mjs` | axios | Proxy Config | Medium |
| `device/service/test/simulateDeviceMessage.mjs` | axios | Proxy Config | Medium |
| `withings/service/activateUser.mjs` | node-fetch | Proxy Config | High |
| `withings/service/getNonceToken.mjs` | node-fetch | Proxy Config | High |
| `withings/service/requestAccessToken.mjs` | node-fetch | Proxy Config | High |
| `withings/service/getMeas.mjs` | node-fetch | Proxy Config | High |
| `patient-assistant/assistant.mjs` | OpenAI SDK | Proxy Config | Medium |
| `deviceUpdates/service/sendText.mjs` | Twilio SDK | Proxy Config | Medium |
| `users/service/sendText.mjs` | Twilio SDK | Proxy Config | Medium |
| `users/service/sendEmail.mjs` | SendGrid SDK | Proxy Config | Medium |

### 1. Package Dependencies
**File: `cardiowell-backend/package.json`**
```json
{
  "dependencies": {
    "https-proxy-agent": "^7.0.2",
    // ... existing dependencies
  }
}
```

### 2. Proxy Configuration Utility
**File: `cardiowell-backend/utils/proxyConfig.mjs`** (NEW FILE)
```javascript
import { HttpsProxyAgent } from 'https-proxy-agent'

const proxy = process.env.QUOTAGUARDSTATIC_URL
export const proxyAgent = proxy ? new HttpsProxyAgent(proxy) : undefined

// For axios
export const axiosProxyConfig = proxy ? { httpsAgent: proxyAgent } : {}

// For node-fetch
export const fetchProxyConfig = proxy ? { agent: proxyAgent } : {}

// Logging for debugging
if (proxy) {
  console.log('QuotaGuard Static IP proxy configured')
} else {
  console.log('No proxy configured - using default routing')
}
```

### 3. High-Priority Axios Updates

#### File: `device/service/forwardMessage.mjs`
```javascript
import axios from "axios"
import { axiosProxyConfig } from "../../utils/proxyConfig.mjs"

export const forwardMessage = async ({ message, endpoint }) => {
  try {
    const needAuthentication = Boolean(endpoint.keyName && endpoint.keyValue)

    const response = await axios.post(endpoint.url, message, {
      validateStatus: () => true,
      headers: needAuthentication
        ? {
            [endpoint.keyName]: endpoint.keyValue,
          }
        : undefined,
      ...axiosProxyConfig  // Add proxy configuration
    })
    
    // ... rest of function unchanged
  } catch (err) {
    // ... error handling unchanged
  }
}
```

#### File: `controller/Iglucose.js`
```javascript
const axios = require("axios")
const { axiosProxyConfig } = require("../utils/proxyConfig.mjs")

exports.getGlucoseData = async () => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await axios({
        url: "https://api.iglucose.com/readings/",
        method: "post",
        data: {
          api_key: "EA452207-9A77-42A2-8A75-0C454E4CFE2E-1616420479",
          device_ids: ["9999996"],
          reading_type: ["blood_glucose"],
        },
        ...axiosProxyConfig  // Add proxy configuration
      })
      // ... rest of function unchanged
    } catch (e) {
      // ... error handling unchanged
    }
  })
}
```

#### File: `customer/service/testEndpointTelemetryMessage.mjs`
```javascript
import axios from "axios"
import { generateSphygmomanometerTelemetry } from "../../device/service/test/generateSphygmomanometerTelemetry.mjs"
import { axiosProxyConfig } from "../../utils/proxyConfig.mjs"

export const testEndpointTelemetryMessage = async ({ endpoint }) => {
  const requestData = generateSphygmomanometerTelemetry()

  const headers = {
    [endpoint.keyName]: endpoint.keyValue,
  }

  const response = await axios.post(endpoint.url, requestData, {
    validateStatus: () => true,
    headers,
    ...axiosProxyConfig  // Add proxy configuration
  })

  // ... rest of function unchanged
}
```

#### File: `customer/service/testEndpointStatusMessage.mjs`
```javascript
import axios from "axios"
import { generateSphygmomanometerStatus } from "../../device/service/test/generateSphygmomanometerStatus.mjs"
import { axiosProxyConfig } from "../../utils/proxyConfig.mjs"

export const testEndpointStatusMessage = async ({ endpoint }) => {
  const requestData = generateSphygmomanometerStatus()

  const headers = {
    [endpoint.keyName]: endpoint.keyValue,
  }

  const response = await axios.post(endpoint.url, requestData, {
    validateStatus: () => true,
    headers,
    ...axiosProxyConfig  // Add proxy configuration
  })

  // ... rest of function unchanged
}
```

#### File: `device/service/test/simulateDeviceMessage.mjs`
```javascript
import axios from "axios"
import { axiosProxyConfig } from "../../utils/proxyConfig.mjs"
// ... other imports

export const simulateDeviceMessage = async device => {
  if (device.type === "bloodPressure" && device.manufacturer === "transtek") {
    const statusMessage = generateSphygmomanometerStatus(device)

    try {
      await axios.post(TEST_RECEIVING_ENDPOINT, statusMessage, {
        headers: {
          authorization: TEST_RECEIVING_API_TOKEN,
        },
        ...axiosProxyConfig  // Add proxy configuration
      })
    } catch (err) {
      console.error(err.cause)
    }
  }

  if (device.type === "bloodPressure" && device.manufacturer === "bodyTrace") {
    const telemetryMessage = generateBodyTraceTelemetry(device)

    try {
      await axios.post(TEST_BODY_TRACE_RECEIVING_ENDPOINT, telemetryMessage, {
        headers: {
          authorization: TEST_BODY_TRACE_RECEIVING_API_TOKEN,
        },
        ...axiosProxyConfig  // Add proxy configuration
      })
    } catch (err) {
      console.error(err.cause)
    }
  }

  if (device.type === "glucometer" && device.manufacturer === "transtek") {
    const telemetryMessage = generateTranstekBloodGlucoseMeterTelemetry(device)
    const statusMessage = generateTranstekBloodGlucoseMeterStatus(device)
    const heartbeatMessage = generateTranstekBloodGlucoseMeterHeartbeat(device)

    const messages = [telemetryMessage, statusMessage, heartbeatMessage]

    try {
      for (const message of messages) {
        await axios.post(TEST_RECEIVING_ENDPOINT, message, {
          headers: {
            authorization: TEST_RECEIVING_API_TOKEN,
          },
          ...axiosProxyConfig  // Add proxy configuration
        })
      }
    } catch (err) {
      console.error(err.cause)
    }
  }

  if (device.type === "oximeter" && device.manufacturer === "berryMed") {
    const message = generateBerryTelemetry(device)

    try {
      await axios.post(TEST_BERRY_RECEIVING_ENDPOINT, message, {
        headers: {
          authorization: TEST_BERRY_RECEIVING_API_TOKEN,
        },
        ...axiosProxyConfig  // Add proxy configuration
      })
    } catch (err) {
      console.error(err)
    }
  }

  // ... rest for other device types with axiosProxyConfig
}
```

### 4. High-Priority Fetch Updates

#### File: `withings/service/activateUser.mjs`
```javascript
import { getNonceToken } from "./getNonceToken.mjs"
import { fetchProxyConfig } from "../../utils/proxyConfig.mjs"

export const activateUser = async (clientId, clientSecret, body) => {
  const nonce = await getNonceToken(clientId, clientSecret)
  
  // ... parameter setup unchanged

  return fetch("https://wbsapi.us.withingsmed.net/v2/user", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(postParams),
    ...fetchProxyConfig  // Add proxy configuration
  })
    .then(response => {
      if (response.status === 200) {
        return response.json()
      }
      throw new Error(response.status)
    })
    .then(data => {
      // ... rest unchanged
    })
    .catch(err => {
      console.error(err)
      return { status: 500, error: err }
    })
}
```

#### File: `withings/service/getNonceToken.mjs`
```javascript
import { generateSignature } from "./generateSignature.mjs"
import { fetchProxyConfig } from "../../utils/proxyConfig.mjs"

export const getNonceToken = async (clientId, clientSecret) => {
  const timestamp = Math.floor(Date.now() / 1000)

  const postParams = new URLSearchParams({
    action: "getnonce",
    client_id: clientId,
    timestamp,
    signature: generateSignature("getnonce", clientId, clientSecret, `${timestamp}`),
  })

  const nonce = fetch("https://wbsapi.us.withingsmed.net/v2/signature", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: postParams,
    ...fetchProxyConfig  // Add proxy configuration
  })
    .then(response => {
      if (response.status == 200) {
        return response.json()
      }
      return {}
    })
    .then(data => {
      try {
        if (data["status"] == 0 && data["body"]) {
          return data["body"]["nonce"]
        }
        return ""
      } catch (e) {
        console.error(e)
        return ""
      }
    })
    .catch(err => {
      console.error(err)
      return ""
    })
  return nonce
}
```

#### File: `withings/service/requestAccessToken.mjs`
```javascript
import { generateSignature } from "./generateSignature.mjs"
import { getNonceToken } from "./getNonceToken.mjs"
import { fetchProxyConfig } from "../../utils/proxyConfig.mjs"

export const requestAccessToken = async ({
  authCode,
  clientId,
  clientSecret,
  redirectUri,
}) => {
  const nonce = await getNonceToken(clientId, clientSecret)

  if (!nonce) {
    return {
      status: 500,
      error: "Error retrieving nonce",
    }
  }

  const postParams = new URLSearchParams({
    action: "requesttoken",
    client_id: clientId,
    nonce,
    signature: generateSignature("requesttoken", clientId, clientSecret, nonce),
    grant_type: "authorization_code",
    code: authCode,
    redirect_uri: redirectUri,
  })

  const currentDate = Math.floor(Date.now() / 1000)

  const tokens = fetch("https://wbsapi.us.withingsmed.net/v2/oauth2", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: postParams,
    ...fetchProxyConfig  // Add proxy configuration
  })
    .then(response => {
      if (response.status == 200) {
        return response.json()
      }
      return {}
    })
    .then(data => {
      if (data["status"] == 0 && data["body"]) {
        return {
          status: data["status"],
          userId: data["body"]["userid"],
          accessToken: data["body"]["access_token"],
          refreshToken: data["body"]["refresh_token"],
          expireDate: currentDate + data["body"]["expires_in"],
          scope: data["body"]["scope"],
          csrfToken: data["body"]["csrf_token"],
          tokenType: data["body"]["token_type"],
        }
      } else {
        console.error("error requesting access tokens:", data)
      }

      return { status: data["status"], error: data["error"] }
    })
    .catch(err => {
      console.error(err)
      return { status: 500, error: err }
    })

  return tokens
}
```

#### File: `withings/service/getMeas.mjs`
```javascript
import { bpmAppli, bpmMeasTypes } from "../utils/categories.mjs"
import { fetchProxyConfig } from "../../utils/proxyConfig.mjs"

export const getMeas = async ({
  accessToken,
  appli,
  startDate,
  endDate,
  offset = undefined,
}) => {
  const measTypeArray = appli === bpmAppli ? bpmMeasTypes : []

  const postParams = new URLSearchParams({
    action: "getmeas",
    meastypes: measTypeArray,
    category: 1,
    startdate: startDate,
    enddate: endDate,
    ...(offset && { offset }),
  })

  let bodyArray = []
  const data = await fetch("https://wbsapi.us.withingsmed.net/measure", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization: `Bearer ${accessToken}`,
    },
    body: postParams,
    ...fetchProxyConfig  // Add proxy configuration
  })
    .then(response => {
      if (response.status == 200) {
        return response.json()
      }
      return {}
    })
    .catch(err => {
      console.error(err)
      return {}
    })

  console.log("getMeas data:", JSON.stringify(data))
  if (data["status"] == 0 && data["body"]) {
    const body = data["body"]
    bodyArray.push(body)
    if (body["more"] && body["more"] == 1 && body["offset"]) {
      const nextRow = await getMeas({
        accessToken,
        appli,
        startDate,
        endDate,
        offset: body["offset"],
      })
      bodyArray.push(...nextRow)
    }
  }

  return bodyArray
}
```

### 5. Third-party SDK Configuration

#### OpenAI Configuration
**File: `patient-assistant/assistant.mjs`**
```javascript
import OpenAI from "openai"
import { proxyAgent } from "../utils/proxyConfig.mjs"
// ... other imports

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  ...(proxyAgent && {
    httpAgent: proxyAgent,
    httpsAgent: proxyAgent,
  })
})

// ... rest of file unchanged
```

#### Twilio Configuration
**File: `deviceUpdates/service/sendText.mjs`**
```javascript
import twilio from "twilio"
import { phoneContact, textToPatient } from "../../models/message.mjs"
import { proxyAgent } from "../../utils/proxyConfig.mjs"

const accountSid = process.env.twilioSID
const authToken = process.env.twilioToken

const client = twilio(accountSid, authToken, {
  ...(proxyAgent && {
    httpAgent: proxyAgent,
    httpsAgent: proxyAgent,
  })
})

// ... rest of file unchanged
```

**File: `users/service/sendText.mjs`**
```javascript
import twilio from "twilio"
import { proxyAgent } from "../../utils/proxyConfig.mjs"

const accountSid = process.env.twilioSID
const authToken = process.env.twilioToken

const client = twilio(accountSid, authToken, {
  ...(proxyAgent && {
    httpAgent: proxyAgent,
    httpsAgent: proxyAgent,
  })
})

// ... rest of file unchanged
```

#### SendGrid Configuration
**File: `users/service/sendEmail.mjs`**
```javascript
import sgMail from "@sendgrid/mail"
import { proxyAgent } from "../../utils/proxyConfig.mjs"

sgMail.setApiKey(process.env.sendgridAPI)

// Configure proxy if available
if (proxyAgent) {
  sgMail.setClient({
    request: {
      httpsAgent: proxyAgent,
    }
  })
}

// ... rest of file unchanged
```

### 6. Testing Endpoint
**File: `cardiowell-backend/routes/test.mjs`** (NEW FILE)
```javascript
import { Router } from "express"
import axios from "axios"
import { axiosProxyConfig } from "../utils/proxyConfig.mjs"

export const testRouter = Router()

// Test endpoint to verify static IP
testRouter.get("/ip-test", async (req, res) => {
  try {
    const response = await axios.get('https://api.ipify.org?format=json', axiosProxyConfig)
    res.json({ 
      ip: response.data.ip, 
      proxy: !!process.env.QUOTAGUARDSTATIC_URL,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// Test multiple services
testRouter.get("/services-test", async (req, res) => {
  const results = {}
  
  try {
    // Test different endpoints
    const ipResult = await axios.get('https://api.ipify.org?format=json', axiosProxyConfig)
    results.ipService = { ip: ipResult.data.ip, status: 'success' }
  } catch (error) {
    results.ipService = { status: 'error', message: error.message }
  }

  res.json(results)
})
```

**Add to `app.mjs`:**
```javascript
import { testRouter } from "./routes/test.mjs"

// Add after other routes (development only)
if (process.env.NODE_ENV === "development") {
  app.use("/routes/test", testRouter)
}
```

## Environment Configuration

### Environment Variables

#### Required Variables
| Variable | Purpose | Source | Example |
|----------|---------|--------|---------|
| `QUOTAGUARDSTATIC_URL` | QuotaGuard proxy URL with credentials | Heroku Config Vars (auto-set) | `http://username:<EMAIL>:9293` |

#### Optional Variables
| Variable | Purpose | Default | Example |
|----------|---------|---------|---------|
| `PROXY_DEBUG` | Enable proxy debugging logs | `false` | `true` |

### Heroku Configuration

#### Add QuotaGuard Static Add-on
```bash
# Via Heroku CLI
heroku addons:create quotaguardstatic:starter --app your-app-name

# Via Heroku Dashboard
# 1. Go to Resources tab
# 2. Search for "QuotaGuard Static"
# 3. Select "Starter" plan
# 4. Click "Submit Order Form"
```

#### Verify Configuration
```bash
# Check config vars
heroku config --app your-app-name

# Should show:
# QUOTAGUARDSTATIC_URL: http://username:<EMAIL>:9293
```

### Local Development Configuration

#### .env File (for local testing)
```bash
# Add to .env file (DO NOT COMMIT)
QUOTAGUARDSTATIC_URL=http://username:<EMAIL>:9293
```

**Note**: For local development, you can use the same QuotaGuard credentials, but this is optional since the proxy is primarily needed for production IP whitelisting.

## Testing & Verification

### 1. Pre-deployment Testing

#### Install Dependencies
```bash
cd cardiowell-backend
yarn add https-proxy-agent
```

#### Local Testing (Optional)
```bash
# Test with proxy (if you have QuotaGuard credentials)
QUOTAGUARDSTATIC_URL=http://user:<EMAIL>:9293 \
  yarn start
```

### 2. Production Verification

#### Test Static IP
```bash
# Test IP endpoint
curl https://cardiowell.herokuapp.com/routes/test/ip-test

# Expected response:
{
  "ip": "XXX.XXX.XXX.XXX",  # Should be QuotaGuard static IP
  "proxy": true,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

#### Monitor Integration Health
```bash
# Monitor logs for proxy usage and errors
heroku logs --tail --app cardiowell | grep -i "proxy\|error"
```

### 3. Test Critical Services

#### iGlucose Integration
- Monitor glucose data retrieval from cronJobs
- Check logs for successful API calls

#### Withings Integration  
- Test blood pressure device connections
- Verify measurement data retrieval

#### Customer Data Forwarding
- Test endpoint forwarding functionality
- Verify data reaches customer systems

#### Communication Services
- Test SMS delivery via Twilio
- Test email delivery via SendGrid

## Deployment Instructions

### Pre-deployment Checklist
- [ ] QuotaGuard Static add-on installed
- [ ] `QUOTAGUARDSTATIC_URL` environment variable confirmed
- [ ] `https-proxy-agent` dependency added
- [ ] All code changes implemented
- [ ] Testing completed

### Deployment Steps

#### 1. Prepare Code Changes
```bash
cd cardiowell-backend

# Create proxy configuration utility
mkdir -p utils
# (Implement utils/proxyConfig.mjs file)

# Install new dependency
yarn add https-proxy-agent

# Update all affected files with proxy configuration
# (Follow the code examples above)

# Commit changes
git add .
git commit -m "feat: implement QuotaGuard Static IP proxy configuration

- Add https-proxy-agent dependency
- Create centralized proxy configuration utility
- Update axios calls to use proxy agent
- Update fetch calls to use proxy agent
- Configure third-party SDKs for proxy usage
- Add IP testing endpoints for verification"
```

#### 2. Deploy to Production
```bash
# Deploy to Heroku
git push heroku main

# Monitor deployment
heroku logs --tail --app cardiowell
```

#### 3. Post-deployment Verification
```bash
# Verify static IP usage
curl https://cardiowell.herokuapp.com/routes/test/ip-test

# Test critical integrations
# - iGlucose data retrieval should work
# - Withings device connections should work
# - Customer data forwarding should work
# - Email/SMS services should work
```

#### 4. External Service Configuration
- **Update IP Whitelists**: Add QuotaGuard static IP to external service configurations
- **Notify Integration Partners**: Inform customers/partners of new static IP
- **Update Documentation**: Record static IP in relevant system documentation

### Rollback Plan

#### If Issues Occur
```bash
# Option 1: Rollback to previous version
heroku rollback --app cardiowell

# Option 2: Disable proxy temporarily
heroku config:unset QUOTAGUARDSTATIC_URL --app cardiowell
# Application will fall back to direct connections

# Option 3: Debug mode
heroku config:set PROXY_DEBUG=true --app cardiowell
```

## Troubleshooting

### Common Issues

#### 1. Proxy Connection Failures
**Symptoms**: HTTP requests timing out or failing

**Causes**: 
- Invalid proxy URL
- Network connectivity issues
- Proxy service downtime

**Solutions**:
```bash
# Verify proxy URL format
heroku config:get QUOTAGUARDSTATIC_URL --app cardiowell

# Check QuotaGuard service status
curl -I http://status.quotaguard.com

# Test direct connection bypass
heroku config:unset QUOTAGUARDSTATIC_URL --app cardiowell
```

#### 2. SSL/TLS Certificate Issues
**Symptoms**: SSL certificate errors in logs

**Causes**: 
- Proxy SSL configuration
- Certificate validation failures

**Solutions**:
```javascript
// In proxyConfig.mjs, add SSL options if needed
export const axiosProxyConfig = proxy ? { 
  httpsAgent: new HttpsProxyAgent(proxy, {
    rejectUnauthorized: true  // Keep true for security
  })
} : {}
```

#### 3. Authentication Problems
**Symptoms**: 401/403 errors from external services

**Causes**:
- Changed IP address not whitelisted
- Proxy authentication issues

**Solutions**:
- Verify static IP with external service providers
- Check proxy credentials in URL
- Confirm service account permissions

#### 4. Performance Issues
**Symptoms**: Slower response times

**Causes**:
- Additional proxy hop latency
- Proxy server load

**Solutions**:
- Monitor response times before/after implementation
- Consider upgrading QuotaGuard plan if needed
- Implement request timeout adjustments

### Debug Techniques

#### Enable Verbose Logging
```javascript
// In proxyConfig.mjs
const proxy = process.env.QUOTAGUARDSTATIC_URL
if (proxy && process.env.PROXY_DEBUG === 'true') {
  console.log('Proxy configuration:', {
    url: proxy.replace(/:([^:@]+)@/, ':***@'), // Hide password
    timestamp: new Date().toISOString()
  })
}
```

#### Monitor Network Traffic
```bash
# Monitor application logs
heroku logs --tail --source app --app cardiowell

# Monitor for specific errors
heroku logs --tail --app cardiowell | grep -i "ECONNRESET\|ETIMEDOUT\|proxy"
```

#### Test Individual Services
```bash
# Test specific integrations individually
curl -X POST https://cardiowell.herokuapp.com/routes/test/services-test
```

### Performance Monitoring

#### Key Metrics to Track
1. **Response Times**: Before vs. after proxy implementation
2. **Error Rates**: HTTP errors from external services
3. **Throughput**: Requests per second for critical endpoints
4. **Availability**: Service uptime for external integrations

#### Alerting Setup
Configure alerts for:
- Increased error rates (>5% for external API calls)
- Response time degradation (>2x baseline)
- Proxy service failures

## Cost Considerations

### QuotaGuard Static Pricing

#### Starter Plan (Recommended)
- **Cost**: $9/month
- **Included**: 1 static IP
- **Bandwidth**: 20GB/month
- **Concurrent Connections**: 5
- **Suitable For**: CardioWell's current needs

#### Professional Plan
- **Cost**: $49/month
- **Included**: 2 static IPs
- **Bandwidth**: 100GB/month
- **Concurrent Connections**: 25
- **Suitable For**: High-traffic scenarios

#### Enterprise Plan
- **Cost**: Custom pricing
- **Included**: Multiple static IPs
- **Bandwidth**: Unlimited
- **Concurrent Connections**: Unlimited
- **Suitable For**: Large-scale enterprise applications

### Cost-Benefit Analysis

#### Current Issues Cost
- **Development Time**: Debugging IP-related integration failures
- **Customer Support**: Handling integration problems
- **Business Impact**: Failed data exchanges, missed transactions

#### Implementation Cost
- **Initial Setup**: ~4-8 hours development time
- **Monthly Service**: $9/month for Starter plan
- **Maintenance**: Minimal ongoing maintenance

#### ROI Calculation
- **Reduced Support Tickets**: Estimated 2-4 hours/month saved
- **Improved Reliability**: 99.9% vs 95% uptime for external integrations
- **Customer Satisfaction**: Reduced integration failures

### Budget Planning

#### Year 1 Costs
- **Setup**: One-time development cost
- **Service**: $108/year (Starter plan)
- **Monitoring**: Included in existing infrastructure

#### Scaling Considerations
- Monitor bandwidth usage monthly
- Upgrade plan if exceeding limits
- Consider multiple IPs for geographical distribution

## Summary

### Implementation Overview

This QuotaGuard Static IP implementation provides:

1. **Fixed IP Addresses**: All outbound HTTP requests use a static IP
2. **Minimal Code Changes**: Centralized proxy configuration with targeted updates
3. **Backward Compatibility**: Graceful fallback when proxy is not configured
4. **Easy Testing**: Built-in endpoints for verification
5. **Simple Deployment**: Heroku add-on integration with automatic configuration

### Files Summary

**New Files Created (2):**
- `utils/proxyConfig.mjs` - Centralized proxy configuration
- `routes/test.mjs` - IP testing endpoints

**Files Modified (15):**
- `package.json` - Add https-proxy-agent dependency
- 5 axios-based files - Add proxy configuration
- 4 fetch-based files - Add proxy configuration  
- 3 Twilio service files - Add proxy configuration
- 1 SendGrid service file - Add proxy configuration
- 1 OpenAI service file - Add proxy configuration
- `app.mjs` - Add test routes for development

### Next Steps

1. **Immediate Actions**
   - Add QuotaGuard Static add-on to Heroku
   - Implement code changes following this guide
   - Deploy and test functionality

2. **Post-Implementation**
   - Update external service IP whitelists with QuotaGuard static IP
   - Monitor performance and reliability metrics
   - Document static IP for future integrations

3. **Long-term Monitoring**
   - Track bandwidth usage for plan optimization
   - Monitor error rates and response times
   - Evaluate integration reliability improvements

### Success Criteria

- [ ] All external API calls route through static IP
- [ ] No degradation in application performance
- [ ] External service integrations remain stable
- [ ] IP whitelisting requirements satisfied
- [ ] Monitoring and alerting in place
- [ ] Documentation updated with static IP information

This implementation ensures CardioWell can reliably integrate with external services requiring IP whitelisting while maintaining application performance and security. The centralized proxy configuration approach makes it easy to manage and troubleshoot all external HTTP requests.