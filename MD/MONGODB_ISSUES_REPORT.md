# MongoDB Issues Report - CardioWell Project

**Report Date**: January 2025  
**Project**: CardioWell Backend  
**Severity**: Critical Performance Issues Identified

## Executive Summary

This report addresses two MongoDB-related issues affecting the CardioWell project:
1. **MongoDB BSON Security Vulnerability** - NOT AFFECTING THIS PROJECT
2. **MongoDB Performance Issues** - CRITICAL - REQUIRES IMMEDIATE ATTENTION

The performance issues are causing significant database inefficiencies, with query targeting ratios exceeding acceptable thresholds. Immediate action is required to implement database indexes and optimize query patterns to ensure optimal system performance.

---

## Issue #1: MongoDB BSON Security Vulnerability

### Status: NOT AFFECTED

### Description
MongoDB issued a security alert regarding the `useBigInt64` option in the BSON JavaScript library, affecting negative Int64 value deserialization.

### Impact Assessment
**This project is NOT affected** because:
- **Safe versions in use**: MongoDB driver `3.3.3` and Mongoose `5.9.10`
- **Vulnerable versions**: MongoDB Node.js driver 6.0.0 - 6.13.0, Mongoose 8.3.5 - 8.10.1
- **No usage of `useBigInt64`**: Codebase search confirmed no explicit usage
- **Default behavior**: `useBigInt64` is disabled by default

### Action Required
**No action needed** - continue monitoring for future updates

---

## Issue #2: MongoDB Performance Problems

### Status: CRITICAL - IMMEDIATE ACTION REQUIRED

### Description
MongoDB Atlas alerts indicate "Query Targeting: Scanned Objects / Returned has gone above 1000", suggesting inefficient queries that scan far more documents than they return. This represents a critical performance bottleneck affecting user experience and system scalability.

### Root Causes Identified

#### 1. Missing Database Indexes
Analysis of the codebase reveals specific collections lacking proper indexes for frequently queried fields:

**VERIFIED MISSING INDEXES:**
```javascript
// Measurement Collections (High Priority - Heavy Query Load)
- PulseOximeterData: queries on `imei` and `time` without indexes
- CellularBloodGlucoseData: queries on `imei` and `ts` without indexes
- Transtek_WS: queries on `imei` and `ts` without indexes
- BT_WS: queries on `imei` and `ts` without indexes (if this collection exists)
- Withings_BPM: queries on `deviceId` and `created` without indexes
- Withings_User_Data: queries on `patientId` and `userId` without indexes

// Core Application Collections (Medium Priority)
- TestDataUploads: queries on `imei`, `deviceId`, `deleted` without indexes
- ClinicalNote: queries on `patientId` and `providerId` without indexes
- PatientProfileTime: queries on `providerId`, `patientId`, `ts` without indexes
- TestThreshold: no indexes defined
- testprogram: queries on `clinicId` without indexes
```

**EXISTING INDEXES (Already Implemented):**
```javascript
// These indexes are already in the codebase:
CONFIRMED: MessageSchema.index({ "user.userId": 1, "user.userType": 1, createdAt: -1 })
CONFIRMED: PatientAssistantThreadSchema.index({ "user.userId": 1, "user.userType": 1 }, { unique: true })
CONFIRMED: Device unique indexes on `imei` and `deviceId`
CONFIRMED: ShortUrl unique index on `shortToken`
CONFIRMED: Message unique index on `messageId`
```

**NOTE:** Collections like BT_BPM, Transtek_BPM, ad_bpms, and bodytracemessages mentioned in the original report may already have indexes created directly in MongoDB Atlas, which is why they weren't flagged as critical issues during this analysis.

#### 2. Complex Aggregation Pipelines
Heavy aggregation operations with multiple `$lookup` stages in:
- `users/service/lookupPatientOverview.mjs`
- `users/service/bodytrace/` files
- `device/service/getDevices.mjs`
- `customer/controller/getCustomers.mjs`

#### 3. Inefficient Query Patterns
- Sorting without compound indexes
- Full collection scans on unindexed fields
- Multiple nested lookups without optimization

### Performance Impact
- **High CPU usage** during database operations
- **Slower response times** for patient data retrieval
- **Increased memory consumption** 
- **Poor user experience** in web and mobile applications
- **Scalability concerns** for future growth

---

## Solutions & Implementation Plan

### IMMEDIATE ACTIONS (Priority 1)

#### 1. Add Critical Database Indexes

**Execute these commands in MongoDB Atlas or your MongoDB instance:**

```javascript
// HIGH PRIORITY - Measurement Collections (Heavy Query Load)
// Pulse Oximeter Collection - MISSING INDEXES
db.PulseOximeterData.createIndex({ "imei": 1, "time": -1 });
db.PulseOximeterData.createIndex({ "imei": 1, "isTest": 1, "time": -1 });

// Cellular Blood Glucose Data - MISSING INDEXES
db.CellularBloodGlucoseData.createIndex({ "imei": 1, "ts": -1 });
db.CellularBloodGlucoseData.createIndex({ "imei": 1, "isTest": 1, "ts": -1 });

// Transtek Weight Scale - MISSING INDEXES
db.Transtek_WS.createIndex({ "imei": 1, "ts": -1 });
db.Transtek_WS.createIndex({ "imei": 1, "isTest": 1, "ts": -1 });

// Withings Blood Pressure - MISSING INDEXES
db.withings_bpms.createIndex({ "deviceId": 1, "created": -1 });
db.withings_bpms.createIndex({ "deviceId": 1, "isTest": 1, "created": -1 });

// Withings User Data - MISSING INDEXES
db.withings_user_datas.createIndex({ "patientId": 1 });
db.withings_user_datas.createIndex({ "userId": 1 });

// MEDIUM PRIORITY - Core Application Collections
// Test Data Uploads - MISSING INDEXES
db.testdatauploads.createIndex({ "imei": 1 });
db.testdatauploads.createIndex({ "deviceId": 1 });
db.testdatauploads.createIndex({ "deleted": 1 });
db.testdatauploads.createIndex({ "sessionId": 1 });

// Clinical Notes - MISSING INDEXES
db.clinicalnotes.createIndex({ "patientId": 1, "createdAt": -1 });
db.clinicalnotes.createIndex({ "providerId": 1, "createdAt": -1 });

// Patient Profile Times - MISSING INDEXES
db.patientprofiletimes.createIndex({ "providerId": 1, "patientId": 1, "ts": -1 });
db.patientprofiletimes.createIndex({ "patientId": 1, "ts": -1 });

// Threshold Collection - MISSING INDEXES
db.testthresholds.createIndex({ "_id": 1 }); // May already exist as primary key
db.testthresholds.createIndex({ "createdAt": -1 });

// Program Collection - MISSING INDEXES
db.testprograms.createIndex({ "clinicId": 1 });
db.testprograms.createIndex({ "thresholdId": 1 });

// EXISTING COLLECTIONS - Verify these are already indexed
// Core Application Collections - Check if these indexes exist, create if missing:
db.devices.createIndex({ "customer": 1 });
db.devices.createIndex({ "clinic": 1 });
db.devices.createIndex({ "type": 1 });
db.devices.createIndex({ "createdAt": -1 });

db.patients.createIndex({ "bpIMEI": 1 });
db.patients.createIndex({ "ttBpIMEI": 1 });
db.patients.createIndex({ "adBpIMEI": 1 });
db.patients.createIndex({ "weightIMEI": 1 });
db.patients.createIndex({ "ttWeightIMEI": 1 });
db.patients.createIndex({ "glucoseIMEI": 1 });
db.patients.createIndex({ "pulseIMEI": 1 });
db.patients.createIndex({ "clinic": 1 });
db.patients.createIndex({ "email": 1 });
db.patients.createIndex({ "username": 1 });

db.customers.createIndex({ "name": 1 });
db.customers.createIndex({ "clinics": 1 });
db.customers.createIndex({ "createdAt": -1 });

// Weight Scale Collections - Check if indexes exist for BT_WS (collection may not exist in all environments)
// Only run if the collection exists:
// db.BT_WS.createIndex({ "imei": 1, "ts": -1 });
// db.BT_WS.createIndex({ "imei": 1, "isTest": 1, "ts": -1 });

// NOTE: The following collections likely already have indexes in Atlas:
// - messages (already has compound index in code)
// - patient-assistant-threads (already has compound unique index in code)
// - BT_BPM, Transtek_BPM, ad_bpms, bodytracemessages (user reported these indexes exist)
```

#### 2. Update Mongoose Models with Schema Indexes

**PRIORITY 1 - Add missing indexes to measurement models:**

**File: `cardiowell-backend/models/pulseOximeter.mjs`**
```javascript
// Add these indexes to PulseOximeterDataSchema (MISSING)
PulseOximeterDataSchema.index({ imei: 1, time: -1 });
PulseOximeterDataSchema.index({ imei: 1, isTest: 1, time: -1 });
```

**File: `cardiowell-backend/models/cellularBloodGlucoseData.js`**
```javascript
// Add these indexes to cellularBloodGlucoseSchema (MISSING)
cellularBloodGlucoseSchema.index({ imei: 1, ts: -1 });
cellularBloodGlucoseSchema.index({ imei: 1, isTest: 1, ts: -1 });
```

**File: `cardiowell-backend/models/transtekWeightScaleMessage.mjs`**
```javascript
// Add these indexes to TranstekWeightScaleMessageSchema (MISSING)
TranstekWeightScaleMessageSchema.index({ imei: 1, ts: -1 });
TranstekWeightScaleMessageSchema.index({ imei: 1, isTest: 1, ts: -1 });
```

**File: `cardiowell-backend/models/withingsBloodPressureData.js`**
```javascript
// Add these indexes to withingsBloodPressureSchema (MISSING)
withingsBloodPressureSchema.index({ deviceId: 1, created: -1 });
withingsBloodPressureSchema.index({ deviceId: 1, isTest: 1, created: -1 });
```

**File: `cardiowell-backend/models/withingsUserData.js`**
```javascript
// Add these indexes to userDataSchema (MISSING)
userDataSchema.index({ patientId: 1 });
userDataSchema.index({ userId: 1 });
```

**PRIORITY 2 - Add missing indexes to core application models:**

**File: `cardiowell-backend/models/testDataUploads.mjs`**
```javascript
// Add these indexes to TestDataUploadsSchema (MISSING)
TestDataUploadsSchema.index({ imei: 1 });
TestDataUploadsSchema.index({ deviceId: 1 });
TestDataUploadsSchema.index({ deleted: 1 });
TestDataUploadsSchema.index({ sessionId: 1 });
```

**File: `cardiowell-backend/models/clinicalNote.mjs`**
```javascript
// Add these indexes to ClinicalNoteSchema (MISSING)
ClinicalNoteSchema.index({ patientId: 1, createdAt: -1 });
ClinicalNoteSchema.index({ providerId: 1, createdAt: -1 });
```

**File: `cardiowell-backend/models/patientProfileTime.mjs`**
```javascript
// Add these indexes to PatientProfileTimeSchema (MISSING)
PatientProfileTimeSchema.index({ providerId: 1, patientId: 1, ts: -1 });
PatientProfileTimeSchema.index({ patientId: 1, ts: -1 });
```

**File: `cardiowell-backend/models/threshold.mjs`**
```javascript
// Add these indexes to ThresholdSchema (MISSING)
ThresholdSchema.index({ createdAt: -1 });
```

**File: `cardiowell-backend/models/program.mjs`**
```javascript
// Add these indexes to programSchema (MISSING)
programSchema.index({ clinicId: 1 });
programSchema.index({ thresholdId: 1 });
```

**PRIORITY 3 - Optional additions to existing models:**

**File: `cardiowell-backend/models/device.mjs`**
```javascript
// Add these additional indexes to DeviceSchema (imei and deviceId already have unique constraints)
DeviceSchema.index({ customer: 1 });
DeviceSchema.index({ clinic: 1 });
DeviceSchema.index({ type: 1 });
// createdAt index automatically created with timestamps: true
```

**File: `cardiowell-backend/models/patient.js`**
```javascript
// Add these indexes to PatientSchema for IMEI lookups
PatientSchema.index({ bpIMEI: 1 });
PatientSchema.index({ ttBpIMEI: 1 });
PatientSchema.index({ adBpIMEI: 1 });
PatientSchema.index({ weightIMEI: 1 });
PatientSchema.index({ ttWeightIMEI: 1 });
PatientSchema.index({ glucoseIMEI: 1 });
PatientSchema.index({ pulseIMEI: 1 });
PatientSchema.index({ clinic: 1 });
PatientSchema.index({ email: 1 });
PatientSchema.index({ username: 1 });
```

**File: `cardiowell-backend/models/customer.mjs`**
```javascript
// Add these indexes to CustomerSchema
CustomerSchema.index({ name: 1 });
CustomerSchema.index({ clinics: 1 });
// createdAt index automatically created with timestamps: true
```

### MEDIUM-TERM IMPROVEMENTS (Priority 2)

#### 1. Optimize Aggregation Pipelines

**Target Files for Optimization:**
- `users/service/lookupPatientOverview.mjs`
- `users/service/bodytrace/*.mjs`
- `device/service/getDevices.mjs`

**Optimization Strategies:**
- Add `$limit` early in pipelines
- Use `$match` stages as early as possible
- Consider breaking complex aggregations into smaller queries
- Implement result caching for frequently accessed data

#### 2. Query Performance Monitoring

**Implement query performance logging:**
```javascript
// Add to mongoose connection
mongoose.set('debug', process.env.NODE_ENV === 'development');

// Monitor slow queries
db.setProfilingLevel(1, { slowms: 100 });
```

#### 3. Database Connection Optimization

**Update `cardiowell-backend/app.mjs`:**
```javascript
await mongoose.connect(process.env.mongoUri, {
  useUnifiedTopology: true,
  useNewUrlParser: true,
  useFindAndModify: false,
  maxPoolSize: 10, // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  bufferMaxEntries: 0 // Disable mongoose buffering
});
```

### LONG-TERM OPTIMIZATIONS (Priority 3)

#### 1. Data Architecture Review
- Consider data denormalization for frequently accessed fields
- Implement read replicas for reporting queries
- Evaluate MongoDB sharding for high-traffic collections

#### 2. Caching Strategy
- Implement Redis caching for frequently accessed patient data
- Cache aggregation results for dashboard queries
- Use MongoDB TTL indexes for temporary data

#### 3. Monitoring & Alerting
- Set up MongoDB Performance Advisor monitoring
- Implement application-level query performance tracking
- Create dashboards for database performance metrics

---

## Implementation Timeline

### Week 1: Critical Fixes (HIGH PRIORITY)
- Execute HIGH PRIORITY database index creation commands
  - PulseOximeterData indexes (imei, time, isTest)
  - CellularBloodGlucoseData indexes (imei, ts, isTest)
  - Transtek_WS indexes (imei, ts, isTest)
  - Withings_BPM indexes (deviceId, created, isTest)
  - Withings_User_Data indexes (patientId, userId)
- Update Priority 1 Mongoose schemas with indexes
  - pulseOximeter.mjs
  - cellularBloodGlucoseData.js
  - transtekWeightScaleMessage.mjs
  - withingsBloodPressureData.js
  - withingsUserData.js
- Deploy changes to staging environment
- Monitor immediate performance improvements

### Week 2: Medium Priority & Validation
- Execute MEDIUM PRIORITY database index creation commands
  - TestDataUploads indexes
  - ClinicalNote indexes
  - PatientProfileTime indexes
  - TestThreshold and Program indexes
- Update Priority 2 Mongoose schemas
  - testDataUploads.mjs
  - clinicalNote.mjs
  - patientProfileTime.mjs
  - threshold.mjs and program.mjs
- Validate query performance improvements on staging
- Deploy to production environment
- Monitor MongoDB Atlas alerts
- Document performance baseline

### Week 3: Optional Optimizations
- Add Priority 3 indexes if needed (based on performance monitoring)
  - Additional Device, Patient, Customer indexes
- Verify existing indexes are working 
  - Confirm BT_BPM, Transtek_BPM, ad_bpms, bodytracemessages indexes
- Performance testing and validation

### Week 4: Advanced Optimization
- Implement aggregation pipeline optimizations
- Add query performance monitoring
- Update database connection settings
- Long-term performance strategy implementation

---

## Success Metrics

### Expected Improvements
- **High Priority Collections (Week 1)**:
  - **PulseOximeterData queries**: 70-90% reduction in scan ratio for IMEI/time queries
  - **CellularBloodGlucoseData queries**: 70-90% reduction in scan ratio for IMEI/ts queries  
  - **Transtek_WS queries**: 70-90% reduction in scan ratio for IMEI/ts queries
  - **Withings queries**: 60-80% improvement in deviceId/userId lookups
- **Medium Priority Collections (Week 2)**:
  - **Clinical workflow queries**: 40-60% improvement in patient/provider lookups
  - **Test data management**: 50-70% improvement in IMEI-based test data queries
- **Overall System Performance**:
  - **Patient dashboard loading**: 30-50% improvement in response times
  - **Device data retrieval**: 40-70% improvement for measurement queries
  - **MongoDB Alerts**: Significant reduction in "Query Targeting" alerts
  - **Resource Usage**: 20-40% reduction in database CPU utilization

### Monitoring KPIs
- **Primary Metrics**:
  - Average query execution time for measurement collections
  - Documents scanned vs. documents returned ratio (target: < 100:1)
  - Database CPU utilization during peak hours
- **Application Metrics**:
  - Patient dashboard load times
  - Device measurement API response times
  - Aggregation pipeline execution times
- **User Experience Metrics**:
  - Page load times in React frontend
  - API error rates
  - System availability during high load

---

## Risk Assessment

### Low Risk Items
- Adding database indexes (non-breaking changes)
- Updating Mongoose schemas
- Connection pool optimization

### Medium Risk Items
- Aggregation pipeline modifications
- Query pattern changes
- Connection setting updates

### Mitigation Strategies
- Deploy changes incrementally
- Maintain rollback capability
- Monitor performance closely during implementation
- Test thoroughly in staging environment

---

## Contact & Support

**Technical Lead**: Development Team Lead  
**Database Administrator**: DBA Contact  
**MongoDB Support**: Available through Atlas support portal

**Next Review Date**: 2 weeks from implementation

---

## Appendix

### A. MongoDB Version Information
- **Current MongoDB Driver**: 3.3.3
- **Current Mongoose**: 5.9.10
- **Recommended Versions**: Latest stable versions
- **Atlas Cluster**: Production cluster details

### B. Related Documentation
- [MongoDB Index Best Practices](https://docs.mongodb.com/manual/indexes/)
- [Mongoose Schema Indexes](https://mongoosejs.com/docs/guide.html#indexes)
- [MongoDB Performance Tuning](https://docs.mongodb.com/manual/administration/analyzing-mongodb-performance/)

### C. Emergency Contacts
- **MongoDB Atlas Support**: Atlas support contact
- **Development Team**: Development team contact
- **Infrastructure Team**: Infrastructure team contact

---

**Report Status**: Complete  
**Last Updated**: January 2025  
**Version**: 1.1 