# Cardiowell Development Environment Deployment Guide

## Table of Contents
1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Environment Setup](#environment-setup)
4. [Step-by-Step Deployment Process](#step-by-step-deployment-process)
5. [Verification and Testing](#verification-and-testing)
6. [Troubleshooting](#troubleshooting)
7. [Rollback Procedures](#rollback-procedures)
8. [Development vs Production Differences](#development-vs-production-differences)

## Overview

This guide provides a complete walkthrough for deploying the Cardiowell application to a **development environment** on Heroku. The Cardiowell project uses a dual-repository architecture where the React frontend is built separately and then integrated with the Node.js backend for deployment.

### Architecture Summary
- **Frontend Repository**: `patientCareReact-master/` (React application)
- **Backend Repository**: `cardiowell-backend/` (Node.js/Express API + serves frontend)
- **Deployment Target**: Heroku development environment
- **Deployment Method**: Git-based with Node.js buildpack

## Prerequisites

### Required Tools
Before starting the deployment process, ensure you have the following tools installed:

```bash
# Check Node.js version (required: v20.13.0+)
node --version

# Check Yarn version (required: v1.22.22+)
yarn --version

# Check Git
git --version

# Install Heroku CLI if not already installed
# macOS
brew tap heroku/brew && brew install heroku

# Ubuntu/Debian
curl https://cli-assets.heroku.com/install-ubuntu.sh | sh

# Windows
# Download from https://devcenter.heroku.com/articles/heroku-cli

# Verify Heroku CLI installation
heroku --version
```

### Required Access and Permissions

1. **Git Repository Access**
   - Read/write access to both `cardiowell-backend` and `patientCareReact-master` repositories
   - SSH key configured for Git operations

2. **Heroku Access**
   - Heroku account with access to the development app
   - Collaborator permissions on the Heroku application

3. **Environment Variables**
   - Access to development environment configuration values
   - Database connection strings for development

### Verify Access
```bash
# Test Git access
<NAME_EMAIL>:Cardiowell/cardiowell-backend.git
<NAME_EMAIL>:Cardiowell/patientCareReact-master.git

# Test Heroku access
heroku auth:login
heroku apps --team your-team-name
```

## Environment Setup

### 1. Clone and Setup Repositories

```bash
# Create workspace directory
mkdir cardiowell-dev
cd cardiowell-dev

# Clone both repositories
<NAME_EMAIL>:Cardiowell/cardiowell-backend.git
<NAME_EMAIL>:Cardiowell/patientCareReact-master.git

# Setup backend
cd cardiowell-backend
yarn install
cd ..

# Setup frontend
cd patientCareReact-master
yarn install
cd ..
```

### 2. Configure Development Environment Variables

Create a `.env` file in the backend repository for local development:

```bash
cd cardiowell-backend
touch .env
```

Add the following development environment variables to `.env`:

```bash
# Core Application Settings
NODE_ENV=development
PORT=8081

# Database Configuration (Development)
mongoUri=mongodb://localhost:27017/cardiowell-dev
# OR use a development MongoDB Atlas instance
# mongoUri=mongodb+srv://dev-user:<EMAIL>/cardiowell-dev

# Authentication & Security (Development Keys)
MAGIC_LINK_JWT_SECRET=your-dev-jwt-secret-key
MAGIC_LINK_JWT_EXPIRATION=1h
clinicalNoteKey=your-32-byte-development-encryption-key

# Email Service (Development/Testing)
sendgridAPI=your-sendgrid-api-key-for-dev

# SMS Service (Development/Testing)
twilioSID=your-twilio-dev-sid
twilioToken=your-twilio-dev-token
twilioNumber=your-twilio-dev-number

# Device Integration APIs (Development Endpoints)
AD_API=https://dev-api.ad-medical.com
AD_USERNAME=dev-username
AD_PASSWORD=dev-password
AD_RECEIVING_ENDPOINT=https://your-dev-app.herokuapp.com/routes/ad

# BerryMed Integration
BERRY_RECEIVING_API_TOKEN=dev-berry-token

# BodyTrace Integration
BODY_TRACE_RECEIVING_API_KEY=dev-bodytrace-key
BODY_TRACE_RECEIVING_API_KEY_VALUE=dev-bodytrace-value

# Monitoring (Development)
SENTRY_DSN=your-sentry-dev-dsn
SERVICE_ENV=development

# AI Assistant (Development)
OPENAI_API_KEY=your-openai-dev-key
OPENAI_ASSISTANT_ID=your-dev-assistant-id

# Feature Flags
FEATURE_BP_BUDDY_ENABLED=true

# Application Origin
WEB_APP_ORIGIN=https://your-dev-app.herokuapp.com
```

### 3. Setup Heroku Development App

If a development Heroku app doesn't exist, create one:

```bash
# Login to Heroku
heroku auth:login

# Create new Heroku app for development
heroku create cardiowell-dev-[your-name] --region us

# Or connect to existing development app
heroku git:remote -a cardiowell-dev-existing-app-name

# Verify connection
heroku apps:info
```

### 4. Configure Heroku Environment Variables

Set all required environment variables in your Heroku development app:

```bash
# Set development environment variables
heroku config:set NODE_ENV=development
heroku config:set mongoUri="your-development-mongodb-connection-string"
heroku config:set MAGIC_LINK_JWT_SECRET="your-dev-jwt-secret"
heroku config:set clinicalNoteKey="your-32-byte-dev-encryption-key"
heroku config:set sendgridAPI="your-sendgrid-dev-api-key"
heroku config:set twilioSID="your-twilio-dev-sid"
heroku config:set twilioToken="your-twilio-dev-token"
heroku config:set twilioNumber="your-twilio-dev-number"

# Continue setting all other variables...

# Verify all variables are set
heroku config
```

## Step-by-Step Deployment Process

### Phase 1: Prepare Frontend Build

1. **Navigate to Frontend Repository**
   ```bash
   cd patientCareReact-master
   ```

2. **Ensure Clean Working Directory**
   ```bash
   # Check for uncommitted changes
   git status
   
   # Commit any pending changes
   git add .
   git commit -m "Development changes for deployment"
   ```

3. **Install Dependencies (if needed)**
   ```bash
   # Clear node_modules if having issues
   rm -rf node_modules package-lock.json
   
   # Install fresh dependencies
   yarn install
   ```

4. **Build Production Frontend**
   ```bash
   # Run production build
   yarn build
   
   # Verify build completed successfully
   echo "Build exit code: $?"
   
   # Check build output
   ls -la build/
   ```

5. **Verify Build Contents**
   ```bash
   # Ensure required files exist
   ls build/index.html
   ls build/static/css/
   ls build/static/js/
   ls build/manifest.json
   
   # Check build size (should be reasonable)
   du -sh build/
   ```

### Phase 2: Integrate Frontend with Backend

1. **Navigate to Backend Repository**
   ```bash
   cd ../cardiowell-backend
   ```

2. **Backup Current Build (Optional)**
   ```bash
   # Create backup of current build
   cp -r build/ build-backup-$(date +%Y%m%d-%H%M%S)/
   ```

3. **Clear Existing Build**
   ```bash
   # Remove old build files
   rm -rf build/*
   ```

4. **Copy New Frontend Build**
   ```bash
   # Copy all build files from frontend to backend
   cp -r ../patientCareReact-master/build/* ./build/
   
   # Verify copy was successful
   ls -la build/
   ```

5. **Verify Integration**
   ```bash
   # Check that index.html exists
   cat build/index.html | head -n 5
   
   # Verify static assets
   ls build/static/
   ```

### Phase 3: Deploy to Heroku

1. **Commit Frontend Build Changes**
   ```bash
   # Check what files changed
   git status
   
   # Add all build files
   git add build/
   
   # Commit with descriptive message
   git commit -m "Deploy: Update frontend build for development - $(date)"
   ```

2. **Push to Development Branch**
   ```bash
   # Push to development branch (or master for dev environment)
   git push origin master
   
   # Or if using a development branch:
   # git push origin development
   ```

3. **Deploy to Heroku**

   **Option A: Deploy via Heroku CLI (Recommended)**
   ```bash
   # Deploy directly to Heroku
   git push heroku master
   
   # Monitor deployment in real-time
   heroku logs --tail
   ```

   **Option B: Deploy via Heroku Dashboard**
   ```bash
   # If using dashboard deployment:
   echo "Navigate to: https://dashboard.heroku.com/apps/your-dev-app-name"
   echo "1. Click 'Deploy' tab"
   echo "2. Scroll to 'Manual Deploy'"
   echo "3. Select 'master' branch"
   echo "4. Click 'Deploy Branch'"
   ```

4. **Monitor Deployment Progress**
   ```bash
   # Watch deployment logs
   heroku logs --tail
   
   # Check build progress
   heroku builds:info
   
   # View recent activity
   heroku activity
   ```

### Phase 4: Post-Deployment Tasks

1. **Wait for Deployment Completion**
   ```bash
   # Check application status
   heroku ps
   
   # Ensure web dyno is running
   heroku ps:scale web=1
   ```

2. **Restart Application (if needed)**
   ```bash
   # Restart all dynos
   heroku restart
   
   # Wait for restart to complete
   sleep 10
   ```

## Verification and Testing

### 1. Basic Connectivity Tests

```bash
# Get your Heroku app URL
HEROKU_URL=$(heroku apps:info -s | grep web_url | cut -d= -f2)
echo "App URL: $HEROKU_URL"

# Test basic connectivity
curl -f "${HEROKU_URL}ping" || echo "Ping test failed"

# Test with expected response
PING_RESPONSE=$(curl -s "${HEROKU_URL}ping")
if [ "$PING_RESPONSE" = "pong" ]; then
    echo "✅ Ping test successful"
else
    echo "❌ Ping test failed. Response: $PING_RESPONSE"
fi
```

### 2. Frontend Application Tests

```bash
# Test that frontend loads
curl -s "$HEROKU_URL" | grep -q "CardioWell" && echo "✅ Frontend loads" || echo "❌ Frontend load failed"

# Test static assets
curl -s -o /dev/null -w "%{http_code}" "${HEROKU_URL}static/css/" | grep -q "200" && echo "✅ CSS assets accessible" || echo "❌ CSS assets failed"

# Test manifest file
curl -s -o /dev/null -w "%{http_code}" "${HEROKU_URL}manifest.json" | grep -q "200" && echo "✅ Manifest accessible" || echo "❌ Manifest failed"
```

### 3. API Endpoint Tests

```bash
# Test API endpoints (these might require authentication)
echo "Testing API endpoints..."

# Test public endpoints if any exist
curl -s -o /dev/null -w "Status: %{http_code}" "${HEROKU_URL}routes/ping"
echo ""

# List available endpoints (if there's a discovery endpoint)
# curl -s "${HEROKU_URL}routes/" 2>/dev/null | head -n 20
```

### 4. Browser Testing Checklist

Open your browser and navigate to your Heroku development URL:

1. **Frontend Loading**
   - [ ] React application loads without errors
   - [ ] No console errors in browser developer tools
   - [ ] All CSS styles are applied correctly
   - [ ] Images and icons load properly

2. **Navigation Testing**
   - [ ] Client-side routing works (refresh any page)
   - [ ] Navigation between different sections works
   - [ ] Back/forward browser buttons work correctly

3. **Authentication Testing**
   - [ ] Login page loads
   - [ ] Can attempt login (even if credentials are test)
   - [ ] Error handling works for invalid credentials

4. **API Connectivity**
   - [ ] Frontend can communicate with backend API
   - [ ] No CORS errors in browser console
   - [ ] API responses are received and processed

### 5. Database Connectivity Test

```bash
# Check database connection (requires app to be running)
heroku logs --tail | grep -i "mongodb\|database\|connection" &
GREP_PID=$!

# Trigger a database operation by accessing the app
curl -s "$HEROKU_URL" > /dev/null

# Wait a moment for logs
sleep 5

# Stop grep and check if we saw database connection logs
kill $GREP_PID 2>/dev/null
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Build Failures

**Issue**: Heroku build fails with dependency errors
```
remote: ERROR: Cannot find module 'some-package'
```

**Solution**:
```bash
# Check package.json for missing dependencies
cd patientCareReact-master
yarn check

# Rebuild lock file
rm yarn.lock
yarn install

# In backend
cd ../cardiowell-backend
yarn check
rm yarn.lock
yarn install

# Redeploy
git add yarn.lock
git commit -m "Fix: Update yarn lockfile"
git push heroku master
```

#### 2. Frontend Build Issues

**Issue**: Frontend build fails or is incomplete
```bash
# Clean build and retry
cd patientCareReact-master
rm -rf build/ node_modules/
yarn install
yarn build

# Check for build errors
echo "Build exit code: $?"
```

**Issue**: Build succeeds but frontend doesn't load
```bash
# Verify build files were copied correctly
cd ../cardiowell-backend
ls -la build/
cat build/index.html | head

# Check if index.html contains expected content
grep -q "CardioWell" build/index.html && echo "✅ Valid build" || echo "❌ Invalid build"
```

#### 3. Environment Variable Issues

**Issue**: Application starts but features don't work

```bash
# Check all environment variables are set
heroku config

# Compare with required variables
heroku config | grep -E "(NODE_ENV|mongoUri|JWT_SECRET)" || echo "Missing critical variables"

# Add missing variables
heroku config:set MISSING_VAR=value

# Restart to apply changes
heroku restart
```

#### 4. Database Connection Issues

**Issue**: Cannot connect to database

```bash
# Check database connectivity from Heroku
heroku run node -e "
const mongoose = require('mongoose');
mongoose.connect(process.env.mongoUri)
  .then(() => console.log('✅ Database connected'))
  .catch(err => console.log('❌ Database error:', err.message));
"

# For MongoDB Atlas, check IP whitelist
echo "Ensure Heroku IP ranges are whitelisted in MongoDB Atlas"
echo "Add 0.0.0.0/0 for development (not recommended for production)"
```

#### 5. Memory or Performance Issues

```bash
# Check dyno status and memory usage
heroku ps
heroku logs --tail | grep -i "memory\|error"

# Scale up dyno if needed
heroku ps:scale web=1:standard-1x
```

### Debugging Commands

```bash
# View detailed logs
heroku logs --tail --dyno=web

# Access Heroku bash shell
heroku run bash

# Check environment variables inside dyno
heroku run env

# Test database connection inside dyno
heroku run node -e "console.log('Testing connection...'); require('mongoose').connect(process.env.mongoUri)"

# Check file system state
heroku run "ls -la build/ && cat build/index.html | head"
```

## Rollback Procedures

### 1. Quick Rollback (Emergency)

If the deployment causes critical issues:

```bash
# View recent releases
heroku releases

# Rollback to previous release
heroku rollback v[previous-version-number]

# Example: heroku rollback v42

# Verify rollback
curl "$HEROKU_URL/ping"
```

### 2. Git-Based Rollback

If you need to rollback to a specific commit:

```bash
# View commit history
git log --oneline -10

# Reset to previous commit (replace COMMIT_HASH)
git reset --hard COMMIT_HASH

# Force push to Heroku
git push heroku master --force

# Or create a revert commit (safer)
git revert HEAD
git push heroku master
```

### 3. Frontend-Only Rollback

If only the frontend build needs to be rolled back:

```bash
# Restore from backup
cd cardiowell-backend
rm -rf build/*
cp -r build-backup-TIMESTAMP/* build/

# Commit and deploy
git add build/
git commit -m "Rollback: Restore previous frontend build"
git push heroku master
```

### 4. Rollback Verification

After any rollback:

```bash
# Test basic functionality
curl "$HEROKU_URL/ping"

# Check application logs
heroku logs --tail

# Test frontend loading
curl -s "$HEROKU_URL" | grep -q "CardioWell" && echo "✅ Frontend OK" || echo "❌ Frontend issue"

# Monitor for a few minutes
heroku logs --tail | head -n 50
```

## Development vs Production Differences

### Environment Variables

| Variable | Development | Production |
|----------|-------------|------------|
| `NODE_ENV` | `development` | `production` |
| `mongoUri` | Local/Dev MongoDB | MongoDB Atlas Production |
| Database Name | `cardiowell-dev` | `cardiowell-prod` |
| API Endpoints | Development APIs | Production APIs |
| `SENTRY_DSN` | Development project | Production project |
| `SERVICE_ENV` | `development` | `production` |

### Application Behavior Differences

#### Development Environment
- **Error Handling**: Detailed error messages shown
- **Logging**: Verbose logging enabled
- **Caching**: Disabled for easier debugging
- **SSL**: Not strictly required
- **Rate Limiting**: Relaxed or disabled
- **Email/SMS**: Test credentials or sandbox mode

#### Production Environment
- **Error Handling**: Sanitized error messages
- **Logging**: Essential logs only
- **Caching**: Enabled for performance
- **SSL**: Required (HTTPS enforcement)
- **Rate Limiting**: Strict limits enforced
- **Email/SMS**: Production credentials

### Testing Approach

#### Development Testing
```bash
# Use test data
heroku run node scripts/testDataLoader.mjs --url $HEROKU_URL --test-mode

# Test with dummy credentials
curl -X POST "$HEROKU_URL/routes/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"testpass"}'
```

#### Performance Considerations

```bash
# Development: Single dyno is usually sufficient
heroku ps:scale web=1:hobby

# Check dyno usage
heroku ps
heroku logs --tail | grep -i "memory"
```

### Security Considerations for Development

1. **Never use production secrets in development**
2. **Use separate database instances**
3. **Enable additional logging for debugging**
4. **Use test API keys where possible**
5. **Document all development-specific configurations**

---

## Quick Reference Commands

```bash
# Complete deployment flow
cd patientCareReact-master && yarn build
cd ../cardiowell-backend
cp -r ../patientCareReact-master/build/* ./build/
git add build/ && git commit -m "Deploy: $(date)"
git push heroku master
heroku logs --tail

# Quick health check
curl $(heroku apps:info -s | grep web_url | cut -d= -f2)ping

# Emergency rollback
heroku rollback v$(expr $(heroku releases --json | jq '.[0].version' | tr -d 'v"') - 1)

# View logs
heroku logs --tail --dyno=web

# Check status
heroku ps && heroku config | wc -l
```

This guide provides a comprehensive walkthrough for deploying Cardiowell to a development environment on Heroku. Follow each section carefully and use the troubleshooting guide when issues arise.