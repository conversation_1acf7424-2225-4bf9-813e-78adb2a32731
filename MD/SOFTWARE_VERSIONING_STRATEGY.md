# CardioWell Software Versioning Strategy

## Overview

This document outlines the comprehensive software versioning strategy for CardioWell deployments, designed to track defects by version across both Development and Production environments.

## Current Project Structure

### Repositories
- **Backend**: `cardiowell-backend/` (Node.js/Express API + serves frontend)
  - Current version: `1.0.0` (in package.json)
  - Main deployment repository
- **Frontend**: `patientCareReact-master/` (React application)
  - Current version: `0.1.0` (in package.json)
  - Development repository, builds integrated into backend

### Deployment Architecture
- **Platform**: Heroku with Git-based deployment
- **Environment**: Node.js buildpack
- **Database**: MongoDB Atlas (production) / Local MongoDB (development)
- **CI/CD**: GitHub Actions for both repositories

## Versioning Scheme: Semantic Versioning (SemVer)

### Format: MAJOR.MINOR.PATCH
```
Example: 2.1.5
```

### Version Components
- **MAJOR**: Breaking changes, major feature releases, architecture changes
- **MINOR**: New features, enhancements, backward-compatible changes
- **PATCH**: Bug fixes, security patches, minor improvements

### Pre-release and Build Metadata

#### Pre-release Tags
- `-alpha.X`: Early internal testing (e.g., `2.1.5-alpha.1`)
- `-beta.X`: Wider testing, feature-complete but not final (e.g., `2.1.5-beta.2`)
- `-rc.X`: Release candidate, almost final (e.g., `2.1.5-rc.1`)

#### Build Metadata
- `+build.X`: Specific build information (e.g., `2.1.5+build.123`)
- `+commit.HASH`: Git commit reference (e.g., `2.1.5+commit.a1b2c3d`)

## Unified Versioning Strategy

### Synchronized Versions
Both frontend and backend will use the **same version number** for each release:
- Backend: `cardiowell-backend/package.json`
- Frontend: `patientCareReact-master/package.json`
- Only environment labels change: `dev`, `prod`, `staging` (when implemented)

### Version Progression Examples
```
Development: 2.1.5-alpha.1
Staging/QA:  2.1.5-rc.1    (when staging environment is implemented)
Production:  2.1.5         (clean version, no tags)
```

## Environment-Specific Implementation

### Development Environment
- **Version Format**: `X.Y.Z-alpha.N` or `X.Y.Z-beta.N`
- **Purpose**: Internal testing, feature development
- **Deployment**: Automatic via GitHub Actions on feature branches

### Staging/QA Environment (Future Implementation)
- **Version Format**: `X.Y.Z-rc.N`
- **Purpose**: Final testing before production
- **Deployment**: Manual promotion from development

### Production Environment
- **Version Format**: `X.Y.Z` (clean, no pre-release tags)
- **Purpose**: Live application for end users
- **Deployment**: Manual via Heroku dashboard or CLI

## Implementation Plan

### Phase 1: Version Management Setup

#### 1.1 Update Package.json Files
```json
// cardiowell-backend/package.json
{
  "name": "patient-care-backend",
  "version": "1.1.0",
  "scripts": {
    "version:patch": "npm version patch",
    "version:minor": "npm version minor",
    "version:major": "npm version major",
    "version:prerelease": "npm version prerelease --preid=alpha"
  }
}

// patientCareReact-master/package.json
{
  "name": "patient-care",
  "version": "1.1.0",
  "scripts": {
    "version:patch": "npm version patch",
    "version:minor": "npm version minor",
    "version:major": "npm version major",
    "version:prerelease": "npm version prerelease --preid=alpha"
  }
}
```

#### 1.2 Create Version Management Scripts
Create `scripts/version-manager.js` in both repositories for synchronized versioning.

#### 1.3 Environment Variable Setup
Add version-related environment variables:
```bash
# Heroku Config Vars
APP_VERSION=1.1.0
BUILD_NUMBER=auto-generated
COMMIT_HASH=auto-generated
ENVIRONMENT=production
```

### Phase 2: UI Integration

#### 2.1 Backend Version Endpoint
Create API endpoint to serve version information:
```javascript
// routes/version.js
app.get('/api/version', (req, res) => {
  res.json({
    version: process.env.APP_VERSION || require('./package.json').version,
    buildNumber: process.env.BUILD_NUMBER,
    commitHash: process.env.COMMIT_HASH,
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString()
  });
});
```

#### 2.2 Frontend Version Display
Add version information to the UI:
- Footer component showing version
- Admin panel with detailed version info
- About/Help section with build details

### Phase 3: CI/CD Integration

#### 3.1 GitHub Actions Enhancement
Update `.github/workflows/all-branches-pipeline.yml` to include version management:

```yaml
- name: Set Version Variables
  run: |
    echo "APP_VERSION=$(node -p "require('./package.json').version")" >> $GITHUB_ENV
    echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_ENV
    echo "COMMIT_HASH=${{ github.sha }}" >> $GITHUB_ENV

- name: Update Heroku Config
  run: |
    heroku config:set APP_VERSION=$APP_VERSION -a cardiowell-application
    heroku config:set BUILD_NUMBER=$BUILD_NUMBER -a cardiowell-application
    heroku config:set COMMIT_HASH=$COMMIT_HASH -a cardiowell-application
```

#### 3.2 Automated Tagging
Implement Git tagging for releases:
```yaml
- name: Create Git Tag
  if: github.ref == 'refs/heads/master'
  run: |
    git tag v${{ env.APP_VERSION }}
    git push origin v${{ env.APP_VERSION }}
```

## Version Display Locations

### 1. Application UI
- **Footer**: Small version number (e.g., "v2.1.5")
- **Admin Panel**: Detailed version information
- **About Page**: Full version details with build info

### 2. Mobile UI (Future)
- **Settings Screen**: Version information
- **About Section**: Build details

### 3. Deployment Notes
- **Heroku Dashboard**: Release descriptions
- **GitHub Releases**: Automated release notes
- **Change Logs**: Version-specific changes

### 4. Tracking Tools
- **Sentry**: Version-tagged error reports
- **Analytics**: Version-based user segmentation
- **Support Tools**: Version information for troubleshooting

## Promotion Process Workflow

### Development to Production
```mermaid
graph LR
    A[Feature Branch] --> B[Dev: 2.1.5-alpha.1]
    B --> C[Testing & QA]
    C --> D[Staging: 2.1.5-rc.1]
    D --> E[Final Testing]
    E --> F[Production: 2.1.5]
```

### Version Bump Commands
```bash
# For patch releases (bug fixes)
npm run version:patch

# For minor releases (new features)
npm run version:minor

# For major releases (breaking changes)
npm run version:major

# For pre-releases (alpha/beta)
npm run version:prerelease
```

## Benefits of This Strategy

### 1. Defect Tracking
- Clear version association for bug reports
- Easy identification of when issues were introduced
- Simplified rollback procedures

### 2. Release Management
- Consistent versioning across all components
- Clear promotion path from dev to production
- Automated version tracking in deployments

### 3. User Communication
- Transparent version information for users
- Clear release notes and change logs
- Better support and troubleshooting

### 4. Development Workflow
- Standardized release process
- Automated version management
- Reduced manual errors in versioning

## Next Steps

1. **Immediate Actions**:
   - Update package.json versions to synchronized values
   - Create version management scripts
   - Add version endpoint to backend API

2. **Short-term Goals**:
   - Implement UI version display
   - Enhance CI/CD with version automation
   - Set up Heroku environment variables

3. **Long-term Goals**:
   - Implement staging environment with RC versions
   - Add automated release notes generation
   - Integrate with issue tracking systems

## Implementation Code Examples

### Version Management Script
Create `scripts/version-sync.js` in both repositories:

```javascript
#!/usr/bin/env node
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class VersionManager {
  constructor() {
    this.packagePath = path.join(process.cwd(), 'package.json');
    this.package = require(this.packagePath);
  }

  getCurrentVersion() {
    return this.package.version;
  }

  updateVersion(newVersion) {
    this.package.version = newVersion;
    fs.writeFileSync(this.packagePath, JSON.stringify(this.package, null, 2) + '\n');
    console.log(`Updated version to ${newVersion}`);
  }

  createGitTag(version) {
    try {
      execSync(`git tag v${version}`, { stdio: 'inherit' });
      console.log(`Created git tag v${version}`);
    } catch (error) {
      console.error('Failed to create git tag:', error.message);
    }
  }

  syncWithOtherRepo(otherRepoPath) {
    const otherPackagePath = path.join(otherRepoPath, 'package.json');
    if (fs.existsSync(otherPackagePath)) {
      const otherPackage = require(otherPackagePath);
      otherPackage.version = this.package.version;
      fs.writeFileSync(otherPackagePath, JSON.stringify(otherPackage, null, 2) + '\n');
      console.log(`Synced version with ${otherRepoPath}`);
    }
  }
}

// Usage
const versionManager = new VersionManager();
const command = process.argv[2];
const value = process.argv[3];

switch (command) {
  case 'current':
    console.log(versionManager.getCurrentVersion());
    break;
  case 'update':
    versionManager.updateVersion(value);
    break;
  case 'sync':
    versionManager.syncWithOtherRepo(value);
    break;
  case 'tag':
    versionManager.createGitTag(versionManager.getCurrentVersion());
    break;
  default:
    console.log('Usage: node version-sync.js [current|update|sync|tag] [value]');
}
```

### Backend Version Route
Add to `cardiowell-backend/routes/version.js`:

```javascript
const express = require('express');
const router = express.Router();
const packageJson = require('../package.json');

router.get('/', (req, res) => {
  const versionInfo = {
    version: process.env.APP_VERSION || packageJson.version,
    buildNumber: process.env.BUILD_NUMBER || 'local',
    commitHash: process.env.COMMIT_HASH || 'unknown',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform
  };

  res.json(versionInfo);
});

router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    version: process.env.APP_VERSION || packageJson.version,
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
```

### Frontend Version Component
Create `patientCareReact-master/src/components/VersionInfo.jsx`:

```jsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';

const VersionInfo = ({ showDetailed = false }) => {
  const [versionInfo, setVersionInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchVersionInfo = async () => {
      try {
        const response = await axios.get('/api/version');
        setVersionInfo(response.data);
      } catch (error) {
        console.error('Failed to fetch version info:', error);
        setVersionInfo({
          version: 'Unknown',
          environment: 'Unknown'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchVersionInfo();
  }, []);

  if (loading) return <span>Loading...</span>;

  if (!showDetailed) {
    return (
      <span className="version-info-simple">
        v{versionInfo?.version}
      </span>
    );
  }

  return (
    <div className="version-info-detailed">
      <h3>Version Information</h3>
      <table>
        <tbody>
          <tr>
            <td><strong>Version:</strong></td>
            <td>{versionInfo?.version}</td>
          </tr>
          <tr>
            <td><strong>Build:</strong></td>
            <td>{versionInfo?.buildNumber}</td>
          </tr>
          <tr>
            <td><strong>Environment:</strong></td>
            <td>{versionInfo?.environment}</td>
          </tr>
          <tr>
            <td><strong>Commit:</strong></td>
            <td>{versionInfo?.commitHash?.substring(0, 8)}</td>
          </tr>
          <tr>
            <td><strong>Build Time:</strong></td>
            <td>{new Date(versionInfo?.timestamp).toLocaleString()}</td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default VersionInfo;
```

### Enhanced GitHub Actions Workflow
Update `.github/workflows/all-branches-pipeline.yml`:

```yaml
name: Common Pipeline with Versioning

on:
  push:
    branches:
      - '**'

env:
  NODE_VERSION: '20.9.0'

jobs:
  static-checks:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      build-number: ${{ steps.version.outputs.build-number }}

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'

      - name: Extract Version Information
        id: version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          BUILD_NUMBER=${{ github.run_number }}
          COMMIT_HASH=${{ github.sha }}

          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "build-number=$BUILD_NUMBER" >> $GITHUB_OUTPUT
          echo "commit-hash=$COMMIT_HASH" >> $GITHUB_OUTPUT

          echo "APP_VERSION=$VERSION" >> $GITHUB_ENV
          echo "BUILD_NUMBER=$BUILD_NUMBER" >> $GITHUB_ENV
          echo "COMMIT_HASH=$COMMIT_HASH" >> $GITHUB_ENV

      - name: Cache Yarn Modules
        id: yarn-cache
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/yarn.lock') }}

      - name: Install Dependencies
        if: steps.yarn-cache.outputs.cache-hit != 'true'
        run: yarn

      - name: Lint
        run: yarn lint --quiet

      - name: Create Release Tag
        if: github.ref == 'refs/heads/master'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git tag v${{ steps.version.outputs.version }}-build.${{ steps.version.outputs.build-number }}
          git push origin v${{ steps.version.outputs.version }}-build.${{ steps.version.outputs.build-number }}

  deploy:
    needs: static-checks
    if: github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to Heroku
        uses: akhileshns/heroku-deploy@v3.12.12
        with:
          heroku_api_key: ${{ secrets.HEROKU_API_KEY }}
          heroku_app_name: "cardiowell-application"
          heroku_email: ${{ secrets.HEROKU_EMAIL }}

      - name: Update Heroku Config Vars
        run: |
          heroku config:set APP_VERSION=${{ needs.static-checks.outputs.version }} -a cardiowell-application
          heroku config:set BUILD_NUMBER=${{ needs.static-checks.outputs.build-number }} -a cardiowell-application
          heroku config:set COMMIT_HASH=${{ github.sha }} -a cardiowell-application
```

## Maintenance and Updates

- **Weekly**: Review version progression and release schedule
- **Monthly**: Audit version consistency across environments
- **Quarterly**: Evaluate versioning strategy effectiveness
- **Annually**: Review and update versioning scheme if needed

## Quick Start Commands

### Initial Setup
```bash
# 1. Sync versions between repositories
cd cardiowell-backend
node scripts/version-sync.js sync ../patientCareReact-master

# 2. Update to new version
npm run version:minor  # or version:patch, version:major

# 3. Create git tag
node scripts/version-sync.js tag

# 4. Deploy with version info
git add . && git commit -m "Release v$(node -p "require('./package.json').version")"
git push origin master
```

### Daily Operations
```bash
# Check current version
node scripts/version-sync.js current

# Create alpha release
npm run version:prerelease

# Promote to production (remove pre-release tags)
npm version patch --no-git-tag-version
```

### Troubleshooting
```bash
# Check version consistency
curl https://careportal.cardiowell.io/api/version

# Verify Heroku config
heroku config -a cardiowell-application | grep VERSION

# Check deployment logs
heroku logs --tail -a cardiowell-application | grep -i version
```
