# Cardiowell Backend

## Overview

The Cardiowell Backend is a Node.js-based server application designed to support a healthcare monitoring and management platform. It integrates with various medical devices and external APIs to collect, store, and process patient health data. The system provides real-time data updates, automated tasks, secure authentication, and extensive data filtering functionalities. Core technologies include:

- **Node.js & Express.js**: Application and API framework
- **MongoDB & Mongoose**: Database and object modeling
- **Socket.IO**: Real-time communication
- **Passport.js**: Authentication middleware
- **Cron**: Scheduled tasks
- **External Integrations**: Withings and iGlucose APIs for fetching and managing device data

## Key Features

- **Authentication & Authorization**:  
  Implements secure login for providers and admins using JWTs and role-based access controls.
  
- **Real-Time Data**:  
  Utilizes Socket.IO for streaming patient data to connected clients in real-time.

- **Device Integrations**:  
  Supports devices from multiple manufacturers (e.g., Transtek, BodyTrace, BerryMed, A&D, Withings). Retrieves and processes health metrics like blood pressure, weight, glucose readings, and oxygen saturation.

- **Clinical Notes & Encryption**:  
  Creates, encrypts, and stores clinical notes securely, providing decryption on retrieval and linking notes to healthcare providers.

- **Program & Threshold Management**:  
  Associates clinics with health programs and configurable thresholds, enabling alerts and personalized monitoring.

- **Filtering Utilities (mongoFilter)**:  
  Offers a flexible system for dynamic querying and filtering of data based on arrays, dates, numeric ranges, and strings.

- **Cron Jobs**:  
  Automates recurring tasks such as cleaning old records, fetching device data periodically, and testing device endpoints.

- **Notification Services**:  
  Integrates with SendGrid and Twilio to send email and SMS notifications, including password reset links and magic links for patients.

## Key Files & Directories

- `app.mjs`: Main application entry point. Configures Express, database connections, middleware, routes, WebSocket (Socket.IO), and cron jobs.
- `package.json`: Specifies dependencies, scripts, and project metadata.
- `docker-compose.yaml`: Defines Docker services for MongoDB and Mongo Express for local development.
- `routes/`: Contains Express route definitions for various modules:
   - `auth`, `clinicalNotes`, `customer`, `device`, `provider`, `program`, `threshold`, `withings` routes.
   - Integrates with controllers and middleware for authentication and data validation.
- `controllers/`: Handle incoming requests, validate input, and respond using services:
   - `auth`: Token management, URL shortening
   - `clinic`: Add/delete clinics and their default programs
   - `clinicalNotes`: Create and retrieve encrypted patient notes
   - `customer`: Manage customers, tests, and related reports
   - `device`: Create, update, and manage devices; handle telemetry data
   - `withings`: Activate users, retrieve data from Withings API, handle notifications
- `services/`: Encapsulate business logic and database operations:
   - `program`, `threshold`, `mongoFilter`, `profileTime`, `users`, etc.
   - Integrate with external APIs (Withings, iGlucose) and handle data processing.
- `models/`: Mongoose schemas and models for entities such as `Patient`, `Clinic`, `Program`, `Threshold`, `Device`, `Provider`, `ClinicalNote`, and `ShortUrl`.
- `utils/`: Utility functions for filtering, date manipulation, validation, encryption, and notification sending.
- `threshold/`: Manages health thresholds, providing utilities for creating, updating, and validating patient threshold data.

## Security & Data Integrity
- **Encryption**: Clinical notes and sensitive fields are encrypted using AES-256-CBC.
- **Authentication**: Uses JWTs and Passport.js for secure access control. Provides role-based access (e.g., super admins, providers).
- **Validation**: Leverages express-validator to ensure request payloads meet schema requirements.
- **Data Consistency**: Employs aggregation pipelines, robust error handling, and atomic operations to maintain database integrity.

## Getting Started (Development)
[Getting Started Guide](GETTING_STARTED.md)

## License
This project is licensed under the ISC License.
