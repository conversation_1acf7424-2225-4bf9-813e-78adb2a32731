import { deleteClinicAndPrograms } from "../service/deleteClinicAndPrograms.mjs"

export const deleteClinic = async (req, res) => {
  try {
    const clinic = await deleteClinicAndPrograms(req.body.id)
    if (!clinic) {
      return res.status(404).send({ message: "Clinic not found" })
    }
    return res.status(201).send({ message: "Success", clinic })
  } catch (err) {
    return res.status(500).json(err)
  }
}
