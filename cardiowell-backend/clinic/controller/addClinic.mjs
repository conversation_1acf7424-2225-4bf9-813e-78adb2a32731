import { createClinic } from "../service/createClinic.mjs"

export const addClinic = async (req, res) => {
  try {
    const clinic = await createClinic({
      name: req.body.practiceName,
      address: req.body.practiceAddress,
      phoneNumber: req.body.practicePhoneNumber,
      mainContact: req.body.practiceMainContact,
      logo: req.body.logo,
    })
    return res.status(201).send({ message: "Success", clinic })
  } catch (error) {
    console.error(error)
    return res.status(500).send({ message: "Error", error })
  }
}
