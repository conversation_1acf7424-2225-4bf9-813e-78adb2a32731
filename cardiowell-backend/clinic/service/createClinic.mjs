import Clinic from "../../models/clinic.js"
import { createProgram } from "../../program/service/createProgram.mjs"
import { createThreshold } from "../../threshold/service/createThreshold.mjs"

const defaultPrograms = [
  "Hypertension",
  "Diabetes",
  "COPD",
  "Kidney Disease",
  "CHF",
  "Other Specialty",
]

export const createClinic = async ({ name, address, phoneNumber, mainContact, logo }) => {
  const clinic = await Clinic.create({
    name,
    address,
    phoneNumber,
    mainContact,
    logo,
  })

  for (const program of defaultPrograms) {
    const threshold = await createThreshold({})
    await createProgram({
      name: program,
      clinicId: clinic._id,
      thresholdId: threshold._id,
      description: program,
    })
  }

  return clinic
}
