import { validationResult, matchedData } from "express-validator"
import { thresholdValidator } from "../utils/validator.mjs"
import { createThreshold } from "../service/createThreshold.mjs"

export const createThresholdValidator = [...thresholdValidator]

export const createNewThreshold = async (request, response) => {
  try {
    validationResult(request).throw()
    const { bloodPressure, weight, pulseOximeter, bloodGlucose } = matchedData(request)
    const threshold = await createThreshold({
      bloodGlucose,
      bloodPressure,
      weight,
      pulseOximeter,
    })

    return response.status(201).send({ message: "Success", threshold })
  } catch (error) {
    console.error(error)
    return response.status(500).json(error)
  }
}
