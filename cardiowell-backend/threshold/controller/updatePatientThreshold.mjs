import { body, validationResult, matchedData } from "express-validator"
import { updateThreshold } from "../service/updateThreshold.mjs"
import { createThreshold } from "../service/createThreshold.mjs"
import { thresholdValidator } from "../utils/validator.mjs"
import patient from "../../models/patient.js"

export const updatePatientThresholdValidator = [
  body("patientId").notEmpty().isString().escape(),
  body("programId").isString().optional().escape(),
  ...thresholdValidator,
]

export const updatePatientThreshold = async (request, response) => {
  try {
    validationResult(request).throw()
    const { patientId, bloodPressure, weight, pulseOximeter, bloodGlucose, programId } =
      matchedData(request)

    const patientDoc = await patient.findById(patientId)

    if (!patientDoc) {
      return response.status(404).send({ message: "patient not found " })
    }

    if (programId) {
      patientDoc.programId = programId
      await patientDoc.save()
    }

    if (patientDoc.thresholdId) {
      const updatedThreshold = await updateThreshold({
        thresholdId: patientDoc.thresholdId,
        bloodPressure,
        weight,
        pulseOximeter,
        bloodGlucose,
      })

      return response.status(201).send({
        message: "Success",
        threshold: updatedThreshold,
        programId,
      })
    }

    const threshold = await createThreshold({
      bloodPressure,
      weight,
      pulseOximeter,
      bloodGlucose,
    })

    patientDoc.thresholdId = threshold._id
    await patientDoc.save()

    return response.status(201).send({ message: "Success", threshold, programId })
  } catch (err) {
    console.error(err)
    return response.status(500).json(err)
  }
}
