import { body } from "express-validator"

const validateField = path => [
  body(path + ".value")
    .isNumeric()
    .escape(),
  body(path + ".enabled")
    .isBoolean()
    .escape(),
]

const generateValidators = component => [
  ...validateField(`${component}.upperHigh`),
  ...validateField(`${component}.upperMedium`),
  ...validateField(`${component}.lowerHigh`),
  ...validateField(`${component}.lowerMedium`),
]

const generateAlertStageValidators = component => [
  ...validateField(`${component}.normal`),
  ...validateField(`${component}.elevated`),
  ...validateField(`${component}.stageOne`),
  ...validateField(`${component}.stageTwo`),
  ...validateField(`${component}.crisis`),
]

export const thresholdValidator = [
  ...generateAlertStageValidators("bloodPressure.diastolic"),
  ...generateAlertStageValidators("bloodPressure.systolic"),
  ...generateValidators("bloodPressure.pulse"),
  ...generateValidators("weight.weight"),
  ...generateValidators("pulseOximeter.spo2"),
  ...generateValidators("pulseOximeter.pulse"),
  ...generateValidators("bloodGlucose.glucose"),
]
