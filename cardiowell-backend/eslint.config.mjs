import js from "@eslint/js"
import prettier from "eslint-plugin-prettier"
import config<PERSON>rettier from "eslint-config-prettier"
import globals from "globals"
import { includeIgnoreFile } from "@eslint/compat"
import path from "node:path"
import { fileURLToPath } from "node:url"

// Resolve the directory and .gitignore path
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const gitignorePath = path.resolve(__dirname, ".gitignore")

export default [
  includeIgnoreFile(gitignorePath),
  {
    ignores: ["build/"],
  },
  js.configs.recommended,
  {
    files: ["**/*.{js,mjs}"],
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: {},
      },
      globals: {
        ...globals.node,
        ...globals.jest,
      },
    },
    plugins: {
      prettier,
    },
    rules: {
      "prettier/prettier": "error",
      "no-console": "warn", // Optionally, turn off for more flexibility with back-end logging
      "no-unused-vars": "warn",
    },
  },
  configPrettier,
]
