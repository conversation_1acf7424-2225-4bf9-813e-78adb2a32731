import "dotenv/config"
import { Command } from "commander"
import axios from "axios"
import fs from "fs"
import FormData from "form-data"
import Table from "cli-table3"

const program = new Command()

const DEFAULT_URL = "http://localhost:8081"
const API_KEY = "VD8fdCYZgmnpC5dJbfZXDHJypBjk8bGdHNRQwDP7ihf3101Em2aH08GZAHK2XHGuatx1uC"

program
  .name("test-data-cli")
  .description("CLI tool to manage blood pressure test data in MongoDB")
  .version("1.0.0")

program
  .command("load <imei> <csvFile>")
  .description("Load test data from a CSV file into the database")
  .option("-u, --url <url>", "API endpoint URL", DEFAULT_URL)
  .action(async (imei, csvFile, options) => {
    try {
      const formData = new FormData()
      formData.append("file", fs.createReadStream(csvFile))

      console.log("Loading...")
      const response = await axios.post(
        `${options.url}/routes/devices/${imei}/load-test-data`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            authorization: API_KEY,
          },
        },
      )

      console.log("Done")
    } catch (err) {
      console.error("Error loading data:", err)
    }
  })

program
  .command("delete <identifier>")
  .description("Delete test data by IMEI or session ID")
  .option("-u, --url <url>", "API endpoint URL", DEFAULT_URL)
  .action(async (identifier, options) => {
    try {
      await axios.delete(
        `${options.url}/routes/devices/test-data-uploads/${identifier}`,
        {
          headers: {
            authorization: API_KEY,
          },
        },
      )

      console.log("Done")
    } catch (err) {
      console.error("Error deleting data:", err)
    }
  })

program
  .command("list")
  .description("List all test data grouped by IMEI and session")
  .option("-u, --url <url>", "API endpoint URL", DEFAULT_URL)
  .action(async options => {
    try {
      const response = await axios.get(
        `${options.url}/routes/devices/test-data-uploads`,
        {
          headers: {
            authorization: API_KEY,
          },
        },
      )
      // Create table instance
      const table = new Table({
        head: ["Session ID", "IMEI", "Amount"],
        style: {
          head: ["cyan"],
          border: ["gray"],
        },
        colWidths: [30, 20, 10],
      })

      // Add data to table
      response.data.forEach(item => {
        table.push([item.sessionId, item.imei, item.amount.toString()])
      })

      console.log(table.toString())
    } catch (err) {
      console.error("Error listing data:", err)
    }
  })

program.parse(process.argv)
