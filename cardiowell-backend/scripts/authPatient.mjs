import Patient from "../models/patient.js"
import { createShortBPMagicLink } from "../routes/patientAuth.mjs"
import mongoose from "mongoose"
import { Command } from "commander"
import "dotenv/config"
import open from "open"

const program = new Command()

program
  .name("auth:patient")
  .description("Authenticate as a patient for development")
  .version("0.0.1")

program
  .command("auth <idOrPhone>")
  .description("Authenticate as a patient for development")
  .action(async idOrPhone => {
    try {
      await connectMongo()
    } catch (error) {
      console.error("Error connecting to MongoDB:", error)
      return
    }

    try {
      const query = mongoose.Types.ObjectId.isValid(idOrPhone)
        ? { $or: [{ _id: idOrPhone }, { phone: idOrPhone }] }
        : { phone: idOrPhone }
      const patient = await Patient.findOne(query)
      if (!patient) {
        console.error(`No patient found with ID or phone: ${idOrPhone}`)
        return
      }
      console.log(
        `Logging in as patient: ${patient.firstName} ${patient.lastName}, _id: ${patient._id}, phoneNumber: ${patient.phone}`,
      )

      const link = await createShortBPMagicLink(patient)
      console.log(`Magic link created: ${link}`)

      console.log("Opening link in default browser...")
      await open(link)
    } catch (error) {
      console.error("Error logging in as test patient:", error)
    } finally {
      await mongoose.disconnect()
    }
  })

program.parse(process.argv)

async function connectMongo() {
  await mongoose.connect(process.env.mongoUri, {
    useUnifiedTopology: true,
    useNewUrlParser: true,
  })
  mongoose.Promise = global.Promise
}
