import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const adBpmStage = (isoString, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ isTest: { $ne: true } }]

  return [
    {
      $lookup: {
        from: "ad_bpms",
        localField: "adBpIMEI",
        foreignField: "payload.imei",
        pipeline: [
          { $match: { $and: [{ "payload.imei": { $ne: null } }, ...testFilter] } },
          { $sort: { "payload.timestamp": -1 } },
          ...(isOverview ? [{ $limit: 1 }] : []),
        ],
        as: "adBpm",
      },
    },
    ...(isOverview
      ? [
          {
            $lookup: {
              from: "ad_bpms",
              localField: "adBpIMEI",
              foreignField: "payload.imei",
              pipeline: [
                {
                  $match: {
                    $and: [
                      { "payload.imei": { $ne: null } },
                      { "payload.timestamp": { $gte: isoString } },
                      ...testFilter,
                    ],
                  },
                },
                { $count: "adRecentBpms" },
              ],
              as: "adRecentBpms",
            },
          },
          {
            $unwind: {
              path: "$adRecentBpms",
              preserveNullAndEmptyArrays: true,
            },
          },
          ...daysWithReadingsLookup({
            collection: "ad_bpms",
            localField: "adBpIMEI",
            foreignField: "payload.imei",
            timestampInput: "$payload.timestamp",
            matchExpression: {
              $and: [
                { "payload.imei": { $ne: null } },
                { "payload.timestamp": { $gte: isoString } },
                ...testFilter,
              ],
            },
            alias: "adBpmDaysWithReadings",
          }),
        ]
      : []),
  ]
}
