import mongoose from "mongoose"
import Patient from "../../models/patient.js"
import { getTimeZone } from "./getTimeZone.mjs"
import { getLookupPatientData } from "./lookupPatientData.mjs"
import { lookupPatientOverview } from "./lookupPatientOverview.mjs"
import { getProgramById } from "../../program/service/getProgramById.mjs"
import { getThresholdById } from "../../threshold/service/getThresholdById.mjs"

export const getPatientsForClinic = async clinic => {
  try {
    return await aggregateClinicPatientData(clinic)
  } catch (err) {
    console.log("Error getting patients for clinic", err)
    throw err
  }
}

export const getPatientOverviewsForClinc = async (clinic, providerId) => {
  try {
    return await aggregateClinicPatientOverviews(clinic, providerId)
  } catch (err) {
    console.error(err)
    throw err
  }
}

export const getPatientByUsername = async username => {
  const patientObj = await aggregatePatientDataByUsername(username)
  return { patientObj, patientId: patientObj.id }
}

export const getPatientDataById = async (id, providerId) => {
  const patientObj = await aggregatePatientDataById(id, providerId)
  return { patientObj, patientId: patientObj.id }
}

export const getPatientOverviewById = async (id, providerId) => {
  const patientObj = await aggregatePatientOverview(id, providerId)
  return { patientObj, patientId: patientObj.id }
}

export const translateTimezone = timezone => {
  return getTimeZone(timezone)
}

// get all data of single patient
const aggregatePatientDataByUsername = async username => {
  const patient = await Patient.findOne({ username })
  const [result] = await Patient.aggregate([
    {
      $match: { username },
    },
    {
      $limit: 1,
    },
    ...getLookupPatientData(patient?.showTestData),
  ])
  if (result) {
    result.timeZone = getTimeZone(result.timeZone)
    return result
  }
  return null
}

// get all data of single patient
const aggregatePatientDataById = async (patientId, providerId) => {
  try {
    const patient = await Patient.findOne({ _id: mongoose.Types.ObjectId(patientId) })

    const [result] = await Patient.aggregate([
      {
        $match: { _id: mongoose.Types.ObjectId(patientId) },
      },
      {
        $limit: 1,
      },
      ...getLookupPatientData(patient?.showTestData, providerId),
    ])
    if (patient.thresholdId) {
      const threshold = await getThresholdById(patient.thresholdId)
      result.threshold = threshold
    }

    result.timeZone = getTimeZone(result.timeZone)
    return result
  } catch (err) {
    console.log(err)
    return null
  }
}

// get a single patient with only their most recent reading of each device
const aggregatePatientOverview = async (patientId, providerId) => {
  const [result] = await Patient.aggregate([
    {
      $match: { _id: mongoose.Types.ObjectId(patientId) },
    },
    {
      $limit: 1,
    },
    {
      $addFields: {
        name: { $concat: ["$firstName", " ", "$lastName"] },
      },
    },
    ...lookupPatientOverview(providerId),
  ])
  if (result) {
    result.timeZone = getTimeZone(result.timeZone)
    return result
  }
  return null
}

// get a single patient and all readings from each associated device
const aggregateClinicPatientData = async clinic => {
  const result = await Patient.aggregate([
    {
      $match: { clinic },
    },
    ...getLookupPatientData(),
  ])
  return result.map(patient => {
    const timeZone = getTimeZone(patient.timeZone)
    return {
      ...patient,
      timeZone,
    }
  })
}

// Get all patients in clinic with only their most recent reading of each device
const aggregateClinicPatientOverviews = async (clinic, providerId) => {
  const result = await Patient.aggregate([
    {
      $match: { clinic },
    },
    ...lookupPatientOverview(providerId),
  ])
  return result.map(patient => {
    const timeZone = getTimeZone(patient.timeZone)
    return {
      ...patient,
      timeZone,
    }
  })
}
