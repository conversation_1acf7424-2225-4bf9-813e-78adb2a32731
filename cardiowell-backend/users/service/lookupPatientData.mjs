import moment from "moment"
import { projectToPatientObjStage } from "./projectToPatientObj.mjs"
import { bodyTraceBpmStage } from "./bodytrace/bodyTraceBpm.mjs"
import { transtekBpmStage } from "./transtek/transtekBpm.mjs"
import { bodyTraceWsStage } from "./bodytrace/bodyTraceWs.mjs"
import { transtekWsStage } from "./transtek/transtekWs.mjs"
import { pulseOximeterStage } from "./berry/pulseOximeter.mjs"
import { transtekGlucoseStage } from "./transtek/transtekGlucose.mjs"
import { bodyTraceMessageBpmStage } from "./bodytrace/bodyTraceMessageBpm.mjs"
import { bodyTraceMessageWsStage } from "./bodytrace/bodyTraceMessageWs.mjs"
import { lookupPatientProfileTimes } from "../../profileTime/service/lookupPatientProfileTimes.mjs"

// lookup pipeline stages for all readings for each device associated with patient
export const getLookupPatientData = (isTest, providerId) => {
  const time = moment().subtract(30, "days").toDate()
  const milliseconds = time.getTime()

  return [
    ...bodyTraceBpmStage(0, false, isTest),
    ...bodyTraceMessageBpmStage(0, false, isTest),
    ...transtekBpmStage(0, false, isTest),
    ...bodyTraceWsStage(0, false, isTest),
    ...bodyTraceMessageWsStage(0, false, isTest),
    ...transtekWsStage(0, false, isTest),
    ...pulseOximeterStage(0, false, isTest),
    ...transtekGlucoseStage(0, false, isTest),
    ...(providerId ? lookupPatientProfileTimes(providerId, milliseconds) : []),
    projectToPatientObjStage,
  ]
}
