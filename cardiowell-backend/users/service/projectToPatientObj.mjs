// project stage to patient object format
export const projectToPatientObjStage = {
  $project: {
    _id: 0,
    id: "$_id",
    name: { $concat: ["$firstName", " ", "$lastName"] },
    clinic: 1,
    cellNumber: 1,
    homeNumber: 1,
    email: 1,
    mrn: "$MRN",
    timeZone: "$timeZone",
    timeZone2: "$timeZone",
    bpm: 1,
    btMessagesBpm: 1,
    ttBpm: 1,
    adBpm: 1,
    ws: 1,
    btMessagesWs: 1,
    ttWs: 1,
    pulse: 1,
    glucose: 1,
    weight: 1,
    height: 1,
    withingsBpm: 1,
    withingsBpDevices: 1,
    bpIMEI: 1,
    ttBpIMEI: 1,
    adBpIMEI: 1,
    weightIMEI: 1,
    ttWeightIMEI: 1,
    pulseIMEI: 1,
    glucoseIMEI: 1,
    selectedBpDevice: 1,
    selectedWeightDevice: 1,
    city: 1,
    state: 1,
    address: 1,
    zip: 1,
    deviceNotificationsEnabled: 1,
    threshold: 1,
    programId: 1,
    registrationCompleted: 1,
    btRecentBpms: {
      $ifNull: ["$btRecentBpms.btRecentBpms", 0],
    },
    btBpmDaysWithReadings: 1,
    ttRecentBpms: {
      $ifNull: ["$ttRecentBpms.ttRecentBpms", 0],
    },
    ttBpmDaysWithReadings: 1,
    btRecentWs: {
      $ifNull: ["$btRecentWs.btRecentWs", 0],
    },
    btWsDaysWithReadings: 1,
    ttRecentWs: {
      $ifNull: ["$ttRecentWs.ttRecentWs", 0],
    },
    ttWsDaysWithReadings: 1,
    recentPulse: {
      $ifNull: ["$recentPulse.recentPulse", 0],
    },
    pulseDaysWithReadings: 1,
    recentGlucose: {
      $ifNull: ["$recentGlucose.recentGlucose", 0],
    },
    glucoseDaysWithReadings: 1,
    withingsRecentBpm: {
      $ifNull: ["$withingsRecentBpm.withingsRecentBpm", 0],
    },
    withingsBpmDaysWithReadings: 1,
    btMessagesRecentBpm: {
      $ifNull: ["$btMessagesRecentBpm.btMessagesRecentBpm", 0],
    },
    btMessagesBpmDaysWithReadings: 1,
    btMessagesRecentWs: {
      $ifNull: ["$btMessagesRecentWs.btMessagesRecentWs", 0],
    },
    btMessagesWsDaysWithReadings: 1,
    adRecentBpms: {
      $ifNull: ["$adRecentBpms.adRecentBpms", 0],
    },
    adBpmDaysWithReadings: 1,
    rt: {
      $ifNull: ["$rt.totalTime", 0],
    },
    targetWeight: 1,
    birthdate: 1,
    gender: 1,
    ethnicity: 1,
    maritalStatus: 1,
    education: 1,
    employment: 1,
    income: 1,
    language: 1,
    allergies: 1,
    chronicConditions: 1,
    medications: 1,
    socialConnectedness: 1,
    hypertensionMedications: 1,
    medicationTime: 1,
    lastUpdated: 1,
    showTestData: 1,
  },
}
