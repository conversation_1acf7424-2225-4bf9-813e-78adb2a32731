import moment from "moment"
import { projectToPatientObjStage } from "./projectToPatientObj.mjs"
import { getBerryDate } from "./berry/getBerryDate.mjs"
import { bodyTraceBpmStage } from "./bodytrace/bodyTraceBpm.mjs"
import { bodyTraceWsStage } from "./bodytrace/bodyTraceWs.mjs"
import { transtekBpmStage } from "./transtek/transtekBpm.mjs"
import { transtekWsStage } from "./transtek/transtekWs.mjs"
import { transtekGlucoseStage } from "./transtek/transtekGlucose.mjs"
import { pulseOximeterStage } from "./berry/pulseOximeter.mjs"
import { bodyTraceMessageBpmStage } from "./bodytrace/bodyTraceMessageBpm.mjs"
import { bodyTraceMessageWsStage } from "./bodytrace/bodyTraceMessageWs.mjs"
import { lookupPatientProfileTimes } from "../../profileTime/service/lookupPatientProfileTimes.mjs"

// lookup pipeline stages for the most recent reading for each device assocated with a patient
export const lookupPatientOverview = providerId => {
  const time = moment().subtract(30, "days").toDate()
  const berryDate = getBerryDate(time)
  const milliseconds = time.getTime()
  const unixTime = milliseconds / 1000
  const isoString = time.toISOString()

  return [
    ...bodyTraceBpmStage(milliseconds, true, false),
    ...bodyTraceMessageBpmStage(milliseconds, true, false),
    ...transtekBpmStage(unixTime, true, false),
    ...bodyTraceWsStage(milliseconds, true, false),

    ...bodyTraceMessageWsStage(milliseconds, true, false),
    ...transtekWsStage(unixTime, true, false),

    ...pulseOximeterStage(berryDate, true, false),
    ...transtekGlucoseStage(unixTime, true, false),
    // Thresholds
    {
      $lookup: {
        from: "testthresholds",
        let: { searchId: { $toObjectId: "$thresholdId" } },
        pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$searchId"] } } }, { $limit: 1 }],
        as: "threshold",
      },
    },
    {
      $unwind: {
        path: "$threshold",
        preserveNullAndEmptyArrays: true,
      },
    },
    ...(providerId ? lookupPatientProfileTimes(providerId, milliseconds) : []),
    projectToPatientObjStage,
  ]
}
