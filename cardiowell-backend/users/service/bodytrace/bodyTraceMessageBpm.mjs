import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

const matchBpmValues = [
  { $ne: [{ $type: "$message.values.systolic" }, "missing"] },
  { $ne: [{ $type: "$message.values.diastolic" }, "missing"] },
]

export const bodyTraceMessageBpmStage = (milliseconds, isOverview, isTest) => {
  const testFilterExpr = isTest ? [] : [{ $ne: ["$message.isTest", true] }]

  return [
    {
      $lookup: {
        from: "bodytracemessages",
        let: {
          imei: "$bpIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$message.imei", "$$imei"] },
                  ...testFilterExpr,
                  ...matchBpmValues,
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          ...(isOverview ? [{ $limit: 1 }] : []),
        ],
        as: "btMessagesBpm",
      },
    },
    ...(isOverview
      ? [
          {
            $lookup: {
              from: "bodytracemessages",
              let: {
                imei: "$bpIMEI",
              },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ["$message.imei", "$$imei"] },
                        ...testFilterExpr,
                        ...matchBpmValues,
                        { $gte: ["$message.ts", milliseconds] },
                      ],
                    },
                  },
                },
                { $count: "btMessagesRecentBpm" },
              ],
              as: "btMessagesRecentBpm",
            },
          },
          {
            $unwind: {
              path: "$btMessagesRecentBpm",
              preserveNullAndEmptyArrays: true,
            },
          },
          ...daysWithReadingsLookup({
            collection: "bodytracemessages",
            localField: "bpIMEI",
            foreignField: "message.imei",
            timestampInput: "$message.ts",
            matchExpression: {
              $expr: {
                $and: [
                  ...matchBpmValues,
                  ...testFilterExpr,
                  { $gte: ["$message.ts", milliseconds] },
                ],
              },
            },
            alias: "btMessagesBpmDaysWithReadings",
          }),
        ]
      : []),
  ]
}
