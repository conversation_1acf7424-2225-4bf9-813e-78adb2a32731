import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const bodyTraceWsStage = (milliseconds, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ $ne: ["$isTest", true] }]

  return [
    {
      $lookup: {
        from: "BT_WS",
        let: {
          imei: "$weightIMEI",
        },
        pipeline: [
          { $match: { $expr: { $and: [{ $eq: ["$imei", "$$imei"] }, ...testFilter] } } },
          { $sort: { _created_at: -1 } },
          ...(isOverview ? [{ $limit: 1 }] : []),
        ],
        as: "ws",
      },
    },
  ]
}
