import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const bodyTraceWsStage = (milliseconds, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ $ne: ["$isTest", true] }]

  return [
    {
      $lookup: {
        from: "BT_WS",
        let: {
          imei: "$weightIMEI",
        },
        pipeline: [
          { $match: { $expr: { $and: [{ $eq: ["$imei", "$$imei"] }, ...testFilter] } } },
          { $sort: { _created_at: -1 } },
          ...(isOverview ? [{ $limit: 2 }] : []),
        ],
        as: "ws",
      },
    },
    ...(isOverview
      ? [
          {
            $lookup: {
              from: "BT_WS",
              let: {
                imei: "$weightIMEI",
              },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ["$imei", "$$imei"] },
                        { $gte: ["$ts", milliseconds] },
                        ...testFilter,
                      ],
                    },
                  },
                },
                { $count: "btRecentWs" },
              ],
              as: "btRecentWs",
            },
          },
          {
            $unwind: {
              path: "$btRecentWs",
              preserveNullAndEmptyArrays: true,
            },
          },
          ...daysWithReadingsLookup({
            collection: "BT_WS",
            localField: "weightIMEI",
            foreignField: "imei",
            timestampInput: "$ts",
            matchExpression: {
              $expr: {
                $and: [
                  {
                    $gte: ["$ts", milliseconds],
                  },
                  ...testFilter,
                ],
              },
            },
            alias: "btWsDaysWithReadings",
          }),
        ]
      : []),
  ]
}
