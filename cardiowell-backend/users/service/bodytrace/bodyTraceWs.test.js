import { expect, test } from "vitest"
import { bodyTraceWsStage } from "./bodyTraceWs.mjs"

const dataStage = JSON.stringify([
  {
    $lookup: {
      from: "BT_WS",
      let: {
        imei: "$weightIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { _created_at: -1 } },
      ],
      as: "ws",
    },
  },
])

const overviewStages = JSON.stringify([
  {
    $lookup: {
      from: "BT_WS",
      let: {
        imei: "$weightIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { _created_at: -1 } },
        { $limit: 1 },
      ],
      as: "ws",
    },
  },
  {
    $lookup: {
      from: "BT_WS",
      let: {
        imei: "$weightIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [{ $eq: ["$imei", "$$imei"] }, { $gte: ["$ts", 100] }],
            },
          },
        },
        { $count: "btRecentWs" },
      ],
      as: "btRecentWs",
    },
  },
  {
    $unwind: {
      path: "$btRecentWs",
      preserveNullAndEmptyArrays: true,
    },
  },
])

test("bodyTraceBpmStage returns correct data stages", () => {
  expect(JSON.stringify(bodyTraceWsStage(0, false))).toEqual(dataStage)
})

test("bodyTraceBpmStage returns correct overview stages", () => {
  expect(JSON.stringify(bodyTraceWsStage(100, true))).toEqual(overviewStages)
})
