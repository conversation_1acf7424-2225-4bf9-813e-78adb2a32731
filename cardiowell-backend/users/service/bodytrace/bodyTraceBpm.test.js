import { expect, test } from "vitest"
import { bodyTraceBpmStage } from "./bodyTraceBpm.mjs"

const dataStage = JSON.stringify([
  {
    $lookup: {
      from: "BT_BPM",
      let: {
        imei: "$bpIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { _created_at: -1 } },
      ],
      as: "bpm",
    },
  },
])

const overviewStages = JSON.stringify([
  {
    $lookup: {
      from: "BT_BPM",
      let: {
        imei: "$bpIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { _created_at: -1 } },
        { $limit: 1 },
      ],
      as: "bpm",
    },
  },
  {
    $lookup: {
      from: "BT_BPM",
      let: {
        imei: "$bpIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [{ $eq: ["$imei", "$$imei"] }, { $gte: ["$ts", 100] }],
            },
          },
        },
        { $count: "btRecentBpms" },
      ],
      as: "btRecentBpms",
    },
  },
  {
    $unwind: {
      path: "$btRecentBpms",
      preserveNullAndEmptyArrays: true,
    },
  },
])

test("bodyTraceBpmStage returns correct data stages", () => {
  expect(JSON.stringify(bodyTraceBpmStage(0, false))).toEqual(dataStage)
})

test("bodyTraceBpmStage returns correct overview stages", () => {
  expect(JSON.stringify(bodyTraceBpmStage(100, true))).toEqual(overviewStages)
})
