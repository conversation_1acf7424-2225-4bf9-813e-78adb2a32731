import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const bodyTraceBpmStage = (milliseconds, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ $ne: ["$isTest", true] }]

  return [
    {
      $lookup: {
        from: "BT_BPM",
        let: {
          imei: "$bpIMEI",
        },
        pipeline: [
          { $match: { $expr: { $and: [{ $eq: ["$imei", "$$imei"] }, ...testFilter] } } },
          { $sort: { _created_at: -1 } },
          ...(isOverview ? [{ $limit: 1 }] : []),
        ],
        as: "bpm",
      },
    },
    ...(isOverview
      ? [
          {
            $lookup: {
              from: "BT_BPM",
              let: {
                imei: "$bpIMEI",
              },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ["$imei", "$$imei"] },
                        { $gte: ["$ts", milliseconds] },
                        ...testFilter,
                      ],
                    },
                  },
                },
                { $count: "btRecentBpms" },
              ],
              as: "btRecentBpms",
            },
          },
          {
            $unwind: {
              path: "$btRecentBpms",
              preserveNullAndEmptyArrays: true,
            },
          },
          ...daysWithReadingsLookup({
            collection: "BT_BPM",
            localField: "bpIMEI",
            foreignField: "imei",
            timestampInput: "$ts",
            matchExpression: {
              $expr: {
                $and: [{ $gte: ["$ts", milliseconds] }, ...testFilter],
              },
            },
            alias: "btBpmDaysWithReadings",
          }),
        ]
      : []),
  ]
}
