import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const bodyTraceBpmStage = (milliseconds, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ $ne: ["$isTest", true] }]

  return [
    {
      $lookup: {
        from: "BT_BPM",
        let: {
          imei: "$bpIMEI",
        },
        pipeline: [
          { $match: { $expr: { $and: [{ $eq: ["$imei", "$$imei"] }, ...testFilter] } } },
          { $sort: { _created_at: -1 } },
          ...(isOverview ? [{ $limit: 1 }] : []),
        ],
        as: "bpm",
      },
    },
    // Removed expensive counts and days-with-readings lookups for BT_BPM (unused)
  ]
}
