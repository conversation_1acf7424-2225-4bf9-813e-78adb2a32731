import { expect, test } from "vitest"
import { bodyTraceMessageBpmStage } from "./bodyTraceMessageBpm.mjs"

const dataStage = JSON.stringify([
  {
    $lookup: {
      from: "bodytracemessages",
      let: {
        imei: "$bpIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$message.imei", "$$imei"] },
                { $ne: [{ $type: "$message.values.systolic" }, "missing"] },
                { $ne: [{ $type: "$message.values.diastolic" }, "missing"] },
              ],
            },
          },
        },
        { $sort: { createdAt: -1 } },
      ],
      as: "btMessagesBpm",
    },
  },
])

const overviewStages = JSON.stringify([
  {
    $lookup: {
      from: "bodytracemessages",
      let: {
        imei: "$bpIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$message.imei", "$$imei"] },
                { $ne: [{ $type: "$message.values.systolic" }, "missing"] },
                { $ne: [{ $type: "$message.values.diastolic" }, "missing"] },
              ],
            },
          },
        },
        { $sort: { createdAt: -1 } },
        { $limit: 1 },
      ],
      as: "btMessagesBpm",
    },
  },
  {
    $lookup: {
      from: "bodytracemessages",
      let: {
        imei: "$bpIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$message.imei", "$$imei"] },
                {
                  $ne: [{ $type: "$message.values.systolic" }, "missing"],
                },
                {
                  $ne: [{ $type: "$message.values.diastolic" }, "missing"],
                },
                { $gte: ["$message.ts", 100] },
              ],
            },
          },
        },
        { $count: "btMessagesRecentBpm" },
      ],
      as: "btMessagesRecentBpm",
    },
  },
  {
    $unwind: {
      path: "$btMessagesRecentBpm",
      preserveNullAndEmptyArrays: true,
    },
  },
])

test("bodyTraceBpmStage returns correct data stages", () => {
  expect(JSON.stringify(bodyTraceMessageBpmStage(0, false))).toEqual(dataStage)
})

test("bodyTraceBpmStage returns correct overview stages", () => {
  expect(JSON.stringify(bodyTraceMessageBpmStage(100, true))).toEqual(overviewStages)
})
