import { expect, test } from "vitest"
import { bodyTraceMessageWsStage } from "./bodyTraceMessageWs.mjs"

const dataStage = JSON.stringify([
  {
    $lookup: {
      from: "bodytracemessages",
      let: {
        imei: "$weightIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$message.imei", "$$imei"] },
                { $ne: [{ $type: "$message.values.weight" }, "missing"] },
              ],
            },
          },
        },
        { $sort: { createdAt: -1 } },
      ],
      as: "btMessagesWs",
    },
  },
])

const overviewStages = JSON.stringify([
  {
    $lookup: {
      from: "bodytracemessages",
      let: {
        imei: "$weightIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$message.imei", "$$imei"] },
                { $ne: [{ $type: "$message.values.weight" }, "missing"] },
              ],
            },
          },
        },
        { $sort: { createdAt: -1 } },
        { $limit: 1 },
      ],
      as: "btMessagesWs",
    },
  },
  {
    $lookup: {
      from: "bodytracemessages",
      let: {
        imei: "$weightIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$message.imei", "$$imei"] },
                { $ne: [{ $type: "$message.values.weight" }, "missing"] },
                { $gte: ["$message.ts", 100] },
              ],
            },
          },
        },
        { $count: "btMessagesRecentWs" },
      ],
      as: "btMessagesRecentWs",
    },
  },
  {
    $unwind: {
      path: "$btMessagesRecentWs",
      preserveNullAndEmptyArrays: true,
    },
  },
])

test("bodyTraceBpmStage returns correct data stages", () => {
  expect(JSON.stringify(bodyTraceMessageWsStage(0, false))).toEqual(dataStage)
})

test("bodyTraceBpmStage returns correct overview stages", () => {
  expect(JSON.stringify(bodyTraceMessageWsStage(100, true))).toEqual(overviewStages)
})
