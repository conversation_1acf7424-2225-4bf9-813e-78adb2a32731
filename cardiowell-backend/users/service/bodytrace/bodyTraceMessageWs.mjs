import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const bodyTraceMessageWsStage = (milliseconds, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ $ne: ["$isTest", true] }]

  return [
    {
      $lookup: {
        from: "bodytracemessages",
        let: {
          imei: "$weightIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$message.imei", "$$imei"] },
                  { $ne: [{ $type: "$message.values.weight" }, "missing"] },
                  ...testFilter,
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "btMessagesWs",
      },
    },
  ]
}
