import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const bodyTraceMessageWsStage = (milliseconds, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ $ne: ["$isTest", true] }]

  return [
    {
      $lookup: {
        from: "bodytracemessages",
        let: {
          imei: "$weightIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$message.imei", "$$imei"] },
                  { $ne: [{ $type: "$message.values.weight" }, "missing"] },
                  ...testFilter,
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          ...(isOverview ? [{ $limit: 2 }] : []),
        ],
        as: "btMessagesWs",
      },
    },
    ...(isOverview
      ? [
          {
            $lookup: {
              from: "bodytracemessages",
              let: {
                imei: "$weightIMEI",
              },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ["$message.imei", "$$imei"] },
                        { $ne: [{ $type: "$message.values.weight" }, "missing"] },
                        { $gte: ["$message.ts", milliseconds] },
                        ...testFilter,
                      ],
                    },
                  },
                },
                { $count: "btMessagesRecentWs" },
              ],
              as: "btMessagesRecentWs",
            },
          },
          {
            $unwind: {
              path: "$btMessagesRecentWs",
              preserveNullAndEmptyArrays: true,
            },
          },
          ...daysWithReadingsLookup({
            collection: "bodytracemessages",
            localField: "weightIMEI",
            foreignField: "message.imei",
            timestampInput: "$message.ts",
            matchExpression: {
              $expr: {
                $and: [
                  { $ne: [{ $type: "$message.values.weight" }, "missing"] },
                  { $gte: ["$message.ts", milliseconds] },
                  ...testFilter,
                ],
              },
            },
            alias: "btMessagesWsDaysWithReadings",
          }),
        ]
      : []),
  ]
}
