export const daysWithReadingsLookup = ({
  collection,
  localField,
  foreignField,
  timestampInput,
  matchExpression,
  alias,
}) => [
  {
    $lookup: {
      from: collection,
      localField,
      foreignField,
      pipeline: [
        {
          $match: matchExpression,
        },
        {
          $group: {
            _id: {
              year: {
                $year: {
                  $convert: { input: timestampInput, to: "date", onError: null },
                },
              },
              month: {
                $month: {
                  $convert: { input: timestampInput, to: "date", onError: null },
                },
              },
              day: {
                $dayOfMonth: {
                  $convert: { input: timestampInput, to: "date", onError: null },
                },
              },
            },
          },
        },
      ],
      as: alias,
    },
  },
]
