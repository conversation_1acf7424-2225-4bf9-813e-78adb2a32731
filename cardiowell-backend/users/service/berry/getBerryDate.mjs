const addZero = num => (num > 9 ? num : `0${num}`)

export const getBerryDate = time => {
  const year = time.getFullYear().toString().slice(-2)
  const month = addZero(time.getMonth() + 1)
  const date = addZero(time.getDate())
  const hour = addZero(time.getHours())
  const minutes = addZero(time.getMinutes())
  const seconds = addZero(time.getSeconds())

  return `${year}/${month}/${date},${hour}:${minutes}:${seconds}`
}
