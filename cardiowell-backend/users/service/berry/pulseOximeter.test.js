import { expect, test } from "vitest"
import { pulseOximeterStage } from "./pulseOximeter.mjs"

const dataStage = JSON.stringify([
  {
    $lookup: {
      from: "PulseOximeterData",
      let: {
        imei: "$pulseIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { time: -1 } },
      ],
      as: "pulse",
    },
  },
])

const overviewStages = JSON.stringify([
  {
    $lookup: {
      from: "PulseOximeterData",
      let: {
        imei: "$pulseIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { time: -1 } },
        { $limit: 1 },
      ],
      as: "pulse",
    },
  },
  {
    $lookup: {
      from: "PulseOximeterData",
      let: {
        imei: "$pulseIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [{ $eq: ["$imei", "$$imei"] }, { $gte: ["$time", 100] }],
            },
          },
        },
        { $count: "recentPulse" },
      ],
      as: "recentPulse",
    },
  },
  {
    $unwind: {
      path: "$recentPulse",
      preserveNullAndEmptyArrays: true,
    },
  },
])

test("bodyTraceBpmStage returns correct data stages", () => {
  expect(JSON.stringify(pulseOximeterStage(0, false))).toEqual(dataStage)
})

test("bodyTraceBpmStage returns correct overview stages", () => {
  expect(JSON.stringify(pulseOximeterStage(100, true))).toEqual(overviewStages)
})
