const dateStringConversion = {
  $dateFromString: {
    dateString: { $substrBytes: ["$time", 0, 17] },
    format: "%Y/%m/%d,%H:%M:%S",
    onError: null,
  },
}

export const pulseOximeterStage = (berryDate, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ isTest: { $ne: true } }]

  return [
    {
      $lookup: {
        from: "PulseOximeterData",
        localField: "pulseIMEI",
        foreignField: "imei",
        pipeline: [
          {
            $match: {
              $and: [{ imei: { $ne: null } }, ...testFilter],
            },
          },
          { $sort: { time: -1 } },
          ...(isOverview ? [{ $limit: 1 }] : []),
        ],
        as: "pulse",
      },
    },
    ...(isOverview
      ? [
          {
            $lookup: {
              from: "PulseOximeterData",
              localField: "pulseIMEI",
              foreignField: "imei",
              pipeline: [
                {
                  $match: {
                    $and: [
                      { imei: { $ne: null } },
                      { time: { $gte: berryDate } },
                      ...testFilter,
                    ],
                  },
                },
                { $count: "recentPulse" },
              ],
              as: "recentPulse",
            },
          },
          {
            $unwind: {
              path: "$recentPulse",
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: "PulseOximeterData",
              localField: "pulseIMEI",
              foreignField: "imei",
              pipeline: [
                {
                  $match: {
                    $and: [
                      { imei: { $ne: null } },
                      { time: { $gte: berryDate } },
                      ...testFilter,
                    ],
                  },
                },
                {
                  $group: {
                    _id: {
                      year: {
                        $year: dateStringConversion,
                      },
                      month: {
                        $month: dateStringConversion,
                      },
                      day: {
                        $dayOfMonth: dateStringConversion,
                      },
                    },
                  },
                },
              ],
              as: "pulseDaysWithReadings",
            },
          },
        ]
      : []),
  ]
}
