import { expect, test } from "vitest"
import { getBerryDate } from "./getBerryDate.mjs"

const date1 = new Date("December 17, 2012 12:24:43")
const date2 = new Date("January 1, 2000 06:01:01")
const date3 = new Date("January 1, 2000 06:00:00")

test("Converts date1", () => {
  expect(getBerryDate(date1)).toEqual("12/12/17,12:24:43")
})

test("Converts date2", () => {
  expect(getBerryDate(date2)).toEqual("00/01/01,06:01:01")
})

test("Converts date3", () => {
  expect(getBerryDate(date3)).toEqual("00/01/01,06:00:00")
})
