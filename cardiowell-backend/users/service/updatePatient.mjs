import patient from "../../models/patient.js"

export const updatePatient = async (data, updateClinic) => {
  const updateFields = {
    ...(updateClinic && { clinic: data.clinic }),
    cellNumber: data.cellNumber,
    firstName: data.firstName,
    homeNumber: data.homeNumber,
    lastName: data.lastName,
    email: data.email,
    MRN: data.MRN,
    city: data.city,
    state: data.state,
    country: data.country,
    address: data.address,
    zip: data.zip,
    timeZone: data.timeZone,
    bpIMEI: data.bpIMEI,
    ttBpIMEI: data.ttBpIMEI,
    adBpIMEI: data.adBpIMEI,
    weightIMEI: data.weightIMEI,
    ttWeightIMEI: data.ttWeightIMEI,
    glucoseIMEI: data.glucoseIMEI,
    pulseIMEI: data.pulseIMEI,
    selectedBpDevice: data.selectedBpDevice,
    selectedWeightDevice: data.selectedWeightDevice,
    deviceNotificationsEnabled: data.deviceNotificationsEnabled,
    targetWeight: data.targetWeight,
    birthdate: data.birthdate,
    gender: data.gender,
    ethnicity: data.ethnicity,
    maritalStatus: data.maritalStatus,
    education: data.education,
    employment: data.employment,
    income: data.income,
    language: data.language,
    socialConnectedness: data.socialConnectedness,
    allergies: data.allergies,
    chronicConditions: data.chronicConditions,
    hypertensionMedications: data.hypertensionMedications,
    medications: data.medications,
    medicationTime: data.medicationTime,
    weight: data.weight,
    height: data.height,
    pregnant: data.pregnant,
    lastUpdated: Date.now(),
    showTestData: data.showTestData,
    patientZip: data.patientZip,
    registrationCompleted: data.registrationCompleted,
  }

  Object.keys(updateFields).forEach(key => {
    if (updateFields[key] === undefined) {
      delete updateFields[key]
    }
  })

  const updatedPatient = await patient.findByIdAndUpdate(
    data.id,
    { $set: updateFields },
    { new: true },
  )

  return updatedPatient
}
