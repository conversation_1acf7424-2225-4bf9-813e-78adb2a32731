import { expect, test } from "vitest"
import { withingsBpmStage } from "./withingsBpm.mjs"

const dataStage = JSON.stringify([
  {
    $lookup: {
      from: "withings_bpms",
      let: {
        devices: "$withingsBpDevices.deviceIds",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $in: [
                "$deviceId",
                {
                  $cond: {
                    if: { $isArray: "$$devices" },
                    then: "$$devices",
                    else: [],
                  },
                },
              ],
            },
          },
        },
        { $sort: { created: -1 } },
      ],
      as: "withingsBpm",
    },
  },
])

const overviewStages = JSON.stringify([
  {
    $lookup: {
      from: "withings_bpms",
      let: {
        devices: "$withingsBpDevices.deviceIds",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $in: [
                "$deviceId",
                {
                  $cond: {
                    if: { $isArray: "$$devices" },
                    then: "$$devices",
                    else: [],
                  },
                },
              ],
            },
          },
        },
        { $sort: { created: -1 } },
        { $limit: 1 },
      ],
      as: "withingsBpm",
    },
  },
  {
    $lookup: {
      from: "withings_bpms",
      let: {
        devices: "$withingsBpDevices.deviceIds",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                {
                  $in: [
                    "$deviceId",
                    {
                      $cond: {
                        if: { $isArray: "$$devices" },
                        then: "$$devices",
                        else: [],
                      },
                    },
                  ],
                },
                { $gte: ["$created", 100] },
              ],
            },
          },
        },
        { $count: "withingsRecentBpm" },
      ],
      as: "withingsRecentBpm",
    },
  },
  {
    $unwind: {
      path: "$withingsRecentBpm",
      preserveNullAndEmptyArrays: true,
    },
  },
])

test("bodyTraceBpmStage returns correct data stages", () => {
  expect(JSON.stringify(withingsBpmStage(0, false))).toEqual(dataStage)
})

test("bodyTraceBpmStage returns correct overview stages", () => {
  expect(JSON.stringify(withingsBpmStage(100, true))).toEqual(overviewStages)
})
