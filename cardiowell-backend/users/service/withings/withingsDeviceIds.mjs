// Get array of Withings device id(s) tied to a patient
export const withingsDeviceIdsStage = () => [
  {
    $lookup: {
      from: "withings_user_datas",
      let: {
        id: { $toString: "$_id" },
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$$id", "$patientId"] } } },
        {
          $project: {
            _id: 0,
            deviceIds: { $first: "$devices.deviceId" },
          },
        },
        { $limit: 1 },
      ],
      as: "withingsBpDevices",
    },
  },
  {
    $unwind: {
      path: "$withingsBpDevices",
      preserveNullAndEmptyArrays: true,
    },
  },
]
