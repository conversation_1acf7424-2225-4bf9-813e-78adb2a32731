import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const withingsBpmStage = (unixTime, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ isTest: { $ne: true } }]
  return [
    {
      $lookup: {
        from: "withings_bpms",
        localField: "withingsBpDevices.deviceIds",
        foreignField: "deviceId",
        pipeline: [
          { $match: { $and: [{ deviceId: { $ne: null } }, ...testFilter] } },
          { $sort: { created: -1 } },
          ...(isOverview ? [{ $limit: 1 }] : []),
        ],
        as: "withingsBpm",
      },
    },
    ...(isOverview
      ? [
          {
            $lookup: {
              from: "withings_bpms",
              localField: "withingsBpDevices.deviceIds",
              foreignField: "deviceId",
              pipeline: [
                {
                  $match: {
                    $and: [
                      { deviceId: { $ne: null } },
                      { created: { $gte: unixTime } },
                      ...testFilter,
                    ],
                  },
                },
                { $count: "withingsRecentBpm" },
              ],
              as: "withingsRecentBpm",
            },
          },
          {
            $unwind: {
              path: "$withingsRecentBpm",
              preserveNullAndEmptyArrays: true,
            },
          },
          ...daysWithReadingsLookup({
            collection: "withings_bpms",
            localField: "withingsBpDevices.deviceIds",
            foreignField: "deviceId",
            timestampInput: { $multiply: ["$created", 1000] },
            matchExpression: {
              $and: [
                { deviceId: { $ne: null } },
                { created: { $gte: unixTime } },
                ...testFilter,
              ],
            },
            alias: "withingsBpmDaysWithReadings",
          }),
        ]
      : []),
  ]
}
