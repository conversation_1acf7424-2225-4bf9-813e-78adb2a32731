import sgMail from "@sendgrid/mail"

sgMail.setApi<PERSON>ey(process.env.sendgridAPI)

export const sendEmail = async ({ recipientEmail, subject, text, html }) => {
  const msg = {
    to: recipientEmail.toLowerCase(),
    from: "<EMAIL>",
    subject,
    text,
    html,
  }

  return sgMail
    .send(msg)
    .then(() => {
      console.log("Message Sent")
      return true
    })
    .catch(error => {
      console.error(error)
      return false
    })
}
