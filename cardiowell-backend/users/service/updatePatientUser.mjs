import mongoose from "mongoose"

mongoose.connect(process.env.mongoUri, {
  useUnifiedTopology: true,
  useNewUrlParser: true,
  useFindAndModify: false,
})
mongoose.Promise = global.Promise
const db = mongoose.connection

export const updatePatientUser = async patient => {
  if (!patient?.email) {
    return null
  }
  const appUser = db.collection("_User")
  const patientUser = await appUser.findOneAndUpdate(
    { email: patient.email.toLowerCase() },
    {
      $set: {
        firstname: patient.firstName,
        lastname: patient.lastName,
        email: patient.email.toLowerCase(),
        IMEI_BPM: patient.bpIMEI,
        IMEI_WS: patient.weightIMEI,
      },
    },
  )
  return patientUser
}
