import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const transtekBpmStage = (unixTime, isOverview, isTest) => {
  const testFilterExpr = isTest ? [] : [{ isTest: { $ne: true } }]

  return [
    {
      $lookup: {
        from: "Transtek_BPM",
        localField: "ttBpIMEI",
        foreignField: "imei",
        pipeline: [
          { $match: { $and: [{ imei: { $ne: null } }, ...testFilterExpr] } },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 1 }] : []),
        ],
        as: "ttBpm",
      },
    },
    ...(isOverview
      ? [
          {
            $lookup: {
              from: "Transtek_BPM",
              localField: "ttBpIMEI",
              foreignField: "imei",
              pipeline: [
                {
                  $match: {
                    $and: [
                      { imei: { $ne: null } },
                      { ts: { $gte: unixTime } },
                      ...testFilterExpr,
                    ],
                  },
                },
                { $count: "ttRecentBpms" },
              ],
              as: "ttRecentBpms",
            },
          },
          {
            $unwind: {
              path: "$ttRecentBpms",
              preserveNullAndEmptyArrays: true,
            },
          },
          ...daysWithReadingsLookup({
            collection: "Transtek_BPM",
            localField: "ttBpIMEI",
            foreignField: "imei",
            timestampInput: { $multiply: ["$ts", 1000] },
            matchExpression: {
              $and: [
                { imei: { $ne: null } },
                { ts: { $gte: unixTime } },
                ...testFilterExpr,
              ],
            },
            alias: "ttBpmDaysWithReadings",
          }),
        ]
      : []),
  ]
}
