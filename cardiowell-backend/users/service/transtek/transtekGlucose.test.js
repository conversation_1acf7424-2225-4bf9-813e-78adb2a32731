import { expect, test } from "vitest"
import { transtekGlucoseStage } from "./transtekGlucose.mjs"

const dataStage = JSON.stringify([
  {
    $lookup: {
      from: "CellularBloodGlucoseData",
      let: {
        imei: "$glucoseIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { ts: -1 } },
      ],
      as: "glucose",
    },
  },
])

const overviewStages = JSON.stringify([
  {
    $lookup: {
      from: "CellularBloodGlucoseData",
      let: {
        imei: "$glucoseIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { ts: -1 } },
        { $limit: 1 },
      ],
      as: "glucose",
    },
  },
  {
    $lookup: {
      from: "CellularBloodGlucoseData",
      let: {
        imei: "$glucoseIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [{ $eq: ["$imei", "$$imei"] }, { $gte: ["$ts", 100] }],
            },
          },
        },
        { $count: "recentGlucose" },
      ],
      as: "recentGlucose",
    },
  },
  {
    $unwind: {
      path: "$recentGlucose",
      preserveNullAndEmptyArrays: true,
    },
  },
])

test("bodyTraceBpmStage returns correct data stages", () => {
  expect(JSON.stringify(transtekGlucoseStage(0, false))).toEqual(dataStage)
})

test("bodyTraceBpmStage returns correct overview stages", () => {
  expect(JSON.stringify(transtekGlucoseStage(100, true))).toEqual(overviewStages)
})
