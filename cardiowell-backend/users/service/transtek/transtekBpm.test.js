import { expect, test } from "vitest"
import { transtekBpmStage } from "./transtekBpm.mjs"

const dataStage = JSON.stringify([
  {
    $lookup: {
      from: "Transtek_BPM",
      let: {
        imei: "$ttBpIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { ts: -1 } },
      ],
      as: "ttBpm",
    },
  },
])

const overviewStages = JSON.stringify([
  {
    $lookup: {
      from: "Transtek_BPM",
      let: {
        imei: "$ttBpIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { ts: -1 } },
        { $limit: 1 },
      ],
      as: "ttBpm",
    },
  },
  {
    $lookup: {
      from: "Transtek_BPM",
      let: {
        imei: "$ttBpIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [{ $eq: ["$imei", "$$imei"] }, { $gte: ["$ts", 100] }],
            },
          },
        },
        { $count: "ttRecentBpms" },
      ],
      as: "ttRecentBpms",
    },
  },
  {
    $unwind: {
      path: "$ttRecentBpms",
      preserveNullAndEmptyArrays: true,
    },
  },
])

test("bodyTraceBpmStage returns correct data stages", () => {
  expect(JSON.stringify(transtekBpmStage(0, false))).toEqual(dataStage)
})

test("bodyTraceBpmStage returns correct overview stages", () => {
  expect(JSON.stringify(transtekBpmStage(100, true))).toEqual(overviewStages)
})
