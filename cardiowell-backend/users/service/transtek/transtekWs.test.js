import { expect, test } from "vitest"
import { transtekWsStage } from "./transtekWs.mjs"

const dataStage = JSON.stringify([
  {
    $lookup: {
      from: "Transtek_WS",
      let: {
        imei: "$ttWeightIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { ts: -1 } },
      ],
      as: "ttWs",
    },
  },
])

const overviewStages = JSON.stringify([
  {
    $lookup: {
      from: "Transtek_WS",
      let: {
        imei: "$ttWeightIMEI",
      },
      pipeline: [
        { $match: { $expr: { $eq: ["$imei", "$$imei"] } } },
        { $sort: { ts: -1 } },
        { $limit: 1 },
      ],
      as: "ttWs",
    },
  },
  {
    $lookup: {
      from: "Transtek_WS",
      let: {
        imei: "$ttWeightIMEI",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [{ $eq: ["$imei", "$$imei"] }, { $gte: ["$ts", 100] }],
            },
          },
        },
        { $count: "ttRecentWs" },
      ],
      as: "ttRecentWs",
    },
  },
  {
    $unwind: {
      path: "$ttRecentWs",
      preserveNullAndEmptyArrays: true,
    },
  },
])

test("bodyTraceBpmStage returns correct data stages", () => {
  expect(JSON.stringify(transtekWsStage(0, false))).toEqual(dataStage)
})

test("bodyTraceBpmStage returns correct overview stages", () => {
  expect(JSON.stringify(transtekWsStage(100, true))).toEqual(overviewStages)
})
