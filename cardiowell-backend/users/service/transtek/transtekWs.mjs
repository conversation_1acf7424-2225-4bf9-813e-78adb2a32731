import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const transtekWsStage = (unixTime, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ isTest: { $ne: true } }]

  return [
    {
      $lookup: {
        from: "Transtek_WS",
        localField: "ttWeightIMEI",
        foreignField: "imei",
        pipeline: [
          { $match: { $and: [{ imei: { $ne: null } }, ...testFilter] } },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 2 }] : []),
        ],
        as: "ttWs",
      },
    },
    ...(isOverview
      ? [
          {
            $lookup: {
              from: "Transtek_WS",
              localField: "ttWeightIMEI",
              foreignField: "imei",
              pipeline: [
                {
                  $match: {
                    $and: [
                      { imei: { $ne: null } },
                      { ts: { $gte: unixTime } },
                      ...testFilter,
                    ],
                  },
                },
                { $count: "ttRecentWs" },
              ],
              as: "ttRecentWs",
            },
          },
          {
            $unwind: {
              path: "$ttRecentWs",
              preserveNullAndEmptyArrays: true,
            },
          },
          ...daysWithReadingsLookup({
            collection: "Transtek_WS",
            localField: "ttWeightIMEI",
            foreignField: "imei",
            timestampInput: { $multiply: ["$ts", 1000] },
            matchExpression: {
              $and: [{ imei: { $ne: null } }, { ts: { $gte: unixTime } }, ...testFilter],
            },
            alias: "ttWsDaysWithReadings",
          }),
        ]
      : []),
  ]
}
