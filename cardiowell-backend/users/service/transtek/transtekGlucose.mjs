import { daysWithReadingsLookup } from "../daysWithReadingsLookup.mjs"

export const transtekGlucoseStage = (unixTime, isOverview, isTest) => {
  const testFilter = isTest ? [] : [{ isTest: { $ne: true } }]

  return [
    {
      $lookup: {
        from: "CellularBloodGlucoseData",
        localField: "glucoseIMEI",
        foreignField: "imei",
        pipeline: [
          { $match: { $and: [{ imei: { $ne: null } }, ...testFilter] } },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 1 }] : []),
        ],
        as: "glucose",
      },
    },
    ...(isOverview
      ? [
          {
            $lookup: {
              from: "CellularBloodGlucoseData",
              localField: "glucoseIMEI",
              foreignField: "imei",
              pipeline: [
                {
                  $match: {
                    $and: [
                      { imei: { $ne: null } },
                      { ts: { $gte: unixTime } },
                      ...testFilter,
                    ],
                  },
                },
                { $count: "recentGlucose" },
              ],
              as: "recentGlucose",
            },
          },
          {
            $unwind: {
              path: "$recentGlucose",
              preserveNullAndEmptyArrays: true,
            },
          },
          ...daysWithReadingsLookup({
            collection: "CellularBloodGlucoseData",
            localField: "glucoseIMEI",
            foreignField: "imei",
            timestampInput: { $multiply: ["$ts", 1000] },
            matchExpression: {
              $and: [{ imei: { $ne: null } }, { ts: { $gte: unixTime } }, ...testFilter],
            },
            alias: "glucoseDaysWithReadings",
          }),
        ]
      : []),
  ]
}
