export const getTimeZone = inputTimeZone => {
  var timeZone = inputTimeZone
  if (inputTimeZone === "AST") {
    timeZone = "America/Puerto_Rico"
  } else if (inputTimeZone === "EST") {
    timeZone = "America/New_York"
  } else if (inputTimeZone === "CST") {
    timeZone = "America/Phoenix"
  } else if (inputTimeZone === "MST") {
    timeZone = "America/Denver"
  } else if (inputTimeZone === "PST") {
    timeZone = "America/Los_Angeles"
  } else if (inputTimeZone === "AKST") {
    timeZone = "America/Juneau"
  } else if (inputTimeZone === "SAST") {
    timeZone = "Asia/Riyadh"
  } else if (inputTimeZone === "PKT") {
    timeZone = "Asia/Karachi"
  } else if (inputTimeZone === "GST") {
    timeZone = "Asia/Dubai"
  } else if (inputTimeZone === "HST") {
    timeZone = "HST"
  } else if (inputTimeZone === "UTC") {
    timeZone = "UTC"
  }

  return timeZone
}
