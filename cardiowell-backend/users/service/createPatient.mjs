import patient from "../../models/patient.js"

export const createPatient = async data => {
  const patientDoc = new patient({
    firstName: data.patientFirstName,
    lastName: data.patientLastName,
    MRN: data.patientMRN,
    email: data.email,
    cellNumber: data.patientCellNumber,
    homeNumber: data.patientHomeNumber,
    city: data.patientCity,
    state: data.patientState,
    address: data.patientAddress,
    zip: data.zip,
    timeZone: data.patientTimeZone,
    clinic: data.patientClinic,
    bpIMEI: data.patientBPIMEI,
    ttBpIMEI: data.patientTTBpIMEI,
    adBpIMEI: data.patientAdBpIMEI,
    weightIMEI: data.patientWeightIMEI,
    ttWeightIMEI: data.patientTTWeightIMEI,
    pulseIMEI: data.patientPulseIMEI,
    glucoseIMEI: data.patientGlucoseIMEI,
    selectedBpDevice: data.selectedBpDevice,
    selectedWeightDevice: data.selectedWeightDevice,
    deviceNotificationsEnabled: data.deviceNotificationsEnabled,
    targetWeight: data.targetWeight,
    birthdate: data.birthdate,
    gender: data.gender,
    ethnicity: data.ethnicity,
    maritalStatus: data.maritalStatus,
    education: data.education,
    height: data.height,
    employment: data.employment,
    income: data.income,
    language: data.language,
    socialConnectedness: data.socialConnectedness,
    allergies: data.allergies,
    chronicConditions: data.chronicConditions,
    hypertensionMedications: data.hypertensionMedications,
    medications: data.medications,
    medicationTime: data.medicationTime,
    weight: data.weight,
    showTestData: data.showTestData,
    registrationCompleted: data.registrationCompleted || false,
  })
  const patientModel = await patientDoc.save()
  return patientModel
}
