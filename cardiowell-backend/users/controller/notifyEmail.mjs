import { body, validationResult, matchedData } from "express-validator"
import Provider from "../../models/provider.js"
import { sendEmail } from "../service/sendEmail.mjs"

export const notifyEmailValidator = [
  body("email").isEmail(),
  body("username").isString().optional(),
]

export const notifyEmail = async (request, response) => {
  try {
    validationResult(request).throw()
    const { email, username } = matchedData(request)
    const provider = await Provider.findOne({
      username: username.toLowerCase(),
    })

    if (!provider) {
      return response.status(403).send({ message: "Provider not found" })
    }

    const sent = await sendEmail({
      recipientEmail: email,
      subject: process.env.emailSubject,
      html: "<div>" + process.env.emailMessage + "</div>",
    })
    if (sent) {
      return response.status(201).send({ message: "Success" })
    }
    return response.status(500).send({ message: "Error sending email" })
  } catch (err) {
    return response.status(500).json(err)
  }
}

export const iFrameNotifyEmail = async (request, response) => {
  try {
    validationResult(request).throw()
    const { email } = matchedData(request)
    const sent = await sendEmail({
      recipientEmail: email,
      subject: process.env.emailSubject,
      html: "<div>" + process.env.emailMessage + "</div>",
    })
    if (sent) {
      return response.status(201).send({ message: "Success" })
    }
    return response.status(500).send({ message: "Error sending email" })
  } catch (err) {
    return response.status(500).json(err)
  }
}
