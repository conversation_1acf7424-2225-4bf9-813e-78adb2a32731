import Patient from "../../models/patient.js"
import { validationResult } from "express-validator"

export const getSpecificPatient = async (req, res) => {
  validationResult(req).throw()
  const patient = await Patient.findOne({ bpIMEI: req.params.imei })
  return res.status(201).send({ message: "Success", patient })
}

export const getPatientRegistrationStatus = async (req, res) => {
  try {
    const { patientId } = req.params

    const patient = await Patient.findById(patientId, "registrationCompleted")

    if (!patient) {
      return res.status(404).json({
        error: "patient-not-found",
        message: "Patient not found",
      })
    }

    return res.status(200).json({
      registrationCompleted: patient.registrationCompleted || false,
    })
  } catch (error) {
    console.error("Error getting patient registration status:", error)
    return res.status(500).json({
      error: "server-error",
      message: "Internal server error",
    })
  }
}
