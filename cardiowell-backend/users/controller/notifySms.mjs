import { body, validationResult, matchedData } from "express-validator"
import Provider from "../../models/provider.js"
import { sendText } from "../service/sendText.mjs"

export const notifySmsValidator = [
  body("number")
    .isMobilePhone("any", { strictMode: false })
    .withMessage("Invalid phone number format"),
  body("username").isString().optional(),
]

export const notifySms = async (request, response) => {
  try {
    validationResult(request).throw()
    let { number, username } = matchedData(request)
    const provider = await Provider.findOne({
      username: username.toLowerCase(),
    })

    if (!provider) {
      return response.status(403).send({ message: "Provider not found" })
    }

    // Ensure the number starts with +1 for US phone numbers
    if (!number.startsWith("+1") && number.length >= 10) {
      number = "+1" + number.replace(/^\+/, "")
    }

    sendText(number, process.env.smsMessage)
    return response.status(201).send({ message: "Success" })
  } catch (err) {
    console.error("SMS Error:", err)
    return response.status(500).json(err)
  }
}

export const iFrameNotifySms = async (request, response) => {
  try {
    validationResult(request).throw()
    let { number } = matchedData(request)

    // Ensure the number starts with +1 for US phone numbers
    if (!number.startsWith("+1") && number.length >= 10) {
      number = "+1" + number.replace(/^\+/, "")
    }

    sendText(number, process.env.smsMessage)
    return response.status(201).send({ message: "Success" })
  } catch (err) {
    console.error("SMS Error:", err)
    return response.status(500).json(err)
  }
}
