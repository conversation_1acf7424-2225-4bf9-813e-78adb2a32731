import Patient from "../../models/patient.js"

export const updatePatientRegistrationStatus = async (req, res) => {
  try {
    const { patientId } = req.params
    const { registrationCompleted } = req.body

    if (typeof registrationCompleted !== "boolean") {
      return res.status(400).json({
        error: "invalid-data",
        message: "registrationCompleted must be a boolean value",
      })
    }

    const updatedPatient = await Patient.findByIdAndUpdate(
      patientId,
      {
        $set: {
          registrationCompleted: registrationCompleted,
          lastUpdated: Date.now(),
        },
      },
      { new: true },
    )

    if (!updatedPatient) {
      return res.status(404).json({
        error: "patient-not-found",
        message: "Patient not found",
      })
    }

    return res.status(200).json({
      message: "Registration status updated successfully",
      registrationCompleted: updatedPatient.registrationCompleted,
    })
  } catch (error) {
    console.error("Error updating patient registration status:", error)
    return res.status(500).json({
      error: "server-error",
      message: "Internal server error",
    })
  }
}
