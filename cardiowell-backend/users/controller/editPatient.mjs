import { body, validationResult, matchedData } from "express-validator"
import { updatePatient } from "../service/updatePatient.mjs"
import { updatePatientUser } from "../service/updatePatientUser.mjs"
import {
  getPatientOverviewById,
  getPatientsForClinic,
  getPatientDataById,
} from "../service/patientData.mjs"
import { isValidDate } from "../../device/controller/isValidDate.mjs"
import patient from "../../models/patient.js"

export const updatePatientValidator = [
  body("id").isString().optional(),
  body("clinic").isString().optional(),
  body("cellNumber").isString().optional(),
  body("homeNumber").isString().optional(),
  body("firstName").isString().optional(),
  body("lastName").isString().optional(),
  body("email").isString().optional(),
  body("MRN").isString().optional(),
  body("city").isString().optional(),
  body("state").isString().optional(),
  body("country").isString().optional(),
  body("address").isString().optional(),
  body("zip").isString().optional(),
  body("timeZone").isString().optional(),
  body("bpIMEI").isString().optional(),
  body("ttBpIMEI").isString().optional(),
  body("adBpIMEI").isString().optional(),
  body("weightIMEI").isString().optional(),
  body("ttWeightIMEI").isString().optional(),
  body("glucoseIMEI").isString().optional(),
  body("pulseIMEI").isString().optional(),
  body("selectedBpDevice").isString().optional(),
  body("selectedWeightDevice").isString().optional(),
  body("deviceNotificationsEnabled").isBoolean().optional(),
  body("deviceNotificationsEnabled").isBoolean().optional(),
  body("targetWeight").isString().optional(),
  body("birthdate").optional().custom(isValidDate),
  body("gender").isString().optional(),
  body("ethnicity").isString().optional(),
  body("maritalStatus").isString().optional(),
  body("education").isString().optional(),
  body("employment").isString().optional(),
  body("income").isString().optional(),
  body("language").isString().optional(),
  body("socialConnectedness").isString().optional(),
  body("allergies").isArray().optional(),
  body("chronicConditions").isArray().optional(),
  body("hypertensionMedications").isArray().optional(),
  body("medications").isArray().optional(),
  body("medicationTime").isArray().optional(),
  body("medicationTime.*.medication").isString().optional(),
  body("medicationTime.*.time").isString().optional(),
  body("height").isNumeric().optional(),
  body("weight").isNumeric().optional(),
  body("address").isString().optional(),
  body("city").isString().optional(),
  body("state").isString().optional(),
  body("pregnant").isBoolean().optional(),
  body("showTestData").isBoolean().optional(),
]

export const mergePatientData = async (patientId, newData) => {
  const existingPatient = await patient.findById(patientId)
  if (!existingPatient) {
    throw new Error("Patient not found")
  }

  const allowedFields = [
    "firstName",
    "lastName",
    "cellNumber",
    "homeNumber",
    "email",
    "MRN",
    "city",
    "state",
    "country",
    "address",
    "zip",
    "timeZone",
    "bpIMEI",
    "ttBpIMEI",
    "adBpIMEI",
    "weightIMEI",
    "ttWeightIMEI",
    "glucoseIMEI",
    "pulseIMEI",
    "selectedBpDevice",
    "selectedWeightDevice",
    "deviceNotificationsEnabled",
    "targetWeight",
    "birthdate",
    "gender",
    "ethnicity",
    "maritalStatus",
    "education",
    "employment",
    "income",
    "language",
    "allergies",
    "chronicConditions",
    "hypertensionMedications",
    "medications",
    "medicationTime",
    "weight",
    "height",
    "pregnant",
    "patientZip",
    "showTestData",
    "socialConnectedness",
    "lastUpdated",
  ]

  const arrayFields = [
    "allergies",
    "chronicConditions",
    "hypertensionMedications",
    "medications",
    "medicationTime",
  ]

  const booleanFields = ["deviceNotificationsEnabled", "showTestData", "pregnant"]

  const numericFields = ["height", "weight", "targetWeight"]

  const updatedData = {
    id: patientId,
    lastUpdated: Date.now(),
    ...existingPatient.toObject(),
    ...Object.fromEntries(
      allowedFields
        .filter(field => newData[field] !== undefined)
        .map(field => {
          if (field === "email") {
            return [field, newData[field]?.toLowerCase() || ""]
          }

          if (booleanFields.includes(field)) {
            return [field, newData[field] === true]
          }

          if (arrayFields.includes(field)) {
            const newValue = Array.isArray(newData[field]) ? newData[field] : []
            const existingValue = Array.isArray(existingPatient[field])
              ? existingPatient[field]
              : []
            // Fix: Allow empty arrays to be saved (don't fallback to existing value)
            // Only fallback to existing value if newData[field] is undefined/null
            return [field, newData[field] !== undefined ? newValue : existingValue]
          }

          if (numericFields.includes(field)) {
            const value = newData[field]?.toString() || ""
            return [field, value]
          }

          if (field === "lastUpdated") {
            return [field, Date.now()]
          }

          return [field, newData[field] ?? existingPatient[field] ?? ""]
        }),
    ),
  }

  return updatedData
}

export const adminSavePatientChanges = async (req, res) => {
  try {
    validationResult(req).throw()
    const updatedPatient = await updatePatient(matchedData(req), true)
    if (!updatedPatient) {
      return res.status(404).send({ message: "Patient not found" })
    }

    await updatePatientUser(updatedPatient)
    return res.status(201).send({ message: "Success", patient: updatedPatient })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
}

export const patientSavePatientChanges = async (req, res) => {
  try {
    validationResult(req).throw()
    const data = matchedData(req)
    const mergedPatientData = await mergePatientData(data.id, data)
    const updatedPatient = await updatePatient(mergedPatientData, false)
    if (!updatedPatient) {
      return res.status(404).send({ message: "Patient not found" })
    }

    await updatePatientUser(updatedPatient)
    const updatedPatientData = await getPatientDataById(data.id)
    return res
      .status(201)
      .send({ message: "Success", data: updatedPatientData.patientObj })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
}

export const providerSavePatientChanges = async (req, res) => {
  try {
    validationResult(req).throw()
    const data = matchedData(req)
    const mergedPatientData = await mergePatientData(data.id, data)
    const updatedPatient = await updatePatient(mergedPatientData, false)
    if (!updatedPatient) {
      return res.status(404).send({ message: "Patient not found" })
    }

    await updatePatientUser(updatedPatient)
    const overview = await getPatientOverviewById(data.id)
    return res.status(201).send({ message: "Success", data: overview.patientObj })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
}

export const iFrameSavePatientChanges = async (req, res) => {
  try {
    validationResult(req).throw()
    const updatedPatient = await updatePatient(matchedData(req), false)
    const patientArray = await getPatientsForClinic(updatedPatient.clinic)
    return res.status(201).send({ message: "Success", data: patientArray })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
}
