import { body, matchedData, validationResult } from "express-validator"
import Provider from "../../models/provider.js"
import Patient from "../../models/patient.js"
import { sendEmail } from "../service/sendEmail.mjs"
import { passwordResetEmailHtml } from "../service/passwordResetEmailHtml.mjs"

export const passwordResetValidator = [body("email").isEmail()]

export const sendPasswordResetLink = async (req, res) => {
  try {
    validationResult(req).throw()
    const { email } = matchedData(req)
    const providerUser = await Provider.findOne({ email: email.toLowerCase() })
    if (providerUser) {
      sendEmail({
        recipientEmail: email.toLowerCase(),
        subject: "Cardiowell Reset Password",
        html: passwordResetEmailHtml(providerUser),
      })
      return res.status(201).send({ message: "Success" })
    }

    const patientUser = await Patient.findOne({ email: email.toLowerCase() })
    if (patientUser) {
      sendEmail({
        recipientEmail: email.toLowerCase(),
        subject: "Cardiowell Reset Password",
        html: passwordResetEmailHtml(patientUser),
      })
      return res.status(201).send({ message: "Success" })
    }
    return res.status(404).send({ message: "User not found" })
  } catch (err) {
    return res.status(500).json(err)
  }
}
