import { body, matchedData, validationResult } from "express-validator"
import { getPatientOverviewById, getPatientsForClinic } from "../service/patientData.mjs"
import { createPatient } from "../service/createPatient.mjs"
import { isValidDate } from "../../device/controller/isValidDate.mjs"
import { getPatientByEmail } from "../service/getPatient.mjs"
import { updatePatient } from "../service/updatePatient.mjs"

export const addPatientValidator = [
  body("patientClinic").isString().optional(),
  body("patientFirstName").isString().optional(),
  body("patientLastName").isString().optional(),
  body("patientCellNumber").isString().optional(),
  body("patientHomeNumber").isString().optional(),
  body("patientFirstName").isString().optional(),
  body("patientLastName").isString().optional(),
  body("email").isString().optional(),
  body("patientMRN").isString().optional(),
  body("patientCity").isString().optional(),
  body("patientState").isString().optional(),
  body("patientAddress").isString().optional(),
  body("zip").isString().optional(),
  body("patientTimeZone").isString().optional(),
  body("patientBPIMEI").isString().optional(),
  body("patientTTBpIMEI").isString().optional(),
  body("patientAdBpIMEI").isString().optional(),
  body("patientWeightIMEI").isString().optional(),
  body("patientTTWeightIMEI").isString().optional(),
  body("patientGlucoseIMEI").isString().optional(),
  body("patientPulseIMEI").isString().optional(),
  body("selectedBpDevice").isString().optional(),
  body("height").isString().optional(),
  body("weight").isString().optional(),
  body("selectedWeightDevice").isString().optional(),
  body("deviceNotificationsEnabled").isBoolean().optional(),
  body("targetWeight").isString().optional(),
  body("birthdate").optional().custom(isValidDate),
  body("gender").isString().optional(),
  body("ethnicity").isString().optional(),
  body("maritalStatus").isString().optional(),
  body("education").isString().optional(),
  body("employment").isString().optional(),
  body("income").isString().optional(),
  body("language").isString().optional(),
  body("socialConnectedness").isString().optional(),
  body("allergies").isArray().optional(),
  body("chronicConditions").isArray().optional(),
  body("hypertensionMedications").isArray().optional(),
  body("medications").isArray().optional(),
  body("medicationTime").isArray().optional(),
  body("medicationTime.*.medication").isString().optional(),
  body("medicationTime.*.time").isString().optional(),
]

export const adminAddPatient = async (req, res) => {
  try {
    validationResult(req).throw()
    const data = matchedData(req)
    const newPatient = await createPatient(data)
    return res.status(201).send({ message: "Success", patient: newPatient })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
}

export const providerAddPatient = async (req, res) => {
  try {
    validationResult(req).throw()
    let newPatient
    const data = matchedData(req)
    const patient = await getPatientByEmail(data.email)
    if (patient) {
      data.id = patient.id
      newPatient = await updatePatient(data, false)
    } else {
      newPatient = await createPatient(data)
    }
    const patientOverview = await getPatientOverviewById(newPatient._id)
    return res.status(201).send({ message: "Success", data: patientOverview.patientObj })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
}

export const iFrameAddPatient = async (req, res) => {
  try {
    validationResult(req).throw()
    const data = matchedData(req)
    await createPatient(data)
    const patientArray = await getPatientsForClinic(data.patientClinic)
    return res.status(201).send({ message: "Success", data: patientArray })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
}
