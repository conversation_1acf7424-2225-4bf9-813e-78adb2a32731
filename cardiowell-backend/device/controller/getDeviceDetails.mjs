import * as service from "../service/getDeviceDetails.mjs"

export const getDeviceDetails = async function (req, res, next) {
  try {
    const { id } = req.params
    const device = await service.getDeviceDetails({ id })

    if (!device) {
      res.status(404).send({
        error: "device-not-found",
        message: "This id does not exist in any device in our records",
      })
    } else {
      res.status(200).json(device)
    }
  } catch (err) {
    console.log(err)
    res.status(400).json(err)
  }
}
