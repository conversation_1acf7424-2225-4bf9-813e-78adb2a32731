import { z } from "zod"
import * as service from "../service/testDataUploads/loadTestData.mjs"
import { parseCsvFile } from "../../utils/parseCsvFile.mjs"
import fs from "fs"

const requestSchema = z.object({
  file: z.instanceof(Object, { message: "File is required" }),
  imei: z.string().min(1, "IMEI is required"),
})

export const loadTestData = async function (req, res, next) {
  let filePath
  try {
    filePath = req.file.path
    const { imei } = req.params

    const validationResult = requestSchema.safeParse({
      file: req.file,
      imei,
    })

    if (!validationResult.success) {
      console.error("Validation Error:", validationResult.error.format())
      return res.status(400).json({ error: validationResult.error.format() })
    }

    const data = await parseCsvFile(req.file.path)

    await service.loadTestData(data, imei)

    res.json({ message: "Loaded", file: req.file })
  } catch (err) {
    console.error(err)
    return res.status(500).json({ err })
  } finally {
    if (filePath) {
      fs.unlink(filePath, err => {
        if (err) console.error("Error deleting file:", err)
      })
    }
  }
}
