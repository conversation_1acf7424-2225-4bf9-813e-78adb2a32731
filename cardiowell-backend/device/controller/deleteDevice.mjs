import * as deviceService from "../service/deleteDevice.mjs"
import { param, validationResult, matchedData } from "express-validator"

export const deleteDeviceValidator = [param("id").notEmpty().escape()]

export const deleteDevice = async function (req, res, next) {
  try {
    validationResult(req).throw()

    const { id } = matchedData(req)
    await deviceService.deleteDevice(id)

    res.status(201).send()
  } catch (err) {
    console.log("Error while createing the customer", err)
    res.status(400).json(err)
  }
}
