import * as service from "../service/getDevices.mjs"
import mongoose from "mongoose"
import Patient from "../../models/patient.js"

const DEFAULT_PAGE_SIZE = 10

export const getDevices = async function (req, res, next) {
  try {
    let {
      page = 0,
      pageSize = DEFAULT_PAGE_SIZE,
      sort,
      filterValue,
      filterProps,
    } = req.query
    const skip = page * pageSize
    let [sortField, sortType] = sort?.split(":") || []
    let [filterField, filterOperator] = filterProps?.split(":") || []

    // Role-based filtering
    if (req.user?.role === "patient") {
      // Find patient by id (support both _id and id)
      const patientId = req.user._id || req.user.id
      const patient = await Patient.findById(patientId).lean()
      if (!patient) {
        return res.status(403).json({ error: "patient-not-found" })
      }
      // Collect all IMEIs from patient fields
      const imeiFields = [
        "bpIMEI",
        "ttBpIMEI",
        "adBpIMEI",
        "weightIMEI",
        "ttWeightIMEI",
        "glucoseIMEI",
        "pulseIMEI",
      ]
      const imeis = imeiFields.map(f => patient[f]).filter(Boolean)
      if (imeis.length === 0) {
        // No devices for this patient
        return res
          .status(200)
          .json({ devices: [], pageInfo: { page, pageSize, totalRowCount: 0 } })
      }
      filterField = "imei"
      filterOperator = "isAnyOf"
      filterValue = imeis
    }
    // For admin: no extra filter, see all

    const result = await service.getDevices({
      skip,
      sortField,
      sortType,
      filterField,
      filterOperator,
      filterValue,
      pageSize,
    })

    res.status(200).json({
      devices: result.devices,
      pageInfo: {
        page,
        pageSize,
        totalRowCount: result?.info[0]?.totalRowCount || 0,
      },
    })
  } catch (err) {
    console.log(err)
    res.status(400).json(err)
  }
}
