import { body, validationResult, matchedData } from "express-validator"
import { isValidDate } from "./isValidDate.mjs"
import * as service from "../service/createDevice.mjs"

export const bulkCreateDevicesValidator = [
  body("*.id").notEmpty().escape(),
  body("*.device.type").notEmpty().escape(),
  body("*.device.manufacturer").optional().escape(),
  body("*.device.identifier").escape(),
  body("*.device.deviceId").escape(),
  body("*.device.customer").optional().escape(),
  body("*.device.endpoint").optional().escape(),
  body("*.device.clinic").optional().escape(),
  body("*.device.billingStart").optional().custom(isValidDate),
  body("*.device.billingEnd").optional().custom(isValidDate),
  body("*.device.isTest").optional().escape(),
]

function* getIterable(bulkCreateDevicesRequest) {
  for (let key of Object.keys(bulkCreateDevicesRequest)) {
    yield bulkCreateDevicesRequest[key]
  }
}

export const bulkCreateDevices = async function (req, res, next) {
  try {
    validationResult(req).throw()
    const bulkCreateDevicesRequest = matchedData(req)

    const result = []
    for (let createDeviceRequest of getIterable(bulkCreateDevicesRequest)) {
      try {
        const newDevice = await service.createDevice(createDeviceRequest.device)
        result.push({
          status: "successful",
          device: newDevice,
          id: createDeviceRequest.id,
        })
      } catch (error) {
        result.push({ status: "error", error, id: createDeviceRequest.id })
      }
    }

    res.status(201).json(result)
  } catch (err) {
    let message = "Error while createing the device"
    if (err?.code === 11000) {
      message = "Failed to create a device. Device with IMEI already exists."
    }
    console.error(err)
    res.status(400).json({ message })
  }
}
