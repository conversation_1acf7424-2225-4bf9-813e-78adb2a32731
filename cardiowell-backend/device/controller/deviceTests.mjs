import { startDeviceTest } from "../service/startDeviceTest.mjs"
import { stopDeviceTest } from "../service/stopDeviceTest.mjs"
import { param, body, validationResult, matchedData } from "express-validator"

export const deviceTestsValidator = [
  param("id").notEmpty().escape(),
  body("start").optional(),
  body("stop").optional(),
]

export const deviceTests = async function (req, res, next) {
  try {
    validationResult(req).throw()
    const { id, start, stop } = matchedData(req)

    let device = null
    if (start) {
      device = await startDeviceTest({ id })
    } else if (stop) {
      device = await stopDeviceTest({ id })
    }

    if (!device) {
      res.status(404).send({
        error: "device-not-found",
        message: "This id does not exist in any device in our records",
      })
    } else {
      res.status(200).json(device)
    }
  } catch (err) {
    console.log(err)
    res.status(400).json(err)
  }
}
