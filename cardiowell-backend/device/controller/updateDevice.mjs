import { param, body, validationResult, matchedData } from "express-validator"
import { isValidDate } from "./isValidDate.mjs"
import * as service from "../service/updateDevice.mjs"

export const updateDeviceValidator = [
  param("id").notEmpty().escape(),
  body("type").notEmpty().escape(),
  body("manufacturer").optional().escape(),
  body("identifier").notEmpty().escape(),
  body("customer").optional().escape(),
  body("endpoint").optional().escape(),
  body("clinic").optional().escape(),
  body("billingStart").optional().custom(isValidDate),
  body("billingEnd").optional().custom(isValidDate),
  body("isTest").optional().escape(),
]

export const updateDevice = async function (req, res, next) {
  try {
    validationResult(req).throw()

    const updatedDeviceRequest = matchedData(req)
    const updatedDevice = await service.updateDevice(updatedDeviceRequest)

    res.status(201).json(updatedDevice)
  } catch (err) {
    console.log("Error while updating the device", err)
    res.status(400).json(err)
  }
}
