import { z } from "zod"
import * as service from "../service/testDataUploads/deleteTestData.mjs"

const requestSchema = z.object({
  id: z.string().min(1, "id is required"),
})

export const deleteTestData = async function (req, res, next) {
  try {
    const { id } = req.params

    const validationResult = requestSchema.safeParse({
      id,
    })

    if (!validationResult.success) {
      console.error("Validation Error:", validationResult.error.format())
      return res.status(400).json({ error: validationResult.error.format() })
    }

    const result = await service.deleteTestData(id)

    res.status(200).json(result)
  } catch (err) {
    console.error(err)
    return res.status(500).json({ err })
  }
}
