import { body, validationResult, matchedData } from "express-validator"
import { isValidDate } from "./isValidDate.mjs"
import * as service from "../service/createDevice.mjs"

export const createDeviceValidator = [
  body("type").notEmpty().escape(),
  body("manufacturer").optional().escape(),
  body("identifier").escape(),
  body("deviceId").escape(),
  body("customer").optional().escape(),
  body("endpoint").optional().escape(),
  body("clinic").optional().escape(),
  body("billingStart").optional().custom(isValidDate),
  body("billingEnd").optional().custom(isValidDate),
  body("isTest").optional().escape(),
]

export const createDevice = async function (req, res, next) {
  try {
    validationResult(req).throw()

    const createDeviceRequest = matchedData(req)
    const newDevice = service.createDevice(createDeviceRequest)
    res.status(201).json(newDevice)
  } catch (err) {
    let message = "Error while createing the device"
    if (err?.code === 11000) {
      message = "Failed to create a device. Device with IMEI already exists."
    }
    console.error(err)
    res.status(400).json({ message })
  }
}
