import { Device } from "../../models/device.mjs"
import * as transtekBloodPressureLatestMessages from "./transtek/transtekBloodPressureLatestMessages.mjs"
import * as transtekGlucometerLatestMessages from "./transtek/transtekGlucometerLatestMessages.mjs"
import * as bodyTraceLatestMessages from "./bodyTrace/bodyTraceLatestMessages.mjs"
import * as berryLatestMessages from "./berry/berryLatestMessages.mjs"
import * as scaleLatestMessage from "./transtek/scale/latestScaleMessage.mjs"
import { getWithingsBPLatestMessage } from "./withings/getWithingsBPLatestMessage.mjs"
import { transformRequest } from "./transform.mjs"

const isTranstekBloodPressure = request =>
  request.type === "bloodPressure" && request.manufacturer === "transtek"

const isTranstekGlucometer = request =>
  request.type === "glucometer" && request.manufacturer === "transtek"

const isBodyTrace = request => request.manufacturer === "bodyTrace"

const isBerryOximeter = request =>
  request.type === "oximeter" && request.manufacturer === "berryMed"

const isWithingsBloodPressure = request =>
  request.type === "bloodPressure" && request.manufacturer === "withings"

const isTranstekScale = request =>
  request.type === "scale" && request.manufacturer === "transtek"

export const updateDevice = async request => {
  let calculatedFields = {}
  const updateDeviceRequest = transformRequest(request)

  if (isTranstekBloodPressure(updateDeviceRequest)) {
    calculatedFields = await transtekBloodPressureLatestMessages.getCalculatedFields(
      updateDeviceRequest.imei,
    )
  }

  if (isTranstekGlucometer(updateDeviceRequest)) {
    calculatedFields = await transtekGlucometerLatestMessages.getCalculatedFields(
      updateDeviceRequest.imei,
    )
  }

  if (isBodyTrace(updateDeviceRequest)) {
    calculatedFields = await bodyTraceLatestMessages.getCalculatedFields(
      updateDeviceRequest.imei,
    )
  }

  if (isBerryOximeter(updateDeviceRequest)) {
    calculatedFields = await berryLatestMessages.getCalculatedFields(
      updateDeviceRequest.imei,
    )
  }

  if (isTranstekScale(updateDeviceRequest)) {
    calculatedFields = await scaleLatestMessage.getCalculatedFields(
      updateDeviceRequest.imei,
    )
  }

  if (isWithingsBloodPressure(updateDeviceRequest)) {
    calculatedFields = {
      latestMessages: [
        await getWithingsBPLatestMessage(updateDeviceRequest.deviceId),
      ].filter(Boolean),
    }
  }

  const { id, ...updateDeviceData } = updateDeviceRequest

  return Device.findOneAndUpdate(
    { _id: id },
    { ...updateDeviceData, ...calculatedFields },
    { new: true },
  )
}
