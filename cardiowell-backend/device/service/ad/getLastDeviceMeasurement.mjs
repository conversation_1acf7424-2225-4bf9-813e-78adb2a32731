import axios from "axios"
import { refreshToken } from "./refreshToken.mjs"
import { signIn } from "./signIn.mjs"

const getMeasurement = (imei, token, page = 1) =>
  axios.get(`${process.env.AD_API}/measurement/imei/${imei}`, {
    headers: { Authorization: `Bearer ${token}` },
    params: { page },
    validateStatus: function (status) {
      return (status >= 200 && status < 300) || status === 401
    },
  })

const takeLast = async ({ readings, page }) => {
  if (readings && page.totalPages === 1) {
    return readings[readings.length - 1]
  }
  if (page.totalPages > 1) {
    const { data, status } = await getMeasurement(device.imei, token, page.totalPages) // eslint-disable-line no-undef
    if (status < 300 && data?.readings) {
      return readings[readings.length - 1]
    }
  }
  throw Error(`Failed to get the last measurement for ${device?.imei}`) // eslint-disable-line no-undef
}

export const getLastDeviceMeasurement = async device => {
  let { IdToken: token, RefreshToken: rfToken } = device?.session || {}

  if (!token) {
    const newSession = await signIn()
    token = newSession.IdToken
    rfToken = newSession.RefreshToken
  }

  const { data, status } = await getMeasurement(device.imei, token)

  if (status < 300) {
    return takeLast(data)
  } else if (rfToken) {
    const { IdToken: newToken } = await refreshToken(rfToken)
    const { data, status } = await getMeasurement(device.imei, newToken)

    if (status < 300) {
      return takeLast(data)
    }
  }

  throw Error(`Failed to get the last measurement for ${device?.imei}`)
}
