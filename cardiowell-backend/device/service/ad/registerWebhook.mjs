import axios from "axios"
import { getAdDevices } from "./getAdDevices.mjs"
import { signIn } from "./signIn.mjs"
import { nanoid } from "nanoid"

const generateAccessToken = () => nanoid(16)
const generateUrl = token => `${process.env.AD_RECEIVING_ENDPOINT}/${token}`

export const registerWebhook = async imei => {
  const { IdToken: token } = await signIn()
  const devices = await getAdDevices(token)
  const availableDevice = devices.find(device => device.imei === imei)

  if (availableDevice) {
    const accessToken = generateAccessToken()
    const webhook = await axios.post(
      `${process.env.AD_API}/webhook`,
      {
        url: generateUrl(accessToken),
        device_model: availableDevice.model,
        device_serial: availableDevice.serial,
        device_iccid: availableDevice.iccid,
        device_imei: availableDevice.imei,
      },
      {
        headers: { Authorization: `Bearer ${token}` },
      },
    )

    return {
      accessToken,
      webhook,
    }
  }
}
