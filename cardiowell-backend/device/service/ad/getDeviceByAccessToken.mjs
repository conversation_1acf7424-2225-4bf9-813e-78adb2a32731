import { Device } from "../../../models/device.mjs"

export const getDeviceByAccessToken = async accessToken => {
  const [device] = await Device.aggregate([
    {
      $match: { accessToken },
    },
    {
      $addFields: {
        customer_id: {
          $cond: {
            if: {
              $and: [{ $ne: ["$customer", null] }, { $ne: ["$customer", ""] }],
            },
            then: { $toObjectId: "$customer" },
            else: null,
          },
        },
      },
    },
    {
      $lookup: {
        from: "customers",
        localField: "customer_id",
        foreignField: "_id",
        as: "customer",
      },
    },
    {
      $unwind: {
        path: "$customer",
        preserveNullAndEmptyArrays: true,
      },
    },
  ])

  return device
}
