import axios from "axios"
import { getAdWebHooks } from "./getAdWebHooks.mjs"
import { signIn } from "./signIn.mjs"

export const deleteWebhook = async imei => {
  const { IdToken: token } = await signIn()
  const webhooks = await getAdWebHooks(token)
  const availableWebhooks = webhooks.filter(webhook => webhook.deviceImei === imei)

  if (availableWebhooks) {
    for (let availableWebhook of availableWebhooks) {
      await axios.delete(`${process.env.AD_API}/webhook/${availableWebhook.id}`, {
        headers: { Authorization: `Bearer ${token}` },
      })
    }
  }
}
