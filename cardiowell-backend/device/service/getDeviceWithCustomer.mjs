import { Device } from "../../models/device.mjs"

export const getDeviceWithCustomer = async identifier => {
  const [device] = await Device.aggregate([
    {
      $match: { $or: [{ imei: identifier }, { deviceId: identifier }] },
    },
    {
      $addFields: {
        customer_id: {
          $cond: {
            if: {
              $and: [{ $ne: ["$customer", null] }, { $ne: ["$customer", ""] }],
            },
            then: { $toObjectId: "$customer" },
            else: null,
          },
        },
      },
    },
    {
      $lookup: {
        from: "customers",
        localField: "customer_id",
        foreignField: "_id",
        as: "customer",
      },
    },
    {
      $unwind: {
        path: "$customer",
        preserveNullAndEmptyArrays: true,
      },
    },
  ])

  return device
}
