import { Device } from "../../models/device.mjs"
import { createMongoFilter } from "../../mongoFilter/createMongoFilter.mjs"
import { getAdditionalDeviceData } from "./additionalDeviceData.mjs"

const filterTypeMap = new Map([
  ["billingStart", "dateIsoString"],
  ["billingEnd", "dateIsoString"],
  ["createdAt", "dateTime"],
  ["sig", "range"],
  ["bat", "number"],
  ["tp", "number"],
  ["imei", "string"],
  ["lastMeasurement", "dateMilliseconds"],
  ["heartbeatTime", "dateMilliseconds"],
  ["receivedByMdms", "dateSeconds"],
])

const filterRangeMap = new Map([
  [
    "sig",
    [
      ["strong", 20],
      ["medium", 15],
    ],
  ],
])

const filterRangeMapper = filterField =>
  filterRangeMap.has(filterField) ? filterRangeMap.get(filterField) : []

const filterTypeMapper = filterField =>
  filterTypeMap.has(filterField) ? filterTypeMap.get(filterField) : "default"

const fieldMap = new Map([
  ["customer", "customer.name"],
  ["clinic", "clinic.name"],
  ["endpoint", "endpointObject.name"],
  ["deviceId", "latestStatus.deviceId"],
  ["modelNumber", "latestStatus.modelNumber"],
  ["sn", "latestTelemetry.sn"],
  ["iccid", "latestTelemetry.iccid"],
  ["mcuv", "latestHeartbeat.status.mcuv"],
  ["appv", "latestHeartbeat.status.appv"],
  ["fv", "latestHeartbeat.status.fv"],
  ["algv", "latestHeartbeat.status.algv"],
  ["ops", "latestStatus.status.ops"],
  ["net", "latestStatus.status.net"],
  ["tz", "latestStatus.status.tz"],
  ["sig", "latestStatus.status.sig"],
  ["bat", "latestStatus.status.bat"],
  ["tp", "latestStatus.status.tp"],
  ["imei", "imei"],
  ["receivedByMdms", "latestTelemetry.createdAt"],
])

const fieldMapper = filterField =>
  fieldMap.has(filterField) ? fieldMap.get(filterField) : filterField

export const getDevices = async ({
  skip,
  sortField,
  sortType,
  filterField,
  filterOperator,
  filterValue,
  pageSize,
} = {}) => {
  const filter = createMongoFilter(
    fieldMapper(filterField),
    filterOperator,
    filterValue,
    filterTypeMapper(filterField),
    filterRangeMapper(filterField),
  )

  const [result] = await Device.aggregate(
    [
      {
        $addFields: {
          customer_id: {
            $cond: {
              if: {
                $and: [{ $ne: ["$customer", null] }, { $ne: ["$customer", ""] }],
              },
              then: { $toObjectId: "$customer" },
              else: null,
            },
          },
          clinic_id: {
            $cond: {
              if: {
                $and: [{ $ne: ["$clinic", null] }, { $ne: ["$clinic", ""] }],
              },
              then: { $toObjectId: "$clinic" },
              else: null,
            },
          },
        },
      },
      {
        $lookup: {
          from: "customers",
          localField: "customer_id",
          foreignField: "_id",
          as: "customer",
        },
      },
      {
        $unwind: {
          path: "$customer",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "clinics",
          localField: "clinic_id",
          foreignField: "_id",
          as: "clinic",
        },
      },
      {
        $unwind: {
          path: "$clinic",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: "$_id",
          customer: { $first: "$customer" },
          clinic: { $first: "$clinic" },

          type: { $first: "$type" },
          imei: { $first: "$imei" },
          endpoint: { $first: "$endpoint" },
          billingStart: { $first: "$billingStart" },
          billingEnd: { $first: "$billingEnd" },
          createdAt: { $first: "$createdAt" },
          endpointObject: { $first: "$endpointObject" },
          manufacturer: { $first: "$manufacturer" },
          latestMessages: { $first: "$latestMessages" },
          lastMeasurement: { $first: "$lastMeasurement" },
          heartbeatTime: { $first: "$heartbeatTime" },
        },
      },
      {
        $match: filter,
      },
      sortField
        ? {
            $sort: { [fieldMapper(sortField)]: sortType === "asc" ? 1 : -1 },
          }
        : {
            $sort: { createdAt: -1 },
          },
      {
        $facet: {
          devices: [{ $skip: Number(skip) }, { $limit: Number(pageSize) }],
          info: [{ $count: "totalRowCount" }],
        },
      },
    ].filter(Boolean),
  )

  return {
    ...result,
    devices: result.devices.map(device => {
      const additionalData = getAdditionalDeviceData(device)
      return {
        ...device,
        ...additionalData,
      }
    }),
  }
}
