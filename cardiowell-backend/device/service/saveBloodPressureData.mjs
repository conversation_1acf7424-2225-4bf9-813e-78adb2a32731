import { TranstekBloodPressureMessage } from "../../models/transtekBloodPressureMessage.mjs"

export const saveBloodPressureData = body => {
  if (body["data"] && body["data"]["imei"]) {
    const data = body["data"]
    return TranstekBloodPressureMessage.create({
      deviceId: body["deviceId"],
      createdAt: body["createdAt"],
      dataType: data["data_type"],
      imei: data["imei"],
      sn: data["sn"],
      iccid: data["iccid"],
      systolic: data["sys"],
      diastolic: data["dia"],
      pulse: data["pul"],
      ihb: data["ihb"],
      hand: data["hand"],
      tri: data["tri"],
      bat: data["bat"],
      signalStrength: data["sig"],
      ts: data["ts"], // unix timestamp in seconds
      upload_time: data["upload_time"],
      tz: data["tz"],
      isTest: body["isTest"],
      modelNumber: body["modelNumber"],
      receivedByMdms: Date.now(),
    })
  }
}
