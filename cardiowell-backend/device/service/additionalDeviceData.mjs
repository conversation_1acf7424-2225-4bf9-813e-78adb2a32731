import { isBpmHeartbeatMessage } from "./transtek/bpm/latestBpmHeartbeat.mjs"
import { isBpmStatusMessage } from "./transtek/bpm/latestBpmStatus.mjs"
import { isBpmTelemetryMessage } from "./transtek/bpm/latestBpmTelemetry.mjs"

import { isBgmHeartbeat } from "./transtek/bgm/latestBgmHeartbeat.mjs"
import { isBgmStatus } from "./transtek/bgm/latestBgmStatus.mjs"
import { isBgmTelemetry } from "./transtek/bgm/latestBgmTelemetry.mjs"

import { isBodyTraceHeartbeatMessage } from "./bodyTrace/isBodyTraceHeartbeatMessage.mjs"

const convertVoltageToPercentage = (mVoltage, min, max) =>
  Math.round(((mVoltage - min) / (max - min)) * 100, 0)

const convertTranstekSignalToCommonFormat = value => {
  if (typeof value === "number") {
    if (value > 20) {
      return "Strong"
    } else if (value > 15) {
      return "Medium"
    } else {
      return "Weak"
    }
  }
  return value
}

const convertBodytraseSignalToCommonFormat = value => {
  if (typeof value === "number") {
    return `${value}%`
  }
  return value
}

const transformAdBloodPressureMessages = (latestMessages = []) => {
  return {}
}

const transformTranstekScaleMessages = (latestMessages = []) => {
  const latestMessage = latestMessages[0]

  return latestMessage
    ? {
        deviceId: latestMessage.deviceId,
        forwardedAt: latestMessage.forwardedAt,
        receivedByMotherShip: latestMessage.createdAt * 1000,
        tz: latestMessage.tz,
        //lastMeasurement: latestMessage.ts * 1000,
        sig: convertTranstekSignalToCommonFormat(latestMessage.sig),
        bat: latestMessage.bat,
        iccid: latestMessage.iccid,
        modelNumber: latestMessage.modelNumber,
        receivedByMdms: latestMessage.receivedByMdms,
      }
    : {}
}

const transformBodyTraceMessages = (latestMessages = []) => {
  const heartbeatMessage = latestMessages.find(
    ({ message }) => message && isBodyTraceHeartbeatMessage(message),
  )
  const valueMessage = latestMessages.find(
    ({ message }) => message && !isBodyTraceHeartbeatMessage(message),
  )

  const latestMessage = [heartbeatMessage, valueMessage]
    .filter(Boolean)
    .toSorted((a, b) => b.message.ts - a.message.ts)[0]

  return heartbeatMessage || valueMessage
    ? {
        bat: latestMessage.message?.batteryVoltage
          ? convertVoltageToPercentage(latestMessage.message?.batteryVoltage, 4700, 6000)
          : undefined,
        // lastMeasurement: valueMessage?.message?.ts,
        sig: convertBodytraseSignalToCommonFormat(latestMessage.message?.signalStrength),
        receivedByMdms: latestMessage?.createdAt
          ? new Date(latestMessage?.createdAt).getTime()
          : undefined,
        forwardedAt: latestMessage?.forwardedAt,
      }
    : {}
}

const transformBerryMedOximeterMessages = (latestMessages = []) => {
  const latestMessage = latestMessages[0]

  return latestMessage
    ? {
        iccid: latestMessage.iccid,
        bat: latestMessage.battery,
        appv: latestMessage.ver,
        // lastMeasurement: getBerryLastMeasurement(latestMessage),
        receivedByMdms: latestMessage.receivedByMdms,
        forwardedAt: latestMessage?.forwardedAt,
      }
    : {}
}

const transformWithingsBloodPressureMessages = (latestMessages = []) => {
  const latestMessage = latestMessages[0]

  return latestMessage
    ? {
        modelNumber: latestMessage.model,
        tz: latestMessage.timezone,

        lastMeasurement: latestMessage.created * 1000,
        receivedByMdms: latestMessage.updateTime * 1000,
        forwardedAt: latestMessage?.forwardedAt,
      }
    : {}
}

const transformTranstekBloodPressureMessages = (latestMessages = []) => {
  const latestStatus = latestMessages?.find(isBpmStatusMessage)
  const latestHeartbeat = latestMessages?.find(isBpmHeartbeatMessage)
  const latestTelemetry = latestMessages?.find(isBpmTelemetryMessage)

  const getCreatedAt = message => message?.message?.createdAt || message?.createdAt
  const getBat = message => message?.bat || message?.message?.status?.bat

  const messages = [latestStatus, latestHeartbeat, latestTelemetry]
    .filter(Boolean)
    .toSorted((a, b) => getCreatedAt(b) - getCreatedAt(a))

  return {
    deviceId:
      latestTelemetry?.deviceId ||
      latestStatus?.message?.deviceId ||
      latestHeartbeat?.message?.deviceId,
    modelNumber:
      latestTelemetry?.modelNumber ||
      latestStatus?.modelNumber ||
      latestHeartbeat?.modelNumber,
    bat: getBat(messages.find(m => Boolean(getBat(m)))),
    sn: latestTelemetry?.sn,
    iccid: latestTelemetry?.iccid,
    mcuv: latestHeartbeat?.message?.status?.mcuv,
    appv: latestHeartbeat?.message?.status?.appv,
    fv: latestHeartbeat?.message?.status?.fv,
    algv: latestHeartbeat?.message?.status?.algv,
    ops: latestStatus?.message?.status?.ops,
    net: latestStatus?.message?.status?.net,
    sig: convertTranstekSignalToCommonFormat(latestStatus?.message?.status?.sig),
    tp: latestStatus?.message?.status?.tp,
    me_num: latestStatus?.message?.status?.me_num,
    // lastMeasurement: latestTelemetry?.ts * 1000,
    receivedByMdms: latestTelemetry?.receivedByMdms,
    receivedByMotherShip: latestTelemetry?.createdAt * 1000,
    forwardedAt: latestTelemetry?.forwardedAt,
    tz: latestStatus?.message?.status?.tz,
  }
}

const transformTranstekGlucometerMessages = (latestMessages = []) => {
  const latestStatus = latestMessages?.find(isBgmStatus)
  const latestHeartbeat = latestMessages?.find(isBgmHeartbeat)
  const latestTelemetry = latestMessages?.find(isBgmTelemetry)

  return {
    deviceId:
      latestTelemetry?.deviceId ||
      latestStatus?.message?.deviceId ||
      latestHeartbeat?.message?.deviceId,
    modelNumber:
      latestTelemetry?.modelNumber ||
      latestStatus?.modelNumber ||
      latestHeartbeat?.modelNumber,
    bat: latestStatus?.message?.status?.bat || latestHeartbeat?.message?.status?.bat,
    sn: latestTelemetry?.sn,
    iccid:
      latestTelemetry?.iccid ||
      latestStatus?.message?.status?.iccid ||
      latestHeartbeat?.message?.status?.iccid,
    mcuv: latestStatus?.message?.status?.mcuv || latestHeartbeat?.message?.status?.mcuv,
    appv:
      latestStatus?.message?.status?.app_ver || latestHeartbeat?.message?.status?.app_ver,
    sig: convertTranstekSignalToCommonFormat(
      latestStatus?.message?.status?.sig || latestHeartbeat?.message?.status?.sig,
    ),
    // lastMeasurement: latestTelemetry?.ts * 1000,
    receivedByMdms: latestTelemetry?.receivedByMdms,
    receivedByMotherShip: latestTelemetry?.createdAt * 1000,
    uptime: latestTelemetry?.uptime * 1000,
    forwardedAt: latestTelemetry?.forwardedAt,
    tz: latestStatus?.message?.status?.tz,
  }
}

const transformMessageDataMap = {
  transtek: {
    bloodPressure: transformTranstekBloodPressureMessages,
    glucometer: transformTranstekGlucometerMessages,
    scale: transformTranstekScaleMessages,
  },
  bodyTrace: {
    bloodPressure: transformBodyTraceMessages,
    scale: transformBodyTraceMessages,
  },
  berryMed: {
    oximeter: transformBerryMedOximeterMessages,
  },
  withings: {
    bloodPressure: transformWithingsBloodPressureMessages,
  },
  ad: {
    bloodPressure: transformAdBloodPressureMessages,
  },
}

export const getAdditionalDeviceData = ({ latestMessages, type, manufacturer } = {}) => {
  const transformMessage = transformMessageDataMap[manufacturer]?.[type]

  return transformMessage?.(latestMessages) || {}
}
