import { Device } from "../../../models/device.mjs"
import { latestPulseOximeterValue } from "./latestPulseOximeterValue.mjs"
import { getLastMeasurement } from "./getLastMeasurement.mjs"

export const getBerryLatestMessages = async imei => {
  const valueMessage = await latestPulseOximeterValue(imei)

  return [valueMessage].filter(Boolean)
}

export const getCalculatedFields = async imei => {
  const latestMessages = await getBerryLatestMessages(imei)
  const telemetryMessage = latestMessages[0]

  return {
    latestMessages,
    ...(telemetryMessage
      ? { lastMeasurement: getLastMeasurement(telemetryMessage) }
      : {}),
  }
}

export const updateBerryLatestMessages = async (imei, newMessage) => {
  const device = await Device.findOne({ imei })

  if (device) {
    return Device.updateOne(
      {
        _id: device._id,
      },
      {
        lastMeasurement: getLastMeasurement(newMessage),
        latestMessages: [newMessage],
      },
    )
  }
}
