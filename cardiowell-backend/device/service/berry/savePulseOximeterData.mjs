import { PulseOximeterData } from "../../../models/pulseOximeter.mjs"

export const savePulseOximeterData = body => {
  return PulseOximeterData.create({
    imei: body["imei"],
    iccid: body["iccid"],
    time: body["time"],
    rsrp: body["rsrp"],
    err: body["err"],
    spo2: body["spo2"],
    pr: body["pr"],
    pi: body["pi"],
    battery: body["battery"],
    ver: body["ver"],
    receivedByMdms: Date.now(),
    isTest: body["isTest"],
  })
}
