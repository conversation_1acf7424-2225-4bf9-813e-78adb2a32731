import { generateImei, generateIccid } from "../../test/utils.mjs"
import { faker } from "@faker-js/faker"
import { formatInTimeZone } from "date-fns-tz"

const generateBerryTime = () =>
  `${formatInTimeZone(Date.now(), "GMT", "yy/MM/dd,HH:mm:ss")}-28`

export const generateBerryTelemetry = device => {
  const letestMessage = device?.letestMessage?.[0]

  const message = {
    imei: device?.imei || generateImei(),
    iccid: letestMessage?.iccid || generateIccid(),
    time: generateBerryTime(),
    rsrp: 0,
    err: 0,
    spo2: faker.number.int({ min: 90, max: 99 }),
    pr: faker.number.int({ min: 25, max: 250 }),
    pi: faker.number.int({ min: 1, max: 200 }),
    battery: faker.number.int({ min: 1, max: 100 }),
    ver: "V1.00.01.00",
    isTest: true,
  }
  return message
}
