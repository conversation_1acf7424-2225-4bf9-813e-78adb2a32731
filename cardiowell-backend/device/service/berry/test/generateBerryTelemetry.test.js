import { generateBerryTelemetry } from "./generateBerryTelemetry.mjs"
import { expect, test } from "vitest"

test("generateBerryTelemetry return telemetry message", () => {
  const message = generateBerryTelemetry()
  expect(message).toHaveProperty("imei")
  expect(message.time).toBeTypeOf("string")

  expect(message.rsrp).toBeTypeOf("number")
  expect(message.err).toBeTypeOf("number")
  expect(message.spo2).toBeTypeOf("number")
  expect(message.pr).toBeTypeOf("number")
  expect(message.pi).toBeTypeOf("number")
  expect(message.battery).toBeTypeOf("number")
  expect(message.ver).toBeTypeOf("string")

  expect(message).toHaveProperty("isTest", true)
})
