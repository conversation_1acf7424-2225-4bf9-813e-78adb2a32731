import { updateBerryLatestMessages } from "./berryLatestMessages.mjs"
import { getDeviceWithCustomer } from "../getDeviceWithCustomer.mjs"
import { forwardMessage, FORWARD_STATUS_NOT_SENT } from "../forwardMessage.mjs"
import { addForwardTime } from "./addForwardTime.mjs"

import { savePulseOximeterData } from "./savePulseOximeterData.mjs"

export const handleBerryMessage = async message => {
  const imei = message?.imei

  let newMessage = await savePulseOximeterData(message)

  if (imei) {
    const device = await getDeviceWithCustomer(imei)

    if (device?.customer) {
      const { endpoints = [], testReport } = device.customer
      const endpoint = endpoints.find(({ _id }) => _id.toString() === device.endpoint)

      if (endpoint) {
        let forwardResults
        if (endpoint.forward) {
          newMessage = await addForwardTime(newMessage)
          forwardResults = await forwardMessage({ endpoint, message })
        } else {
          forwardResults = { status: FORWARD_STATUS_NOT_SENT }
        }

        /* eslint-disable no-undef */
        if (testReport) {
          await TestMessage.create({
            imei,
            testReport,
            message,
            forwardResults,
          })
        }
        /* eslint-enable no-undef */
      }
    }
  }

  updateBerryLatestMessages(imei, newMessage)
}
