import { BodyTraceMessage } from "../../../models/bodyTraceMessage.mjs"
import { parse, addHours } from "date-fns"
import { generateBodyTraceTelemetry } from "../bodyTrace/test/generateBodyTraceTelemetry.mjs"

export async function loadBloodPressureBodyTraceData({
  data,
  device,
  testLoadSessionId,
}) {
  const bodyTraceMessages = data.map(entry => {
    const ts = addHours(parse(entry.Date, "yyyy-MM-dd", new Date()), 12).getTime()

    const message = generateBodyTraceTelemetry(device, {
      ts,
      systolic: Number(entry.Systolic) * 100,
      diastolic: Number(entry.Diastolic) * 100,
      pulse: Number(entry.Pulse),
      unit: 0,
      irregular: 0,
    })

    return {
      message,
      testLoadSessionId,
      createdAt: new Date(message.ts),
    }
  })

  await BodyTraceMessage.insertMany(bodyTraceMessages)
}
