import { TestDataUploads } from "../../../models/testDataUploads.mjs"
import { Device } from "../../../models/device.mjs"
import { deleteBloodPressureBodyTraceData } from "./deleteBloodPressureBodyTraceData.mjs"

export const deleteTestData = async sessionId => {
  const testDataUploads = await TestDataUploads.findOne({ sessionId })
  const device = await Device.findOne({ imei: testDataUploads.imei })

  if (device.type === "bloodPressure" || device.manufacturer === "bodyTrace") {
    const { deletedCount } = await deleteBloodPressureBodyTraceData({
      testLoadSessionId: sessionId,
    })
    console.log(`Deleted ${deletedCount} blood pressure bodyTrace test data`)

    testDataUploads.deleted = true
    await testDataUploads.save()

    return
  }

  throw new Error("Unsupported device type or manufacturer")
}
