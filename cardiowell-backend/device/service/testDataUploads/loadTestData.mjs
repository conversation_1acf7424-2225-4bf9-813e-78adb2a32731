import { customAlphabet } from "nanoid"
import { Device } from "../../../models/device.mjs"
import { TestDataUploads } from "../../../models/testDataUploads.mjs"
import { loadBloodPressureBodyTraceData } from "./loadBloodPressureBodyTraceData.mjs"

const alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"

const nanoid = customAlphabet(alphabet, 20)

export const loadTestData = async (data, imei) => {
  const device = await Device.findOne({ imei })

  if (!device) {
    throw new Error("Device not found")
  }

  if (device.type !== "bloodPressure" || device.manufacturer !== "bodyTrace") {
    throw new Error("Unsupported device type or manufacturer")
  }

  const sessionId = nanoid()
  const amount = data.length

  const testDataUpload = new TestDataUploads({
    sessionId,
    imei,
    amount,
  })

  await testDataUpload.save()

  await loadBloodPressureBodyTraceData({ data, device, testLoadSessionId: sessionId })
}
