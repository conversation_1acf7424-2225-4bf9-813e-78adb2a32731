import { latestBpmTelemetry } from "./bpm/latestBpmTelemetry.mjs"
import { latestBpmStatus } from "./bpm/latestBpmStatus.mjs"
import { latestBpmHeartbeat } from "./bpm/latestBpmHeartbeat.mjs"
import { Device } from "../../../models/device.mjs"
import {
  getBpmMessageType,
  BPM_TELEMETRY_DATA_TYPE,
  BPM_HEARTBEAT_DATA_TYPE,
} from "./bpm/types.mjs"
import { getLastMeasurement } from "./getLastMeasurement.mjs"

export const getTranstekBloodPressureLatestMessages = async imei => {
  const telemetry = await latestBpmTelemetry(imei)
  const status = await latestBpmStatus(imei)
  const heartbeat = await latestBpmHeartbeat(imei)

  return [telemetry, status, heartbeat].filter(Boolean)
}

export const getCalculatedFields = async imei => {
  const latestMessages = await getTranstekBloodPressureLatestMessages(imei)
  const telemetryMessage = latestMessages.find(
    message => getBpmMessageType(message) === BPM_TELEMETRY_DATA_TYPE,
  )
  const heartbeatMessage = latestMessages.find(
    message => getBpmMessageType(message) === BPM_HEARTBEAT_DATA_TYPE,
  )

  return {
    latestMessages,
    ...(telemetryMessage
      ? { lastMeasurement: getLastMeasurement(telemetryMessage) }
      : {}),
    ...(heartbeatMessage ? { heartbeatTime: getLastMeasurement(heartbeatMessage) } : {}),
  }
}

export const updateTranstekBloodPressureLatestMessages = async (imei, newMessage) => {
  const device = await Device.findOne({ imei })
  const newMessageType = getBpmMessageType(newMessage)

  if (device) {
    const latestMessages =
      device.latestMessages?.filter(latestMessage => {
        const latestMessageType = getBpmMessageType(latestMessage)
        return Boolean(latestMessageType) && latestMessageType !== newMessageType
      }) || []

    return Device.updateOne(
      {
        _id: device._id,
      },
      {
        ...(newMessageType === BPM_TELEMETRY_DATA_TYPE
          ? { lastMeasurement: getLastMeasurement(newMessage) }
          : {}),
        ...(newMessageType === BPM_HEARTBEAT_DATA_TYPE
          ? { heartbeatTime: getLastMeasurement(newMessage) }
          : {}),
        latestMessages: [newMessage, ...latestMessages],
      },
    )
  }
}
