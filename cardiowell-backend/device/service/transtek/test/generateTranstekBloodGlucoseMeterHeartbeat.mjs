import {
  generateImei,
  generateIccid,
  generateTsSeconds,
  generateDeviceId,
  generatemodelNumber,
  generateSig,
  generateBat,
  generateCreatedAt,
} from "../../test/utils.mjs"
import { getAdditionalDeviceData } from "../../additionalDeviceData.mjs"

export const generateTranstekBloodGlucoseMeterHeartbeat = device => {
  const additionalDeviceData = getAdditionalDeviceData(device)

  return {
    deviceId: generateDeviceId(),
    createdAt: generateCreatedAt(),
    status: {
      data_type: "bgm_gen1_heartbeat",
      imei: device?.imei || generateImei(),
      imsi: "460081948308296",
      iccid: additionalDeviceData?.iccid || generateIccid(),
      apn: "hologram",
      sig: generateSig(),
      bat: generateBat(),
      tz: 8,
      kernel_ver: "BG95M3LAR02A03_BETA0614",
      app_ver: additionalDeviceData?.appv || "0.1.0",
      mcuv: additionalDeviceData?.mcuv || "SW2071238011A001",
      uptime: generateTsSeconds(),
    },
    isTest: true,
    modelNumber: additionalDeviceData?.modelNumber || generatemodelNumber(),
  }
}
