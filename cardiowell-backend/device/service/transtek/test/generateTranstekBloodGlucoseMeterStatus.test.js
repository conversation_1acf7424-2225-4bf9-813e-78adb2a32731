import { generateTranstekBloodGlucoseMeterStatus } from "./generateTranstekBloodGlucoseMeterStatus.mjs"
import { expect, test } from "vitest"

test("generateTranstekBloodGlucoseMeterStatus return status message", () => {
  const message = generateTranstekBloodGlucoseMeterStatus()

  expect(message.deviceId).toBeTypeOf("string")
  expect(message.createdAt).toBeTypeOf("number")
  expect(message.status.data_type).toEqual("bgm_gen1_status")
  expect(message.status.imei).toBeTypeOf("string")
  expect(message.status.imsi).toBeTypeOf("string")
  expect(message.status.iccid).toBeTypeOf("string")
  expect(message.status.apn).toBeTypeOf("string")
  expect(message.status.sig).toBeTypeOf("number")
  expect(message.status.bat).toBeTypeOf("number")
  expect(message.status.tz).toBeTypeOf("number")
  expect(message.status.kernel_ver).toBeTypeOf("string")
  expect(message.status.app_ver).toBeTypeOf("string")
  expect(message.status.mcuv).toBeTypeOf("string")
})
