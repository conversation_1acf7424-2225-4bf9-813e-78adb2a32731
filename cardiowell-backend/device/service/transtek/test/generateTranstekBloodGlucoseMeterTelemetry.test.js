import { generateTranstekBloodGlucoseMeterTelemetry } from "./generateTranstekBloodGlucoseMeterTelemetry.mjs"
import { expect, test } from "vitest"

test("generateTranstekBloodGlucoseMeterTelemetry return telemetry message", () => {
  const message = generateTranstekBloodGlucoseMeterTelemetry()

  expect(message.data).toHaveProperty("data_type", "bgm_gen1_measure")
  expect(message.data).toHaveProperty("imei")

  expect(message.data.iccid).toBeTypeOf("string")
  expect(message.data.sn).toBeTypeOf("string")
  expect(message.data.data).toBeTypeOf("number")
  expect(message.data.unit).toBeTypeOf("number")
  expect(message.data.sample).toBeTypeOf("number")
  expect(message.data.target).toBeTypeOf("number")
  expect(message.data.meal).toBeTypeOf("number")
  expect(message.data.sig_lvl).toBeTypeOf("number")
  expect(message.data.ts).toBeTypeOf("number")
  expect(message.data.uptime).toBeTypeOf("number")
})
