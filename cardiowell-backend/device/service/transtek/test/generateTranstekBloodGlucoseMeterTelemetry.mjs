import { faker } from "@faker-js/faker"
import {
  generateImei,
  generateSn,
  generateIccid,
  generateTsSeconds,
} from "../../test/utils.mjs"
import { getAdditionalDeviceData } from "../../additionalDeviceData.mjs"

export const generateTranstekBloodGlucoseMeterTelemetry = device => {
  const additionalDeviceData = getAdditionalDeviceData(device)

  return {
    deviceId: device?.deviceId || "100224300182",
    createdAt: generateTsSeconds(),
    data: {
      data_type: "bgm_gen1_measure",
      messageType: "telemetry",
      imei: device?.imei || generateImei(),
      iccid: additionalDeviceData?.iccid || generateIccid(),
      sn: additionalDeviceData?.sn || generateSn(),
      data: faker.number.int({ min: 10, max: 200 }),
      unit: faker.number.int({ min: 1, max: 2 }),
      sample: faker.number.int({ min: 1, max: 2 }),
      target: faker.number.int({ min: 1, max: 3 }),
      meal: faker.number.int({ min: 0, max: 2 }),
      sig_lvl: faker.number.int({ min: 0, max: 4 }),
      ts: generateTsSeconds(),
      uptime: generateTsSeconds(),
    },
    isTest: true,
    modelNumber: device?.modelNumber || "TMB-2282-G",
  }
}
