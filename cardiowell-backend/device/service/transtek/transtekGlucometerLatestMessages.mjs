import { latestBgmTelemetry } from "./bgm/latestBgmTelemetry.mjs"
import { latestBgmStatus } from "./bgm/latestBgmStatus.mjs"
import { latestBgmHeartbeat } from "./bgm/latestBgmHeartbeat.mjs"

import { Device } from "../../../models/device.mjs"
import {
  getBgmMessageType,
  BGM_TELEMETRY_DATA_TYPE,
  BGM_HEARTBEAT_DATA_TYPE,
} from "../transtek/bgm/types.mjs"
import { getLastMeasurement } from "./getLastMeasurement.mjs"

export const getTranstekGlucometerLatestMessages = async imei => {
  const telemetry = await latestBgmTelemetry(imei)
  const status = await latestBgmStatus(imei)
  const heartbeat = await latestBgmHeartbeat(imei)

  return [telemetry, status, heartbeat].filter(Boolean)
}

export const getCalculatedFields = async imei => {
  const latestMessages = await getTranstekGlucometerLatestMessages(imei)
  const telemetryMessage = latestMessages.find(
    message => getBgmMessageType(message) === BGM_TELEMETRY_DATA_TYPE,
  )

  const heartbeatMessage = latestMessages.find(
    message => getBgmMessageType(message) === BGM_HEARTBEAT_DATA_TYPE,
  )

  return {
    latestMessages,
    ...(telemetryMessage
      ? { lastMeasurement: getLastMeasurement(telemetryMessage) }
      : {}),
    ...(heartbeatMessage ? { heartbeatTime: getLastMeasurement(heartbeatMessage) } : {}),
  }
}

export const updateTranstekGlucometerLatestMessages = async (imei, newMessage) => {
  const device = await Device.findOne({ imei })
  const newMessageType = getBgmMessageType(newMessage)

  if (device) {
    const latestMessages =
      device.latestMessages?.filter(latestMessage => {
        const latestMessageType = getBgmMessageType(latestMessage)

        return Boolean(latestMessageType) && latestMessageType !== newMessageType
      }) || []

    return Device.updateOne(
      {
        _id: device._id,
      },
      {
        ...(newMessageType === BGM_TELEMETRY_DATA_TYPE
          ? { lastMeasurement: getLastMeasurement(newMessage) }
          : {}),
        latestMessages: [...latestMessages, newMessage],
      },
    )
  }
}
