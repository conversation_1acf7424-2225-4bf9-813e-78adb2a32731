import { TranstekWeightScaleMessage } from "../../../../models/transtekWeightScaleMessage.mjs"

export const saveWeightScaleData = body => {
  if (body["data"] && body["data"]["imei"]) {
    const data = body["data"]
    return TranstekWeightScaleMessage.create({
      deviceId: body["deviceId"],
      createdAt: body["createdAt"],
      uid: data["uid"],
      wt: data["wt"], // weight in grams
      ts: data["ts"], // unix timestamp in seconds
      wet: data["wet"],
      lts: data["lts"],
      imei: data["imei"],
      iccid: data["iccid"],
      sig: data["sig"],
      bat: data["bat"], // battery percentage
      tz: data["tz"],
      upload_time: data["upload_time"],
      isTest: body["isTest"],
      modelNumber: body["modelNumber"],
      receivedByMdms: Date.now(),
    })
  }
}
