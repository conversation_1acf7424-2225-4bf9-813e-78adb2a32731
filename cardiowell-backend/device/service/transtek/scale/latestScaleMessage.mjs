import { Device } from "../../../../models/device.mjs"
import { TranstekWeightScaleMessage } from "../../../../models/transtekWeightScaleMessage.mjs"
import { getLastMeasurement } from "../getLastMeasurement.mjs"

export const getLatestScaleMessage = async imei => {
  return TranstekWeightScaleMessage.findOne(
    {
      imei: imei,
    },
    null,
    {
      $sort: { createdAt: -1 },
    },
  ).exec()
}

export const getCalculatedFields = async imei => {
  const latestMessage = await getLatestScaleMessage(imei)

  return {
    latestMessages: [latestMessage].filter(Boolean),
    ...(latestMessage ? { lastMeasurement: getLastMeasurement(latestMessage) } : {}),
  }
}

export const updateLatestScaleMessages = async (imei, newMessage) => {
  const device = await Device.findOne({ imei })

  if (device) {
    return Device.updateOne(
      {
        _id: device._id,
      },
      {
        lastMeasurement: getLastMeasurement(newMessage),
        latestMessages: [newMessage],
      },
    )
  }
}
