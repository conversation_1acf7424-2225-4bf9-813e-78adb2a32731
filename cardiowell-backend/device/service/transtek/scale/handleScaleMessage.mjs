import { saveWeightScaleData } from "./saveWeightScaleData.mjs"
import { getDeviceWithCustomer } from "../../getDeviceWithCustomer.mjs"
import { forwardMessage, FORWARD_STATUS_NOT_SENT } from "../../forwardMessage.mjs"
import { TestMessage } from "../../../../models/customer.mjs"
import { addForwardTime } from "./addForwardTime.mjs"
import { updateLatestScaleMessages } from "./latestScaleMessage.mjs"

export const handleScaleMessage = async message => {
  let newMessage = await saveWeightScaleData(message)
  const imei = message?.data?.imei

  if (imei) {
    const device = await getDeviceWithCustomer(imei)

    if (device?.customer) {
      const { endpoints = [], testReport } = device.customer
      const endpoint = endpoints.find(({ _id }) => _id.toString() === device.endpoint)

      if (endpoint) {
        let forwardResults
        if (endpoint.forward) {
          newMessage = await addForwardTime(newMessage)
          forwardResults = await forwardMessage({ endpoint, message })
        } else {
          forwardResults = { status: FORWARD_STATUS_NOT_SENT }
        }

        if (testReport) {
          await TestMessage.create({
            imei,
            testReport,
            message,
            forwardResults,
          })
        }
      }
    }
  }

  updateLatestScaleMessages(imei, newMessage)
}
