import { saveBloodGlucoseData } from "../saveBloodGlucoseData.mjs"
import { saveBloodGlucoseHeartbeatData } from "../saveBloodGlucoseHeartbeatData.mjs"
import { saveBloodGlucoseStatusData } from "../saveBloodGlucoseStatusData.mjs"
import { getDeviceWithCustomer } from "../getDeviceWithCustomer.mjs"
import { updateTranstekGlucometerLatestMessages } from "../../service/transtek/transtekGlucometerLatestMessages.mjs"
import { forwardMessage, FORWARD_STATUS_NOT_SENT } from "../forwardMessage.mjs"
import { TestMessage } from "../../../models/customer.mjs"
import { isRawBgmTelemetry, isRawBgmStatus, isRawBgmHeartbeat } from "./bgm/types.mjs"
import { addForwardTime } from "./bgm/addForwardTime.mjs"

export const handleBloodGlucoseMeterMessage = async message => {
  let newMessage
  let imei
  if (isRawBgmTelemetry(message)) {
    newMessage = await saveBloodGlucoseData(message)
    imei = message?.data?.imei
  } else if (isRawBgmStatus(message)) {
    newMessage = await saveBloodGlucoseStatusData(message)
    imei = message?.status.imei
  } else if (isRawBgmHeartbeat(message)) {
    newMessage = await saveBloodGlucoseHeartbeatData(message)
    imei = message?.status.imei
  }

  if (imei) {
    const device = await getDeviceWithCustomer(imei)

    if (device?.customer) {
      const { endpoints = [], testReport } = device.customer
      const endpoint = endpoints.find(({ _id }) => _id.toString() === device.endpoint)

      if (endpoint) {
        let forwardResults
        if (endpoint.forward) {
          newMessage = await addForwardTime(newMessage)
          forwardResults = await forwardMessage({ endpoint, message })
        } else {
          forwardResults = { status: FORWARD_STATUS_NOT_SENT }
        }

        if (testReport) {
          await TestMessage.create({
            imei,
            testReport,
            message,
            forwardResults,
          })
        }
      }
    }
  }

  await updateTranstekGlucometerLatestMessages(imei, newMessage)
}
