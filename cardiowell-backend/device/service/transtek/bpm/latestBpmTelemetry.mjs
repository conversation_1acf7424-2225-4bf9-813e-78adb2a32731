import { TranstekBloodPressureMessage } from "../../../../models/transtekBloodPressureMessage.mjs"
import { BPM_TELEMETRY_DATA_TYPE } from "./types.mjs"

export const latestBpmTelemetry = async imei => {
  return TranstekBloodPressureMessage.findOne(
    {
      dataType: BPM_TELEMETRY_DATA_TYPE,
      imei: imei,
    },
    null,
    {
      $sort: { createdAt: -1 },
    },
  ).exec()
}

export const isBpmTelemetryMessage = ({ dataType }) =>
  dataType === BPM_TELEMETRY_DATA_TYPE
