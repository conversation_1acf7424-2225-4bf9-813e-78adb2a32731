import { TranstekBloodPressureStatusMessage } from "../../../../models/transtekBloodPressureMessage.mjs"
import { BPM_STATUS_DATA_TYPE } from "./types.mjs"

export const latestBpmStatus = async imei => {
  return TranstekBloodPressureStatusMessage.findOne(
    {
      "message.status.data_type": BPM_STATUS_DATA_TYPE,
      "message.status.imei": imei,
    },
    null,
    {
      $sort: { createdAt: -1 },
    },
  ).exec()
}

export const isBpmStatusMessage = ({ message }) =>
  message?.status?.data_type === BPM_STATUS_DATA_TYPE
