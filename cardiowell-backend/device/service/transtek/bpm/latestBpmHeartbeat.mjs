import { TranstekBloodPressureStatusMessage } from "../../../../models/transtekBloodPressureMessage.mjs"
import { BPM_HEARTBEAT_DATA_TYPE } from "./types.mjs"

export const latestBpmHeartbeat = async imei =>
  TranstekBloodPressureStatusMessage.findOne(
    {
      "message.status.data_type": BPM_HEARTBEAT_DATA_TYPE,
      "message.status.imei": imei,
    },
    null,
    {
      sort: { createdAt: -1 },
    },
  ).exec()

export const isBpmHeartbeatMessage = ({ message }) =>
  message?.status?.data_type === BPM_HEARTBEAT_DATA_TYPE
