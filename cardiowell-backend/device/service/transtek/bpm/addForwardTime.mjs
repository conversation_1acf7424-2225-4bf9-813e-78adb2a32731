import {
  TranstekBloodPressureMessage,
  TranstekBloodPressureStatusMessage,
} from "../../../../models/transtekBloodPressureMessage.mjs"
import { getBpmMessageType, BPM_TELEMETRY_DATA_TYPE } from "./types.mjs"

export const addForwardTime = async message => {
  if (getBpmMessageType(message) === BPM_TELEMETRY_DATA_TYPE) {
    const newMesage = await TranstekBloodPressureMessage.findByIdAndUpdate(
      message._id,
      {
        $set: {
          forwardedAt: Date.now(),
        },
      },
      { new: true },
    )
    return newMesage
  } else {
    return TranstekBloodPressureStatusMessage.findByIdAndUpdate(message._id, {
      $set: {
        forwardedAt: Date.now(),
      },
    })
  }
}
