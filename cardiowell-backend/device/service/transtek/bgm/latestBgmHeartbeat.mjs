import { BloodGlucoseMeterMessage } from "../../../../models/bloodGlucoseMeterMessage.mjs"
import { BGM_HEARTBEAT_DATA_TYPE } from "./types.mjs"

export const latestBgmHeartbeat = async imei => {
  return BloodGlucoseMeterMessage.findOne(
    {
      "message.status.data_type": BGM_HEARTBEAT_DATA_TYPE,
      "message.status.imei": imei,
    },
    null,
    {
      $sort: { createdAt: -1 },
    },
  ).exec()
}

export const isBgmHeartbeat = ({ message }) =>
  message?.status?.data_type === BGM_HEARTBEAT_DATA_TYPE
