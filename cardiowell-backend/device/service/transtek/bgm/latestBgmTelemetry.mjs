import CellularBloodGlucoseDataModel from "../../../../models/cellularBloodGlucoseData.js"
import { BGM_TELEMETRY_DATA_TYPE } from "./types.mjs"

export const latestBgmTelemetry = async imei =>
  CellularBloodGlucoseDataModel.findOne(
    {
      dataType: BGM_TELEMETRY_DATA_TYPE,
      imei,
    },
    null,
    {
      sort: { createdAt: -1 },
    },
  ).exec()

export const isBgmTelemetry = ({ dataType }) => dataType === BGM_TELEMETRY_DATA_TYPE
