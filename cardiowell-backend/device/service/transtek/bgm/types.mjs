export const BGM_TELEMETRY_DATA_TYPE = "bgm_gen1_measure"
export const BGM_STATUS_DATA_TYPE = "bgm_gen1_status"
export const BGM_HEARTBEAT_DATA_TYPE = "bgm_gen1_heartbeat"

export const getBgmMessageType = message =>
  message?.dataType || message.message.status?.data_type

export const isRawBgmTelemetry = message =>
  message.data?.data_type === BGM_TELEMETRY_DATA_TYPE

export const isRawBgmStatus = message =>
  message.status?.data_type === BGM_STATUS_DATA_TYPE

export const isRawBgmHeartbeat = message =>
  message.status?.data_type === BGM_HEARTBEAT_DATA_TYPE
