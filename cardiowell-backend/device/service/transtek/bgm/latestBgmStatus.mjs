import { BloodGlucoseMeterMessage } from "../../../../models/bloodGlucoseMeterMessage.mjs"
import { BGM_STATUS_DATA_TYPE } from "./types.mjs"

export const latestBgmStatus = async imei => {
  return BloodGlucoseMeterMessage.findOne(
    {
      "message.status.data_type": BGM_STATUS_DATA_TYPE,
      "message.status.imei": imei,
    },
    null,
    {
      $sort: { createdAt: -1 },
    },
  ).exec()
}

export const isBgmStatus = ({ message }) =>
  message?.status?.data_type === BGM_STATUS_DATA_TYPE
