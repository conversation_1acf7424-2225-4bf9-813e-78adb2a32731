import { BloodGlucoseMeterMessage } from "../../../../models/bloodGlucoseMeterMessage.mjs"
import CellularBloodGlucoseDataModel from "../../../../models/cellularBloodGlucoseData.js"

import { getBgmMessageType, BGM_TELEMETRY_DATA_TYPE } from "./types.mjs"

export const addForwardTime = async message => {
  if (getBgmMessageType(message) === BGM_TELEMETRY_DATA_TYPE) {
    const newMesage = await CellularBloodGlucoseDataModel.findByIdAndUpdate(
      message._id,
      {
        $set: {
          forwardedAt: Date.now(),
        },
      },
      { new: true },
    )
    return newMesage
  } else {
    return BloodGlucoseMeterMessage.findByIdAndUpdate(message._id, {
      $set: {
        forwardedAt: Date.now(),
      },
    })
  }
}
