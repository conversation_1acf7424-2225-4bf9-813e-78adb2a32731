import WithingsBloodPressureData from "../../../models/withingsBloodPressureData.js"

export const addForwardTime = async (message, isNotSaved) => {
  if (isNotSaved) {
    return {
      ...message,
      forwardedAt: Date.now(),
    }
  }

  return WithingsBloodPressureData.findByIdAndUpdate(
    message._id,
    {
      $set: {
        forwardedAt: Date.now(),
      },
    },
    { new: true },
  )
}
