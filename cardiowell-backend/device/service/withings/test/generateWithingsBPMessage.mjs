import {
  generateTsSeconds,
  generateTz,
  generateDeviceId,
  generateDia,
  generateSys,
  generatePul,
} from "../../test/utils.mjs"

export const generateWithingsBPMessage = device => {
  return {
    updateTime: generateTsSeconds(),
    timezone: generateTz(),
    isTest: true,
    deviceId: device?.deviceId || generateDeviceId(),
    gprId: 5192882298,
    attrib: 0,
    date: generateTsSeconds(),
    created: generateTsSeconds(),
    modified: generateTsSeconds(),
    category: 1,
    modelId: 46,
    model: "BPM Connect Pro",
    comment: "commect",
    dia: {
      value: generateDia(),
      unit: 0,
      algo: 0,
      fm: 3,
    },
    sys: {
      value: generateSys(),
      unit: 0,
      algo: 0,
      fm: 3,
    },
    pulse: {
      value: generatePul(),
      unit: 0,
      algo: 0,
      fm: 3,
    },
  }
}
