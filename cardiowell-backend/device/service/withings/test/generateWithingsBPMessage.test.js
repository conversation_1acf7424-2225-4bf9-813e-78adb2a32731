import { generateWithingsBPMessage } from "./generateWithingsBPMessage.mjs"
import { expect, test } from "vitest"

test("generateWithingsBPMessage return message", () => {
  const message = generateWithingsBPMessage()

  expect(message.updateTime).toBeTypeOf("number")
  expect(message.timezone).toBeTypeOf("string")
  expect(message.deviceId).toBeTypeOf("string")
  expect(message.gprId).toBeTypeOf("number")
  expect(message.attrib).toBeTypeOf("number")
  expect(message.date).toBeTypeOf("number")
  expect(message.created).toBeTypeOf("number")
  expect(message.modified).toBeTypeOf("number")
  expect(message.category).toBeTypeOf("number")
  expect(message.modelId).toBeTypeOf("number")
  expect(message.model).toBeTypeOf("string")
  expect(message.comment).toBeTypeOf("string")

  expect(message.dia.value).toBeTypeOf("number")
  expect(message.dia.unit).toBeTypeOf("number")
  expect(message.dia.algo).toBeTypeOf("number")
  expect(message.dia.fm).toBeTypeOf("number")

  expect(message.sys.value).toBeTypeOf("number")
  expect(message.sys.unit).toBeTypeOf("number")
  expect(message.sys.algo).toBeTypeOf("number")
  expect(message.sys.fm).toBeTypeOf("number")

  expect(message.pulse.value).toBeTypeOf("number")
  expect(message.pulse.unit).toBeTypeOf("number")
  expect(message.pulse.algo).toBeTypeOf("number")
  expect(message.pulse.fm).toBeTypeOf("number")

  expect(message).toHaveProperty("isTest", true)
})
