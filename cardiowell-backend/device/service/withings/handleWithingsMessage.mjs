import { getDeviceWithCustomer } from "../getDeviceWithCustomer.mjs"
import { forwardMessage, FORWARD_STATUS_NOT_SENT } from "../forwardMessage.mjs"
import { updateWithingsBPLatestMessages } from "./withingsBPLatestMessages.mjs"
import { addForwardTime } from "./addForwardTime.mjs"
import { TestMessage } from "../../../models/customer.mjs"

export const handleWithingsMessage = async (message, isNotSaved) => {
  const deviceId = message?.deviceId
  let newMessage

  if (deviceId) {
    const device = await getDeviceWithCustomer(deviceId)

    if (device?.customer) {
      const { endpoints = [] } = device.customer
      const endpoint = endpoints.find(({ _id }) => _id.toString() === device.endpoint)

      if (endpoint) {
        let forwardResults
        if (endpoint.forward) {
          newMessage = await addForwardTime(message, isNotSaved)
          forwardResults = await forwardMessage({ endpoint, message })
        } else {
          forwardResults = { status: FORWARD_STATUS_NOT_SENT }
        }

        /* eslint-disable no-undef */
        if (testReport) {
          await TestMessage.create({
            deviceIdentifier: deviceId,
            testReport,
            message,
            forwardResults,
          })
        }
        /* eslint-enable no-undef */
      }
    }
  }

  updateWithingsBPLatestMessages(deviceId, newMessage || message)
}
