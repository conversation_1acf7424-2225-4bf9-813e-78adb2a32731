import { generateSphygmomanometerStatus } from "./generateSphygmomanometerStatus.mjs"
import { generateBodyTraceTelemetry } from "../bodyTrace/test/generateBodyTraceTelemetry.mjs"
import { updateTestMessagePrototype } from "./updateTestMessagePrototype.mjs"
import { generateTranstekBloodGlucoseMeterTelemetry } from "../transtek/test/generateTranstekBloodGlucoseMeterTelemetry.mjs"
import { generateTranstekBloodGlucoseMeterStatus } from "../transtek/test/generateTranstekBloodGlucoseMeterStatus.mjs"
import { generateTranstekBloodGlucoseMeterHeartbeat } from "../transtek/test/generateTranstekBloodGlucoseMeterHeartbeat.mjs"
import { generateBerryTelemetry } from "../berry/test/generateBerryTelemetry.mjs"
import { handleWithingsMessage } from "../withings/handleWithingsMessage.mjs"
import { generateWithingsBPMessage } from "../withings/test/generateWithingsBPMessage.mjs"
import { handleMessage as handleAdMessage } from "../ad/handleMessage.mjs"
import axios from "axios"

const TEST_RECEIVING_ENDPOINT = process.env.TEST_RECEIVING_ENDPOINT
const TEST_RECEIVING_API_TOKEN = process.env.TEST_RECEIVING_API_TOKEN
const TEST_BERRY_RECEIVING_API_TOKEN = process.env.TEST_BERRY_RECEIVING_API_TOKEN

const TEST_BERRY_RECEIVING_ENDPOINT = process.env.TEST_BERRY_RECEIVING_ENDPOINT
const TEST_BODY_TRACE_RECEIVING_ENDPOINT = process.env.TEST_BODY_TRACE_RECEIVING_ENDPOINT
const TEST_BODY_TRACE_RECEIVING_API_TOKEN =
  process.env.TEST_BODY_TRACE_RECEIVING_API_TOKEN

export const simulateDeviceMessage = async device => {
  if (device.type === "bloodPressure" && device.manufacturer === "transtek") {
    const statusMessage = generateSphygmomanometerStatus(device)

    if (!device.testStatusMessagePrototype) {
      updateTestMessagePrototype({ id: device._id, statusMessage })
    }
    try {
      await axios.post(TEST_RECEIVING_ENDPOINT, statusMessage, {
        headers: {
          authorization: TEST_RECEIVING_API_TOKEN,
        },
      })
    } catch (err) {
      console.error(err.cause)
    }
  }

  if (device.type === "bloodPressure" && device.manufacturer === "bodyTrace") {
    const telemetryMessage = generateBodyTraceTelemetry(device)

    try {
      await axios.post(TEST_BODY_TRACE_RECEIVING_ENDPOINT, telemetryMessage, {
        headers: {
          authorization: TEST_BODY_TRACE_RECEIVING_API_TOKEN,
        },
      })
    } catch (err) {
      console.error(err.cause)
    }
  }

  if (device.type === "glucometer" && device.manufacturer === "transtek") {
    const telemetryMessage = generateTranstekBloodGlucoseMeterTelemetry(device)
    const statusMessage = generateTranstekBloodGlucoseMeterStatus(device)
    const heartbeatMessage = generateTranstekBloodGlucoseMeterHeartbeat(device)

    const messages = [telemetryMessage, statusMessage, heartbeatMessage]

    try {
      for (const message of messages) {
        await axios.post(TEST_RECEIVING_ENDPOINT, message, {
          headers: {
            authorization: TEST_RECEIVING_API_TOKEN,
          },
        })
      }
    } catch (err) {
      console.error(err.cause)
    }
  }

  if (device.type === "oximeter" && device.manufacturer === "berryMed") {
    const message = generateBerryTelemetry(device)

    try {
      await axios.post(TEST_BERRY_RECEIVING_ENDPOINT, message, {
        headers: {
          authorization: TEST_BERRY_RECEIVING_API_TOKEN,
        },
      })
    } catch (err) {
      console.error(err)
    }
  }

  if (device.type === "bloodPressure" && device.manufacturer === "withings") {
    const message = generateWithingsBPMessage(device)

    try {
      await handleWithingsMessage(message, true)
    } catch (err) {
      console.error(err)
    }
  }

  if (device.type === "bloodPressure" && device.manufacturer === "ad") {
    const payload = { data: "TEST DATA" } // TODO add message generation

    try {
      await handleAdMessage(payload, device, true)
    } catch (err) {
      console.error(err)
    }
  }
}
