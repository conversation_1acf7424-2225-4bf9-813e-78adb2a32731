import { faker } from "@faker-js/faker"
import imei from "node-imei"

export const generateDeviceId = () =>
  String(faker.number.int({ min: 100000000000, max: 999999999999 }))

export const generateSn = () =>
  String(faker.number.int({ min: 100000000000, max: 999999999999 }))

export const generateIccid = () =>
  String(faker.number.int({ min: 10000000000000000000, max: 99999999999999999999 })) // eslint-disable-line no-loss-of-precision

export const generateSys = () => faker.number.int({ min: 60, max: 230 })

export const generateDia = () => faker.number.int({ min: 40, max: 130 })

export const generatePul = () => faker.number.int({ min: 40, max: 199 })

export const generateUserId = () => faker.number.int({ min: 1, max: 10 })

export const generateBatteryVoltage = () => faker.number.int({ min: 4700, max: 6000 })
export const generateSignalStregth = () => faker.number.int({ min: 0, max: 100 })
export const generateDiastolic = () => faker.number.int({ min: 10000, max: 11000 })
export const generateSystolic = () => faker.number.int({ min: 16000, max: 17000 })
export const generateIrregular = () => faker.number.int({ min: 0, max: 1 })

export const generateIhb = () => faker.datatype.boolean()
export const generateHand = () => faker.datatype.boolean()
export const generateTri = () => faker.datatype.boolean()

export const generateCreatedAt = () => Math.round(Date.now() / 1000)
export const generateTsSeconds = () => Math.round(Date.now() / 1000)
export const generateTs = () => Math.round(Date.now())
export const generateBat = () => faker.number.int({ min: 0, max: 100 })
export const generateTz = () => "UTC+8"
export const generateOps = () => {
  const options = ["AT&T", "T-mobile", "Verizon", "CHINA MOBILE"]
  const index = faker.number.int({ min: 0, max: options.length })
  return options[index]
}
export const generateNet = () => {
  const options = ["GSM", "eMTC", "NB-IOT"]
  const index = faker.number.int({ min: 0, max: options.length })
  return options[index]
}

export const generateSig = () => faker.number.int({ min: 0, max: 31 })
export const generateTp = () => faker.number.int({ min: 200, max: 250 })
export const generateAtTime = () => faker.number.int({ min: 1, max: 20 })
export const generatemodelNumber = () =>
  `${faker.string.alpha({ length: 3, casing: "upper" })}-${faker.number.int({
    min: 1000,
    max: 9999,
  })}-${faker.string.alpha({ length: 3, casing: "upper" })}`

export const generateImei = () => new imei().random()
