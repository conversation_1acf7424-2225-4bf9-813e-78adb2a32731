import {
  generateDeviceId,
  generateCreatedAt,
  generateBat,
  generateTz,
  generateSig,
  generatemodelNumber,
  generateImei,
  generateSn,
  generateIccid,
  generateUserId,
  generateSys,
  generateDia,
  generatePul,
  generateIhb,
  generateHand,
  generateTri,
  generateTsSeconds,
} from "./utils.mjs"

export const generateSphygmomanometerTelemetry = device => {
  return {
    deviceId: generateDeviceId(),
    createdAt: generateCreatedAt(),
    data: {
      date_type: "bpm_gen2_measure",
      imei: generateImei(),
      sn: generateSn(),
      iccid: generateIccid(),
      user: generateUserId(),
      sys: generateSys(),
      dia: generateDia(),
      pul: generatePul(),
      ihb: generateIhb(),
      hand: generateHand(),
      tri: generateTri(),
      bat: generateBat(),
      sig: generateSig(),
      ts: generateTsSeconds(),
      tz: generateTz(),
    },
    isTest: true,
    messageType: "telemetry",
    modelNumber: generatemodelNumber(),
  }
}
