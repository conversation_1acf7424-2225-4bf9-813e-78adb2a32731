import { generateSphygmomanometerTelemetry } from "./generateSphygmomanometerTelemetry.mjs"
import { expect, test } from "vitest"

test("generateSphygmomanometerTelemetry return telemetry message", () => {
  const message = generateSphygmomanometerTelemetry()

  expect(message).toHaveProperty("deviceId")
  expect(message).toHaveProperty("createdAt")

  expect(message).toHaveProperty("data.date_type", "bpm_gen2_measure")
  expect(message).toHaveProperty("data.imei")
  expect(message?.data?.sn).toHaveLength(12)
  expect(message?.data?.iccid).toHaveLength(20)

  expect(message?.data?.user).toBeTypeOf("number")
  expect(message?.data?.sys).toBeTypeOf("number")
  expect(message?.data?.dia).toBeTypeOf("number")
  expect(message?.data?.ihb).toBeTypeOf("boolean")
  expect(message?.data?.tri).toBeTypeOf("boolean")
  expect(message?.data?.bat).toBeTypeOf("number")
  expect(message?.data?.sig).toBeTypeOf("number")
  expect(message?.data?.ts).toBeTypeOf("number")
  expect(message?.data?.tz).toBeTypeOf("string")

  expect(message).toHaveProperty("isTest", true)
  expect(message).toHaveProperty("messageType", "telemetry")
  expect(message).toHaveProperty("modelNumber")
})
