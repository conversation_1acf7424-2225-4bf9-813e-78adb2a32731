import {
  generateDeviceId,
  generateCreatedAt,
  generateBat,
  generateTz,
  generateOps,
  generateNet,
  generateSig,
  generateTp,
  generateAtTime,
  generatemodelNumber,
  generateImei,
} from "./utils.mjs"
import { getAdditionalDeviceData } from "../additionalDeviceData.mjs"

export const generateSphygmomanometerStatus = device => {
  const additionalDeviceData = getAdditionalDeviceData(device)

  return {
    deviceId: additionalDeviceData?.deviceId || generateDeviceId(),
    createdAt: generateCreatedAt(),
    status: {
      data_type: "bpm_gen2_status",
      imei: device?.imei || generateImei(),
      bat: generateBat(),
      tz: additionalDeviceData?.tz || generateTz(),
      ops: additionalDeviceData?.ops || generateOps(),
      net: additionalDeviceData?.net || generateNet(),
      sig: generateSig(),
      tp: generateTp(),
      at_t: generateAtTime(),
    },
    modelNumber: additionalDeviceData?.modelNumber || generatemodelNumber(),
    messageType: "status",
    isTest: true,
  }
}
