const withingsTransform = ({ identifier, ...otherProps }) => ({
  deviceId: identifier,
  ...otherProps,
})

const defaultTransform = ({ identifier, ...otherProps }) => ({
  imei: identifier,
  ...otherProps,
})

const transformRequestMap = {
  withings: withingsTransform,
}

export const transformRequest = request => {
  return transformRequestMap[request.manufacturer]?.(request) || defaultTransform(request)
}
