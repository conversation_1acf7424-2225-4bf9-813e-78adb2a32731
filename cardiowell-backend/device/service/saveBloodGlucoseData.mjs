import CellularBloodGlucoseDataModel from "../../models/cellularBloodGlucoseData.js"

export const saveBloodGlucoseData = async body => {
  const data = body["data"]
  if (!data) {
    return null
  }

  let timestamp = data["ts"]
  if (!timestamp && data["time"]) {
    const time = data["time"].split(".")
    if (time.length == 5) {
      // split into year, month index, day, hour, seconds
      const tsDate = new Date(time[0], time[1] - 1, time[2], time[3], time[4])
      timestamp = Math.floor(tsDate.getTime() / 1000)
    }
  }
  const bloodGlucoseData = new CellularBloodGlucoseDataModel({
    deviceId: data["deviceId"],
    createdAt: body["createdAt"],
    dataType: data["data_type"],
    imei: data["imei"],
    iccid: data["iccid"],
    sn: data["sn"],
    data: data["data"],
    unit: data["unit"],
    sample: data["sample"],
    target: data["target"],
    meal: data["meal"],
    sigLevel: data["sig_lvl"],
    ts: data["ts"] ?? timestamp,
    uptime: data["uptime"] ?? data["upload_time"],
    isTest: data["isTest"],
    modelNumber: data["modelNumber"],
    receivedByMdms: Date.now(),
  })

  return bloodGlucoseData.save().catch(err => console.error(err))
}
