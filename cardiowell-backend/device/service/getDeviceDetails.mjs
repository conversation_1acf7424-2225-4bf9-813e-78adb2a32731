import { Device } from "../../models/device.mjs"
import mongoose from "mongoose"
import { getAdditionalDeviceData } from "./additionalDeviceData.mjs"

export const getDeviceDetails = async ({ id } = {}) => {
  const [device] = await Device.aggregate(
    [
      {
        $match: {
          _id: mongoose.Types.ObjectId(id),
        },
      },
      {
        $addFields: {
          customer_id: {
            $cond: {
              if: {
                $and: [{ $ne: ["$customer", null] }, { $ne: ["$customer", ""] }],
              },
              then: { $toObjectId: "$customer" },
              else: null,
            },
          },
          clinic_id: {
            $cond: {
              if: {
                $and: [{ $ne: ["$clinic", null] }, { $ne: ["$clinic", ""] }],
              },
              then: { $toObjectId: "$clinic" },
              else: null,
            },
          },
        },
      },
      {
        $lookup: {
          from: "customers",
          localField: "customer_id",
          foreignField: "_id",
          as: "customer",
        },
      },
      {
        $unwind: {
          path: "$customer",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "clinics",
          localField: "clinic_id",
          foreignField: "_id",
          as: "clinic",
        },
      },
      {
        $unwind: {
          path: "$clinic",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          endpointObject: {
            $cond: {
              if: { $ne: ["$endpoint", ""] },
              then: {
                $cond: {
                  if: {
                    $in: [{ $toObjectId: "$endpoint" }, "$customer.endpoints._id"],
                  },
                  then: {
                    $arrayElemAt: [
                      "$customer.endpoints",
                      {
                        $indexOfArray: [
                          "$customer.endpoints._id",
                          { $toObjectId: "$endpoint" },
                        ],
                      },
                    ],
                  },
                  else: {},
                },
              },
              else: {},
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          customer: 1,
          clinic: 1,
          type: 1,
          imei: 1,
          endpoint: 1,
          billingStart: 1,
          billingEnd: 1,
          createdAt: 1,
          endpointObject: 1,
          manufacturer: 1,
          testStarted: 1,
          testReport: 1,
          latestMessages: 1,
        },
      },
    ].filter(Boolean),
  )

  return device
    ? {
        ...device,
        ...getAdditionalDeviceData(device),
      }
    : null
}
