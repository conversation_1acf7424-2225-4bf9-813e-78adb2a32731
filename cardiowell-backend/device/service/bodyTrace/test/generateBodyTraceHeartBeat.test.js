import { generateBodyTraceHeartBeat } from "./generateBodyTraceHeartBeat.mjs"
import { expect, test } from "vitest"

test("generateBodyTraceHeartBeat return telemetry message", () => {
  const message = generateBodyTraceHeartBeat()

  expect(message).toHaveProperty("imei")
  expect(message.ts).toBeTypeOf("number")
  expect(message.batteryVoltage).toBeTypeOf("number")
  expect(message.signalStregth).toBeTypeOf("number")

  expect(message).toHaveProperty("isTest", true)
})
