import { generateBodyTraceTelemetry } from "./generateBodyTraceTelemetry.mjs"
import { expect, test } from "vitest"

test("generateBodyTraceTelemetry return telemetry message", () => {
  const message = generateBodyTraceTelemetry()

  expect(message).toHaveProperty("imei")
  expect(message.ts).toBeTypeOf("number")
  expect(message.batteryVoltage).toBeTypeOf("number")
  expect(message.signalStregth).toBeTypeOf("number")
  expect(message.values?.systolic).toBeTypeOf("number")
  expect(message.values?.diastolic).toBeTypeOf("number")
  expect(message.values?.unit).toBeTypeOf("number")
  expect(message.values?.irregular).toBeTypeOf("number")

  expect(message).toHaveProperty("isTest", true)
})
