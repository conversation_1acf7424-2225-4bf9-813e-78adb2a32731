import {
  generateTs,
  generateBatteryVoltage,
  generateSignalStregth,
  generateSystolic,
  generateDiastolic,
  generateIrregular,
  generateImei,
} from "../../test/utils.mjs"

export const generateBodyTraceTelemetry = (device, data) => {
  const timestamp = data?.ts || generateTs()

  return {
    imei: device?.imei || data?.imei || generateImei(),
    ts: timestamp,
    batteryVoltage: data?.batteryVoltage || generateBatteryVoltage(),
    signalStregth: data?.signalStregth || generateSignalStregth(),
    values: {
      systolic: data?.systolic || generateSystolic(),
      diastolic: data?.diastolic || generateDiastolic(),
      unit: data?.unit || 0,
      pulse: data?.pulse || 63,
      irregular: data?.irregular || generateIrregular(),
    },
    isTest: true,
  }
}
