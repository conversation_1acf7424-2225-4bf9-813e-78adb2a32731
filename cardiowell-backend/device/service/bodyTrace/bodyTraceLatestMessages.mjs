import { Device } from "../../../models/device.mjs"
import { isBodyTraceHeartbeatMessage } from "./isBodyTraceHeartbeatMessage.mjs"
import { latestBtValue } from "./latestBtValue.mjs"
import { latestBtHeartbeat } from "./latestBtHeartbeat.mjs"
import { getLastMeasurement } from "./getLastMeasurement.mjs"

export const getBodyTraceLatestMessages = async imei => {
  const valueMessage = await latestBtValue(imei)
  const heartbeat = await latestBtHeartbeat(imei)

  return [valueMessage, heartbeat].filter(Boolean)
}

export const getCalculatedFields = async imei => {
  const latestMessages = await getBodyTraceLatestMessages(imei)

  const telemetryMessage = latestMessages.find(
    message => getBodyTraceMessageType(message) === "values",
  )
  const heartbeatMessage = latestMessages.find(
    message => getBodyTraceMessageType(message) === "heartbeat",
  )

  return {
    latestMessages,
    ...(telemetryMessage
      ? { lastMeasurement: getLastMeasurement(telemetryMessage) }
      : {}),
    ...(heartbeatMessage
      ? {
          heartbeatTime: getLastMeasurement(heartbeatMessage),
        }
      : {}),
  }
}

const getBodyTraceMessageType = ({ message }) => {
  if (message) {
    return isBodyTraceHeartbeatMessage(message) ? "heartbeat" : "values"
  }
  return null
}

export const updateBodyTraceLatestMessages = async (imei, newMessage) => {
  const device = await Device.findOne({ imei })
  const newMessageType = getBodyTraceMessageType(newMessage)

  if (device) {
    const latestMessages =
      device.latestMessages?.filter(latestMessage => {
        const latestMessageType = getBodyTraceMessageType(latestMessage)
        return Boolean(latestMessageType) && latestMessageType !== newMessageType
      }) || []

    return Device.updateOne(
      {
        _id: device._id,
      },
      {
        ...(newMessageType === "values"
          ? { lastMeasurement: getLastMeasurement(newMessage) }
          : {}),
        ...(newMessageType === "heartbeat"
          ? {
              heartbeatTime: getLastMeasurement(newMessage),
            }
          : {}),
        latestMessages: [newMessage, ...latestMessages],
      },
    )
  }
}
