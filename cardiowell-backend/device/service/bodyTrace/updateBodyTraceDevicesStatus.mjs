import { Device } from "../../../models/device.mjs"
import { latestBpmMessage } from "../bodyTrace/latestBpmMessage.mjs"
import { getLastMeasurement } from "./getLastMeasurement.mjs"
import { isBodyTraceHeartbeatMessage } from "./bodyTrace/isBodyTraceHeartbeatMessage.mjs"

export const updateBodyTraceDevicesBloodPressureStatus = async () => {
  const devices = await Device.find({
    type: "bloodPressure",
    manufacturer: "bodyTrace",
  }).exec()

  for (let device of devices) {
    const latestMessage = await latestBpmMessage(device.imei)
    device.latestMessages = [latestMessage].filter(Boolean)
    if (!isBodyTraceHeartbeatMessage(latestMessage)) {
      device.lastMeasurement = getLastMeasurement(latestMessage)
    }
    await device.save()
  }
}
