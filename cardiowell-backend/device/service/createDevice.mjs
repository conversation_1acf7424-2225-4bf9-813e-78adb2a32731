import { Device } from "../../models/device.mjs"
import { getTranstekBloodPressureLatestMessages } from "./transtek/transtekBloodPressureLatestMessages.mjs"
import { getTranstekGlucometerLatestMessages } from "./transtek/transtekGlucometerLatestMessages.mjs"
import { getBodyTraceLatestMessages } from "./bodyTrace/bodyTraceLatestMessages.mjs"
import { getBerryLatestMessages } from "./berry/berryLatestMessages.mjs"
import { getWithingsBPLatestMessage } from "./withings/getWithingsBPLatestMessage.mjs"
import { getLatestScaleMessage } from "./transtek/scale/latestScaleMessage.mjs"
import { transformRequest } from "./transform.mjs"
import { registerWebhook } from "./ad/registerWebhook.mjs"

const isTranstekBloodPressure = request =>
  request.type === "bloodPressure" && request.manufacturer === "transtek"

const isTranstekGlucometer = request =>
  request.type === "glucometer" && request.manufacturer === "transtek"

const isBodyTrace = request => request.manufacturer === "bodyTrace"

const isBerryOximeter = request =>
  request.type === "oximeter" && request.manufacturer === "berryMed"

const isWithingsBloodPressure = request =>
  request.type === "bloodPressure" && request.manufacturer === "withings"

const isTranstekScale = request =>
  request.type === "scale" && request.manufacturer === "transtek"

const isAdBloodPressure = request =>
  request.type === "bloodPressure" && request.manufacturer === "ad"

export const createDevice = async request => {
  let extraFields = {}

  try {
    const createDeviceRequest = transformRequest(request)

    if (isTranstekBloodPressure(createDeviceRequest)) {
      extraFields.latestMessages = await getTranstekBloodPressureLatestMessages(
        createDeviceRequest.imei,
      )
    }

    if (isTranstekGlucometer(createDeviceRequest)) {
      extraFields.latestMessages = await getTranstekGlucometerLatestMessages(
        createDeviceRequest.imei,
      )
    }

    if (isBodyTrace(createDeviceRequest)) {
      extraFields.latestMessages = await getBodyTraceLatestMessages(
        createDeviceRequest.imei,
      )
    }

    if (isBerryOximeter(createDeviceRequest)) {
      extraFields.latestMessages = [
        await getBerryLatestMessages(createDeviceRequest.imei),
      ].filter(Boolean)
    }

    if (isWithingsBloodPressure(createDeviceRequest)) {
      extraFields.latestMessages = [
        await getWithingsBPLatestMessage(createDeviceRequest.deviceId),
      ].filter(Boolean)
    }

    if (isTranstekScale(createDeviceRequest)) {
      extraFields.latestMessages = [
        await getLatestScaleMessage(createDeviceRequest.imei),
      ].filter(Boolean)
    }

    if (isAdBloodPressure(createDeviceRequest)) {
      const { accessToken } = await registerWebhook(createDeviceRequest.imei)
      extraFields.accessToken = accessToken
    }

    return Device.create({ ...createDeviceRequest, ...extraFields })
  } catch (e) {
    console.log(e)
  }
}
