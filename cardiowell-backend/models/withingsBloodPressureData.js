const mongoose = require("mongoose")

const withingsBloodPressureSchema = mongoose.Schema({
  updateTime: Number,
  timezone: String,
  deviceId: String,
  grpId: Number,
  attrib: Number,
  date: Number,
  created: Number,
  modified: Number,
  category: Number,
  modelId: Number,
  model: String,
  comment: String,
  dia: {
    value: Number,
    unit: Number,
    algo: Number,
    fm: Number,
  },
  sys: {
    value: Number,
    unit: Number,
    algo: Number,
    fm: Number,
  },
  pulse: {
    value: Number,
    unit: Number,
    algo: Number,
    fm: Number,
  },
  sp02: {
    value: Number,
    unit: Number,
    algo: Number,
    fm: Number,
  },
  forwardedAt: Number,
})

withingsBloodPressureSchema.index({ deviceId: 1, created: -1 })
withingsBloodPressureSchema.index({ deviceId: 1, isTest: 1, created: -1 })

module.exports = mongoose.model("Withings_BPM", withingsBloodPressureSchema)
