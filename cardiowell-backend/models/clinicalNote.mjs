import mongoose from "mongoose"
const { Schema } = mongoose

const ClinicalNoteSchema = new Schema({
  patientId: {
    type: String,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  providerId: {
    type: String,
    required: true,
  },
  note: {
    type: String,
    required: true,
  },
  iv: {
    type: Buffer,
    required: true,
  },
})

ClinicalNoteSchema.index({ patientId: 1, createdAt: -1 })
ClinicalNoteSchema.index({ providerId: 1, createdAt: -1 })

export const ClinicalNote = mongoose.model("ClinicalNote", ClinicalNoteSchema)
