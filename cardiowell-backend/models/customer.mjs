import mongoose from "mongoose"
const { Schema } = mongoose

const EndpointSchema = new Schema({
  name: {
    type: String,
    required: true,
  },
  url: {
    type: String,
    required: true,
  },
  keyName: {
    type: String,
    required: true,
  },
  keyValue: {
    type: String,
    required: true,
  },
  forward: {
    type: Boolean,
    required: true,
  },
})

const TestMessageSchema = new Schema(
  {
    imei: String,
    deviceIdentifier: String,
    testReport: {
      type: String,
      required: true,
    },
    message: Schema.Types.Mixed,
    forwardResults: [Schema.Types.Mixed],
  },
  { timestamps: true },
)

const TestReportSchema = new Schema(
  {
    customer: {
      type: String,
      required: true,
    },
    messages: [
      {
        type: Schema.Types.ObjectId,
        ref: "TestMessage",
      },
    ],
    finishAt: Date,
  },
  { timestamps: true },
)

TestReportSchema.index({ customer: 1, finishAt: -1 })

const CustomerSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
    },
    contact: {
      type: String,
      required: true,
    },
    contactNumber: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
    },
    address: {
      type: String,
      required: true,
    },
    clinics: {
      type: [String],
      required: true,
    },
    endpoints: {
      type: [EndpointSchema],
    },
    testReport: {
      type: String,
    },
  },
  { timestamps: true },
)

CustomerSchema.index({ name: 1 })
CustomerSchema.index({ clinics: 1 })

export const Customer = mongoose.model("Customer", CustomerSchema)

export const TestReport = mongoose.model("TestReport", TestReportSchema)

export const TestMessage = mongoose.model("TestMessage", TestMessageSchema)
