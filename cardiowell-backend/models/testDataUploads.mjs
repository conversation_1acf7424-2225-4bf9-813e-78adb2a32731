import mongoose from "mongoose"
const { Schema } = mongoose

const TestDataUploadsSchema = new Schema(
  {
    imei: {
      type: String,
      unique: false,
    },
    deviceId: {
      type: String,
      unique: false,
    },
    sessionId: {
      type: String,
    },
    deleted: Boolean,
    amount: Number,
  },
  { timestamps: true },
)

TestDataUploadsSchema.index({ imei: 1 })
TestDataUploadsSchema.index({ deviceId: 1 })
TestDataUploadsSchema.index({ deleted: 1 })
TestDataUploadsSchema.index({ sessionId: 1 })

export const TestDataUploads = mongoose.model("TestDataUploads", TestDataUploadsSchema)
