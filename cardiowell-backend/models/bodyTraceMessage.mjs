import mongoose from "mongoose"
const { Schema } = mongoose

const BodyTraceMessageSchema = new Schema(
  {
    message: Schema.Types.Mixed,
    forwardedAt: Number,
    testLoadSessionId: String,
  },
  { timestamps: true },
)

BodyTraceMessageSchema.index({ "message.imei": 1, createdAt: -1 })
BodyTraceMessageSchema.index({ "message.imei": 1, "message.isTest": 1, createdAt: -1 })
BodyTraceMessageSchema.index({ "message.imei": 1, "message.ts": -1 })
BodyTraceMessageSchema.index({ createdAt: -1 })

export const BodyTraceMessage = mongoose.model("BodyTraceMessage", BodyTraceMessageSchema)
