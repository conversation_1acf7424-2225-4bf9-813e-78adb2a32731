const mongoose = require("mongoose")
var passportLocalMongoose = require("passport-local-mongoose")

const ProviderSchema = new mongoose.Schema(
  {
    username: String,
    firstName: String,
    lastName: String,
    phoneNumber: String,
    password: String,
    email: String,
    clinic: String,
  },
  {
    toObject: { virtuals: true },
    toJSON: { virtuals: true },
  },
)

ProviderSchema.virtual("role").get(function () {
  return "provider"
})

ProviderSchema.plugin(passportLocalMongoose)

module.exports = mongoose.model("Provider", ProviderSchema)
