const mongoose = require("mongoose")

const glucoseDataSchema = new mongoose.Schema({
  readings: [
    {
      reading_id: String,
      device_id: String,
      device_model: String,
      date_recorded: Date,
      date_received: Date,
      blood_glucose_mgdl: Number,
      blood_glucose_mmol: Number,
      before_meal: Boolean,
      battery: Number,
      time_zone_offset: Number,
      reading_type: String,
      event_flag: String,
    },
  ],
})

module.exports = mongoose.model("GlucoseData", glucoseDataSchema)
