import mongoose from "mongoose"
const { Schema } = mongoose

const TranstekBloodPressureMessageSchema = new Schema(
  {
    deviceId: String,
    createdAt: Number,
    dataType: String,
    imei: String,
    sn: String,
    iccid: String,
    systolic: Number,
    diastolic: Number,
    pulse: Number,
    ihb: <PERSON><PERSON><PERSON>,
    hand: <PERSON><PERSON><PERSON>,
    tri: <PERSON><PERSON><PERSON>,
    bat: Number,
    signalStrength: Number,
    ts: Number,
    upload_time: Number,
    tz: String,
    isTest: Boolean,
    modelNumber: String,
    receivedByMdms: Number,
    forwardedAt: Number,
  },
  {
    collection: "Transtek_BPM",
  },
)

TranstekBloodPressureMessageSchema.index({ imei: 1, ts: -1 })
TranstekBloodPressureMessageSchema.index({ imei: 1, isTest: 1, ts: -1 })

const TranstekBloodPressureStatusMessageSchema = new Schema(
  {
    message: Schema.Types.Mixed,
  },
  { timestamps: true },
)

export const TranstekBloodPressureStatusMessage = mongoose.model(
  "DeviceStatus",
  TranstekBloodPressureStatusMessageSchema,
)

export const TranstekBloodPressureMessage = mongoose.model(
  "TranstekBloodPressureMessage",
  TranstekBloodPressureMessageSchema,
)
