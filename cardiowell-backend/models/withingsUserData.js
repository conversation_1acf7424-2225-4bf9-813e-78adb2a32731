const mongoose = require("mongoose")

const userDataSchema = mongoose.Schema({
  patientId: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  devices: [
    {
      deviceId: String,
      macAddress: String,
      serialNumber: String,
      deviceType: String,
      battery: String,
      model: String,
      modelId: Number,
      timezone: String,
      lastSessionDate: Number,
    },
  ],
  userId: {
    type: String,
    required: true,
  },
  accessToken: {
    type: String,
    required: true,
  },
  refreshToken: {
    type: String,
    required: true,
  },
  expireDate: {
    type: Number,
    required: true,
  },
  scope: {
    type: String,
    required: true,
  },
  csrfToken: {
    type: String,
    required: true,
  },
  tokenType: {
    type: String,
    required: true,
  },
  lastUpdated: {
    type: Number,
    default: 0,
  },
})

userDataSchema.index({ patientId: 1 })
userDataSchema.index({ userId: 1 })

module.exports = mongoose.model("Withings_User_Data", userDataSchema)
