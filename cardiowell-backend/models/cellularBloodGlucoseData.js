const mongoose = require("mongoose")

const cellularBloodGlucoseSchema = mongoose.Schema({
  deviceId: String,
  createdAt: Number,
  dataType: String,
  imei: {
    type: String,
    required: true,
  },
  iccid: String,
  sn: String,
  data: {
    type: Number,
    required: true,
  },
  unit: Number,
  sample: Number,
  target: Number,
  meal: Number,
  sigLevel: Number,
  ts: {
    type: Number,
    required: true,
  },
  uptime: Number,
  isTest: Boolean,
  modelNumber: String,
  receivedByMdms: Number,
  forwardedAt: Number,
})

cellularBloodGlucoseSchema.index({ imei: 1, ts: -1 })
cellularBloodGlucoseSchema.index({ imei: 1, isTest: 1, ts: -1 })

module.exports = mongoose.model(
  "CellularBloodGlucoseData",
  cellularBloodGlucoseSchema,
  "CellularBloodGlucoseData",
)
