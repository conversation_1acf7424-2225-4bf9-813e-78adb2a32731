const mongoose = require("mongoose")
var passportLocalMongoose = require("passport-local-mongoose")
const jwt = require("jsonwebtoken")

const SuperAdminSchema = new mongoose.Schema(
  {
    username: String,
    password: String,
  },
  {
    toObject: { virtuals: true },
    toJSON: { virtuals: true },
  },
)

SuperAdminSchema.virtual("role").get(function () {
  return "superadmin"
})

SuperAdminSchema.plugin(passportLocalMongoose)

SuperAdminSchema.methods.generateJWT = function () {
  const today = new Date()
  const expirationDate = new Date(today)
  expirationDate.setDate(today.getDate() + 60)

  return jwt.sign(
    {
      email: this.email,
      role: this.role,
      id: this._id,
      exp: parseInt(expirationDate.getTime() / 1000, 10),
    },
    "secret",
  )
}

SuperAdminSchema.methods.toAuthJSON = function () {
  return {
    _id: this._id,
    username: this.username,
    role: this.role,
    token: this.generateJWT(),
  }
}

module.exports = mongoose.model("SuperAdmin", SuperAdminSchema)
