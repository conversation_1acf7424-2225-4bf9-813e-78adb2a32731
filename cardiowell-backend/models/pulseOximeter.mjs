import mongoose from "mongoose"
const { Schema } = mongoose

const PulseOximeterDataSchema = new Schema(
  {
    imei: String,
    iccid: String,
    time: String,
    rsrp: Number,
    err: Number,
    spo2: Number,
    pr: Number,
    pi: Number,
    battery: Number,
    ver: String,

    isTest: Boolean,
    receivedByMdms: Number,
    forwardedAt: Number,
  },
  {
    collection: "PulseOximeterData",
  },
)

PulseOximeterDataSchema.index({ imei: 1, time: -1 })
PulseOximeterDataSchema.index({ imei: 1, isTest: 1, time: -1 })

export const PulseOximeterData = mongoose.model(
  "PulseOximeterData",
  PulseOximeterDataSchema,
)
