import mongoose from "mongoose"
const { Schema } = mongoose

const TranstekWeightScaleMessageSchema = new Schema(
  {
    deviceId: String,
    createdAt: Number,
    uid: String,
    wt: Number, // weight in grams
    ts: Number, // unix timestamp in seconds
    wet: Number,
    lts: Number,
    imei: String,
    iccid: String,
    sig: Number,
    bat: Number, // battery percentage
    tz: String,
    upload_time: Number,
    isTest: Boolean,
    modelNumber: String,
    forwardedAt: Number,
    receivedByMdms: Number,
  },
  {
    collection: "Transtek_WS",
  },
)

TranstekWeightScaleMessageSchema.index({ imei: 1, ts: -1 })
TranstekWeightScaleMessageSchema.index({ imei: 1, isTest: 1, ts: -1 })

export const TranstekWeightScaleMessage = mongoose.model(
  "TranstekWeightScaleMessage",
  TranstekWeightScaleMessageSchema,
)
