import mongoose from "mongoose"
const { Schema } = mongoose

const AdBpmMessageSchema = new Schema(
  {
    payload: Schema.Types.Mixed,
    deviceId: String,
    receivedByMdms: Number,
    forwardedAt: Number,
    isTest: Boolean,
  },
  { timestamps: true },
)

AdBpmMessageSchema.index({ "payload.imei": 1, "payload.timestamp": -1 })
AdBpmMessageSchema.index({ "payload.imei": 1, isTest: 1, "payload.timestamp": -1 })
AdBpmMessageSchema.index({ "payload.imei": 1 })
AdBpmMessageSchema.index({ isTest: 1 })

export const AdBpmMessage = mongoose.model("AD_BPM", AdBpmMessageSchema)
