import mongoose from "mongoose"
const { Schema } = mongoose

const PatientProfileTimeSchema = new Schema({
  providerId: {
    type: String,
    required: true,
  },
  patientId: {
    type: String,
    required: true,
  },
  time: {
    type: Number,
    required: true,
  },
  ts: {
    type: Number,
    required: true,
  },
})

PatientProfileTimeSchema.index({ providerId: 1, patientId: 1, ts: -1 })
PatientProfileTimeSchema.index({ patientId: 1, ts: -1 })

export const PatientProfileTime = mongoose.model(
  "patientProfileTime",
  PatientProfileTimeSchema,
)
