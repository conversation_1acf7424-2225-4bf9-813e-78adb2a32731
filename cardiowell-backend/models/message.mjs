import mongoose from "mongoose"
import { v4 as uuidv4 } from "uuid"

const MessageSchema = new mongoose.Schema({
  messageId: {
    type: String,
    required: true,
    unique: true,
    default: uuidv4,
  },
  user: {
    userId: { type: String, required: true },
    userType: { type: String, required: true },
  },
  userContact: {
    contact: { type: String, required: true },
    contactType: { type: String, required: true },
  },
  systemContact: {
    contact: { type: String, required: true },
    contactType: { type: String, required: true },
  },
  transportType: { type: String, required: true },
  senderType: { type: String, required: true },
  messageText: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
})

MessageSchema.methods.getUserContact = function () {
  return this.userContact
}

MessageSchema.methods.isFromUser = function () {
  return this.senderType === "user"
}

MessageSchema.index({ "user.userId": 1, "user.userType": 1, createdAt: -1 })

export const Message = mongoose.model("Message", MessageSchema)

export const phoneContact = phone => ({
  contact: phone,
  contactType: "phone",
})

export const textToPatient = (patientId, patientContact, systemContact, messageText) => {
  return new Message({
    user: {
      userId: patientId,
      userType: "patient",
    },
    userContact: patientContact,
    systemContact: systemContact,
    transportType: "SMS",
    senderType: "system",
    messageText,
  })
}

export const textFromPatient = (
  patientId,
  patientContact,
  systemContact,
  messageText,
) => {
  return new Message({
    user: {
      userId: patientId,
      userType: "patient",
    },
    userContact: patientContact,
    systemContact: systemContact,
    transportType: "SMS",
    senderType: "user",
    messageText,
  })
}

export const webMessageFromPatient = (patientId, messageText) => {
  return new Message({
    user: {
      userId: patientId,
      userType: "patient",
    },
    userContact: {
      contact: "web",
      contactType: "web",
    },
    systemContact: {
      contact: "assistant",
      contactType: "app",
    },
    transportType: "WEB_APP",
    senderType: "user",
    messageText,
  })
}
