import mongoose from "mongoose"
const { Schema } = mongoose

const DeviceSchema = new Schema(
  {
    imei: {
      type: String,
      unique: true,
    },
    deviceId: {
      type: String,
    },
    type: {
      type: String,
      required: true,
    },
    manufacturer: {
      type: String,
    },
    customer: {
      type: String,
    },
    endpoint: {
      type: String,
    },
    clinic: {
      type: String,
    },
    billingStart: {
      type: String,
    },
    billingEnd: {
      type: String,
    },
    testStarted: {
      type: Boolean,
    },
    testStatusMessagePrototype: {
      type: Schema.Types.Mixed,
    },
    accessToken: String,
    session: {
      type: Schema.Types.Mixed,
    },
    latestMessages: [Schema.Types.Mixed],
    lastMeasurement: Number,
    heartbeatTime: Number,
  },
  { timestamps: true },
)

DeviceSchema.index({ customer: 1 })
DeviceSchema.index({ clinic: 1 })
DeviceSchema.index({ type: 1 })
DeviceSchema.index({ imei: 1 })
DeviceSchema.index({ deviceId: 1 })
DeviceSchema.index({ accessToken: 1 })
DeviceSchema.index({ lastMeasurement: -1 })
DeviceSchema.index({ heartbeatTime: -1 })

export const Device = mongoose.model("Device", DeviceSchema)
