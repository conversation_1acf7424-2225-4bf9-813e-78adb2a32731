import mongoose from "mongoose"
const { Schema } = mongoose

const alertLevels = {
  upperHigh: {
    value: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  upperMedium: {
    value: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  lowerHigh: {
    value: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  lowerMedium: {
    value: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
}

const alertStages = {
  normal: {
    value: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  elevated: {
    value: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  stageOne: {
    value: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  stageTwo: {
    value: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  crisis: {
    value: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
}

const ThresholdSchema = new Schema({
  bloodPressure: {
    diastolic: alertStages,
    systolic: alertStages,
    pulse: alertLevels,
  },
  weight: {
    weight: alertLevels,
  },
  pulseOximeter: {
    spo2: alertLevels,
    pulse: alertLevels,
  },
  bloodGlucose: {
    glucose: alertLevels,
  },
})

ThresholdSchema.index({ createdAt: -1 })

export const Threshold = mongoose.model("TestThreshold", ThresholdSchema)
