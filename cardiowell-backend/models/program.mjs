import mongoose from "mongoose"
const { Schema } = mongoose

const programSchema = new Schema({
  name: {
    type: String,
    required: true,
  },
  clinicId: {
    type: String,
    required: true,
  },
  thresholdId: {
    type: String,
    required: true,
  },
  description: {
    type: String,
  },
})

programSchema.index({ clinicId: 1 })
programSchema.index({ thresholdId: 1 })

export const Program = mongoose.model("testprogram", programSchema)
