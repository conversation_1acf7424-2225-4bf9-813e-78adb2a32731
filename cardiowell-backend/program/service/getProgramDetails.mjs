import { Program } from "../../models/program.mjs"
import mongoose from "mongoose"
export const getProgramDetails = async id => {
  const [doc] = await Program.aggregate([
    {
      $match: { _id: mongoose.Types.ObjectId(id) },
    },
    {
      $limit: 1,
    },
    {
      $lookup: {
        from: "testthresholds",
        let: { searchId: { $toObjectId: "$thresholdId" } },
        pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$searchId"] } } }, { $limit: 1 }],
        as: "threshold",
      },
    },
    {
      $unwind: {
        path: "$threshold",
        preserveNullAndEmptyArrays: true,
      },
    },
  ])

  return doc
}
