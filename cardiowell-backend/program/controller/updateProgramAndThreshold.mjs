import { body, validationResult, matchedData } from "express-validator"
import { thresholdValidator } from "../../threshold/utils/validator.mjs"
import { updateThreshold } from "../../threshold/service/updateThreshold.mjs"
import { updateProgram } from "../service/updateProgram.mjs"

export const updateProgramValidator = [
  body("name").notEmpty().escape(),
  body("description").isString().escape(),
  ...thresholdValidator,
]

export const updateProgramAndThreshold = async (request, response) => {
  try {
    validationResult(request).throw()
    const { id } = request.params
    const { name, description, bloodPressure, bloodGlucose, weight, pulseOximeter } =
      matchedData(request)

    const program = await updateProgram({ id, name, description })
    if (!program) {
      return response.status(404).send({ message: "Program not found" })
    }

    const threshold = await updateThreshold({
      thresholdId: program.thresholdId,
      bloodGlucose,
      bloodPressure,
      weight,
      pulseOximeter,
    })

    return response.status(201).send({ message: "Success", program, threshold })
  } catch (error) {
    console.error(error)
    return response.status(500).json(error)
  }
}
