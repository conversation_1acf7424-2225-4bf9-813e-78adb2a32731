import { getProgramDetails } from "../service/getProgramDetails.mjs"

export const getProgram = async (request, response) => {
  try {
    const { id } = request.params
    const program = await getProgramDetails(id)
    if (!program) {
      return response.status(200).send({ message: "Not Found" })
    }
    return response.status(200).send({ message: "Success", program })
  } catch (error) {
    console.error(error)
    return response.status(500).json(error)
  }
}
