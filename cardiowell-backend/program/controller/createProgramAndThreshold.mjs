import { body, validationResult, matchedData } from "express-validator"
import { createProgram } from "../service/createProgram.mjs"
import { createThreshold } from "../../threshold/service/createThreshold.mjs"
import { thresholdValidator } from "../../threshold/utils/validator.mjs"

export const createProgramValidator = [
  body("name").notEmpty().escape(),
  body("clinicId").notEmpty().escape(),
  body("description").isString().optional().escape(),
  ...thresholdValidator,
]
export const createProgramAndThreshold = async (request, response) => {
  try {
    validationResult(request).throw()
    const {
      name,
      clinicId,
      description,
      bloodPressure,
      weight,
      pulseOximeter,
      bloodGlucose,
    } = matchedData(request)

    const threshold = await createThreshold({
      bloodGlucose,
      weight,
      pulseOximeter,
      bloodPressure,
    })

    const newProgram = await createProgram({
      name,
      clinicId,
      description,
      thresholdId: threshold._id,
    })

    return response
      .status(201)
      .send({ message: "Success", program: newProgram, threshold })
  } catch (error) {
    console.error(error)
    return response.status(500).json(error)
  }
}
