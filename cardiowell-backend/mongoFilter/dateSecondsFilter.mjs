import { millisecondsToSeconds } from "date-fns"

export const dateSecondsFilter = (filterField, filterOperator, filterValue) => {
  const dateValue = new Date(filterValue)

  if (filterOperator === "is") {
    if (!filterValue) {
      return {}
    }

    return {
      [filterField]: {
        $gte: millisecondsToSeconds(dateValue.getTime()),
        $lte: millisecondsToSeconds(dateValue.getTime()) + 60,
      },
    }
  }

  if (filterOperator === "not") {
    if (!filterValue) {
      return {}
    }
    return {
      $or: [
        {
          [filterField]: {
            $lt: millisecondsToSeconds(dateValue.getTime()),
          },
        },
        {
          [filterField]: {
            $gt: millisecondsToSeconds(dateValue.getTime()) + 60,
          },
        },
      ],
    }
  }

  if (filterOperator === "after") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $gt: millisecondsToSeconds(dateValue.getTime()),
      },
    }
  }

  if (filterOperator === "before") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $lt: millisecondsToSeconds(dateValue.getTime()),
      },
    }
  }

  if (filterOperator === "onOrAfter") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $gte: millisecondsToSeconds(dateValue.getTime()),
      },
    }
  }

  if (filterOperator === "onOrBefore") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $lte: millisecondsToSeconds(dateValue.getTime()),
      },
    }
  }

  if (filterOperator === "isNotEmpty") {
    return {
      [filterField]: { $exists: true, $ne: null },
    }
  }

  if (filterOperator === "isEmpty") {
    return {
      $or: [{ [filterField]: { $exists: false } }, { [filterField]: null }],
    }
  }

  return {}
}
