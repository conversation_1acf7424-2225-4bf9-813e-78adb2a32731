export const dateIsoStringFilter = (filterField, filterOperator, filterValue) => {
  if (filterOperator === "after") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $gt: filterValue },
    }
  }

  if (filterOperator === "before") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $lt: filterValue },
    }
  }

  if (filterOperator === "onOrAfter") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $gte: filterValue },
    }
  }

  if (filterOperator === "onOrBefore") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $lte: filterValue },
    }
  }

  if (filterOperator === "isNotEmpty") {
    return {
      [filterField]: { $exists: true, $ne: null },
    }
  }

  if (filterOperator === "isEmpty") {
    return {
      $or: [{ [filterField]: { $exists: false } }, { [filterField]: null }],
    }
  }

  return {}
}
