import { setMilliseconds } from "date-fns"
import { setSeconds } from "date-fns"

export const dateTimeFilter = (filterField, filterOperator, filterValue) => {
  const dateValue = new Date(filterValue)

  if (filterOperator === "is") {
    if (!filterValue) {
      return {}
    }

    return {
      [filterField]: {
        $gte: setSeconds(setMilliseconds(dateValue, 0), 0),
        $lte: setSeconds(setMilliseconds(dateValue, 999), 59),
      },
    }
  }

  if (filterOperator === "not") {
    if (!filterValue) {
      return {}
    }
    return {
      $or: [
        {
          [filterField]: {
            $lt: setSeconds(setMilliseconds(dateValue, 0), 0),
          },
        },
        {
          [filterField]: {
            $gt: setSeconds(setMilliseconds(dateValue, 999), 59),
          },
        },
      ],
    }
  }

  if (filterOperator === "after") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $gt: setSeconds(setMilliseconds(dateValue, 999), 59) },
    }
  }

  if (filterOperator === "before") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $lt: setSeconds(setMilliseconds(dateValue, 0), 0) },
    }
  }

  if (filterOperator === "onOrAfter") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $gte: setSeconds(setMilliseconds(dateValue, 0), 0) },
    }
  }

  if (filterOperator === "onOrBefore") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $lte: setSeconds(setMilliseconds(dateValue, 999), 59),
      },
    }
  }

  if (filterOperator === "isNotEmpty") {
    return {
      [filterField]: { $exists: true, $ne: null },
    }
  }

  if (filterOperator === "isEmpty") {
    return {
      $or: [{ [filterField]: { $exists: false } }, { [filterField]: null }],
    }
  }

  return {}
}
