import { millisecondsToSeconds } from "date-fns"

export const dateMillisecondsFilter = (filterField, filterOperator, filterValue) => {
  const dateValue = new Date(filterValue)

  if (filterOperator === "is") {
    if (!filterValue) {
      return {}
    }

    return {
      [filterField]: {
        $gte: dateValue.getTime() - 1000,
        $lte: dateValue.getTime() + 1000,
      },
    }
  }

  if (filterOperator === "not") {
    if (!filterValue) {
      return {}
    }
    return {
      $or: [
        {
          [filterField]: {
            $lt: dateValue.getTime() - 1000,
          },
        },
        {
          [filterField]: {
            $gt: dateValue.getTime() + 1000,
          },
        },
      ],
    }
  }

  if (filterOperator === "after") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $gt: dateValue.getTime(),
      },
    }
  }

  if (filterOperator === "before") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $lt: dateValue.getTime(),
      },
    }
  }

  if (filterOperator === "onOrAfter") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $gte: dateValue.getTime(),
      },
    }
  }

  if (filterOperator === "onOrBefore") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $lte: dateValue.getTime(),
      },
    }
  }

  if (filterOperator === "isNotEmpty") {
    return {
      [filterField]: { $exists: true, $ne: null },
    }
  }

  if (filterOperator === "isEmpty") {
    return {
      $or: [{ [filterField]: { $exists: false } }, { [filterField]: null }],
    }
  }

  return {}
}
