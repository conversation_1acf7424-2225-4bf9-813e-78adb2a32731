import escapeStringRegexp from "escape-string-regexp"

export const stringFilter = (filterField, filterOperator, filterValue) => {
  if (filterOperator === "contains") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $regex: escapeStringRegexp(filterValue), $options: "i" },
    }
  }
  if (filterOperator === "in") {
    return { [filterField]: { $in: filterValue.split(",") } }
  }

  if (filterOperator === "equals") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: filterValue,
    }
  }

  if (filterOperator === "startsWith") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $regex: `^${escapeStringRegexp(filterValue)}`,
        $options: "i",
      },
    }
  }

  if (filterOperator === "endsWith") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $regex: `${escapeStringRegexp(filterValue)}$`,
        $options: "i",
      },
    }
  }

  if (filterOperator === "isEmpty") {
    return {
      [filterField]: { $exists: false },
    }
  }

  if (filterOperator === "isNotEmpty") {
    return {
      [filterField]: { $exists: true, $ne: "" },
    }
  }

  if (filterOperator === "isAnyOf") {
    if (!filterValue) {
      return {}
    }
    const value = typeof filterValue === "string" ? [filterValue] : filterValue
    return {
      [filterField]: { $in: value },
    }
  }

  if (filterOperator === "is") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: filterValue,
    }
  }

  if (filterOperator === "not") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $exists: true, $ne: filterValue },
    }
  }

  return {}
}
