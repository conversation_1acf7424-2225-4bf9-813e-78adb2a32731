import escapeStringRegexp from "escape-string-regexp"

export const arrayFilter = (filterField, filterOperator, filterValue) => {
  if (filterOperator === "contains") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $elemMatch: {
          $regex: escapeStringRegexp(filterValue),
          $options: "i",
        },
      },
    }
  }

  if (filterOperator === "equals") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $elemMatch: { $eq: filterValue } },
    }
  }

  if (filterOperator === "startsWith") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $elemMatch: {
          $regex: `^${escapeStringRegexp(filterValue)}`,
          $options: "i",
        },
      },
    }
  }

  if (filterOperator === "endsWith") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: {
        $elemMatch: {
          $regex: `${escapeStringRegexp(filterValue)}$`,
          $options: "i",
        },
      },
    }
  }

  if (filterOperator === "isEmpty") {
    return {
      $or: [{ [filterField]: { $exists: false } }, { [filterField]: { $size: 0 } }],
    }
  }

  if (filterOperator === "isNotEmpty") {
    return {
      [filterField]: { $exists: true, $not: { $size: 0 } },
    }
  }

  if (filterOperator === "isAnyOf") {
    const value = typeof filterValue === "string" ? [filterValue] : filterValue
    return {
      [filterField]: { $elemMatch: { $in: value } },
    }
  }
  return {}
}
