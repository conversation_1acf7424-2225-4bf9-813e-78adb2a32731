export const rangeFilter = (filterField, filterOperator, filterValue, rangeList = []) => {
  const POINT = 1
  const rangeIndex = rangeList.findIndex(([val, point]) => val === filterValue)
  if (rangeList.length === 0 || !filterValue) {
    return {}
  }

  if (filterOperator === "isAnyOf") {
    const value = typeof filterValue === "string" ? [filterValue] : filterValue
    return {
      $or: value.map(val => rangeFilter(filterField, "is", val, rangeList)),
    }
  }

  if (filterOperator === "is") {
    if (rangeIndex === -1) {
      return {
        [filterField]: {
          $lte: rangeList[rangeList.length - 1][POINT],
        },
      }
    } else if (rangeIndex === 0) {
      return {
        [filterField]: {
          $gt: rangeList[0][POINT],
        },
      }
    } else {
      return {
        [filterField]: {
          $gte: rangeList[rangeIndex][POINT],
          $lt: rangeList[rangeIndex - 1][POINT],
        },
      }
    }
  }

  if (filterOperator === "not") {
    if (rangeIndex === -1) {
      return {
        [filterField]: {
          $gt: rangeList[rangeList.length - 1][POINT],
        },
      }
    } else if (rangeIndex === 0) {
      return {
        [filterField]: {
          $lte: rangeList[0][POINT],
        },
      }
    } else {
      return {
        $or: [
          {
            [filterField]: {
              $lt: rangeList[rangeIndex][POINT],
            },
          },
          {
            [filterField]: {
              $gte: rangeList[rangeIndex - 1][POINT],
            },
          },
        ],
      }
    }
  }

  return {}
}
