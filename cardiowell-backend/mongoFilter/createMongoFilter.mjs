import { numberFilter } from "./numberFilter.mjs"
import { rangeFilter } from "./rangeFilter.mjs"
import { arrayFilter } from "./arrayFilter.mjs"
import { dateTimeFilter } from "./dateTimeFilter.mjs"
import { dateIsoStringFilter } from "./dateIsoStringFilter.mjs"
import { stringFilter } from "./stringFilter.mjs"
import { dateSecondsFilter } from "./dateSecondsFilter.mjs"
import { dateMillisecondsFilter } from "./dateMillisecondsFilter.mjs"

export const createMongoFilter = (
  filterField,
  filterOperator,
  filterValue,
  type,
  rangeList = [],
) => {
  if (type === "number") {
    return numberFilter(filterField, filterOperator, filterValue)
  }

  if (type === "range") {
    return rangeFilter(filterField, filterOperator, filterValue, rangeList)
  }

  if (type === "dateTime") {
    return dateTimeFilter(filterField, filterOperator, filterValue)
  }

  if (type === "dateIsoString") {
    return dateIsoStringFilter(filterField, filterOperator, filterValue)
  }

  if (type === "array") {
    return arrayFilter(filterField, filterOperator, filterValue)
  }

  if (type === "dateSeconds") {
    return dateSecondsFilter(filterField, filterOperator, filterValue)
  }

  if (type === "dateMilliseconds") {
    return dateMillisecondsFilter(filterField, filterOperator, filterValue)
  }

  return stringFilter(filterField, filterOperator, filterValue)
}
