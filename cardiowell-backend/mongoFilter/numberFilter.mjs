export const numberFilter = (filterField, filterOperator, filterValue) => {
  if (filterOperator === "=") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: Number(filterValue),
    }
  }

  if (filterOperator === "!=") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $exists: true, $ne: Number(filterValue) },
    }
  }

  if (filterOperator === ">") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $gt: Number(filterValue) },
    }
  }

  if (filterOperator === ">=") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $gte: Number(filterValue) },
    }
  }

  if (filterOperator === "<") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $lt: Number(filterValue) },
    }
  }

  if (filterOperator === "<=") {
    if (!filterValue) {
      return {}
    }
    return {
      [filterField]: { $lte: Number(filterValue) },
    }
  }

  if (filterOperator === "isAnyOf") {
    if (!filterValue) {
      return {}
    }
    const value = typeof filterValue === "string" ? [filterValue] : filterValue
    return {
      [filterField]: { $in: value.map(Number) },
    }
  }

  if (filterOperator === "isNotEmpty") {
    return {
      [filterField]: { $exists: true },
    }
  }

  if (filterOperator === "isEmpty") {
    return { [filterField]: { $exists: false } }
  }

  return {}
}
