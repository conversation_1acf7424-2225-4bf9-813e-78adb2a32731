import { body, validationResult, matchedData } from "express-validator"
import { ids, secrets } from "../utils/client.mjs"
import { urls } from "../utils/callbackUrls.mjs"
import { recoverAuthCode } from "../service/recoverAuthCode.mjs"
import { requestAccessToken } from "../service/requestAccessToken.mjs"
import { saveAccessTokens } from "../service/saveAccessTokens.mjs"
import { getUserDataByEmail } from "../service/getUserDataByEmail.mjs"

export const recoverValidator = [
  body("email").isEmail().escape(),
  body("clientId").isString().isIn(ids).withMessage("Invalid client id").escape(),
  body("clientSecret")
    .isString()
    .isIn(secrets)
    .withMessage("Invalid client secret")
    .escape(),
  body("callbackUrl").isString().isIn(urls).withMessage("Invalid callback url").escape(),
]

export const recover = async (request, response) => {
  try {
    validationResult(request).throw()
    const { email, clientId, clientSecret, callbackUrl } = matchedData(request)
    const userData = await getUserDataByEmail(email)
    if (!userData) {
      return response.status(404).send({ message: "Email not found" })
    }
    const authCode = await recoverAuthCode(clientId, clientSecret, email)
    if (!authCode) {
      throw new Error("Error retrieving auth code", authCode)
    }
    const tokens = await requestAccessToken({
      authCode,
      clientId,
      clientSecret,
      redirectUri: callbackUrl,
    })
    if (!tokens.accessToken || !tokens.refreshToken) {
      throw new Error("Error requesting access tokens")
    }
    await saveAccessTokens(userData.userId, tokens)
    return response.status(200).send({ message: "Success" })
  } catch (error) {
    console.error(error)
    return response.status(500).json(error)
  }
}
