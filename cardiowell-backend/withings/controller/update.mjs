import { body, validationResult, matchedData } from "express-validator"
import { onNotification } from "../service/onNotification.mjs"
import { clientId, clientSecret, clientIdDev, clientSecretDev } from "../utils/client.mjs"
import { callbackUrl, callbackUrlDev } from "../utils/callbackUrls.mjs"

export const updateValidator = [
  body("appli").isNumeric(),
  body("userid").isNumeric(),
  body("startdate").isNumeric(),
  body("enddate").isNumeric(),
]

export const update = async (request, response) => {
  try {
    validationResult(request).throw()
    const { appli, userid, startdate, enddate } = matchedData(request)
    const socket = request.app.get("socketio")
    onNotification({
      clientId,
      clientSecret,
      callbackUrl,
      socket,
      appli,
      userId: userid,
      startDate: startdate,
      endDate: enddate,
    })
    return response.status(200).send({ message: "Notification update received" })
  } catch (err) {
    console.error(err)
    return response.status(500).json(err)
  }
}

export const devUpdate = async (request, response) => {
  try {
    validationResult(request).throw()
    const { appli, userid, startdate, enddate } = matchedData(request)
    const socket = request.app.get("socketio")
    onNotification({
      clientId: clientIdDev,
      clientSecret: clientSecretDev,
      callbackUrl: callbackUrlDev,
      socket,
      appli,
      userId: userid,
      startDate: startdate,
      endDate: enddate,
    })
    return response.status(200).send({ message: "Notification update received" })
  } catch (err) {
    console.error(err)
    return response.status(500).json(err)
  }
}
