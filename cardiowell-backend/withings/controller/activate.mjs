import { body, validationResult, matchedData, check } from "express-validator"
import { activateUser } from "../service/activateUser.mjs"
import { requestAccessToken } from "../service/requestAccessToken.mjs"
import { saveUserData } from "../service/saveUserData.mjs"
import { notifySubscribe } from "../service/notifySubscribe.mjs"
import { clientId, clientSecret, clientIdDev, clientSecretDev } from "../utils/client.mjs"
import { callbackUrl, callbackUrlDev } from "../utils/callbackUrls.mjs"
import { bpmAppli } from "../utils/categories.mjs"
import { getUserDataByEmail } from "../service/getUserDataByEmail.mjs"
import { E164PhoneNumberRegex } from "../utils/regex.mjs"

export const activateValidator = [
  body("mailingPref").isBoolean({ strict: true }).toBoolean(),
  body("birthDate").isNumeric(),
  body("measures").isArray(),
  body("gender").isNumeric(),
  body("prefLang").isString(),
  body("unitPref").isObject(),
  body("email").isEmail(),
  body("timeZone").isString(),
  body("shortName").isAlphanumeric().isLength({ min: 3, max: 3 }),
  body("patientId").isString(),
  body("macAddresses").isArray(),
  body("firstName").isString().optional(),
  body("lastName").isString().optional(),
  body("phoneNumber")
    .isString()
    .matches(E164PhoneNumberRegex)
    .withMessage("Phone number does not match E164 format")
    .optional(),
  body("recoveryCode").isString().optional(),
  body("goals").isObject().optional(),
  body("redirectUri").isString().optional(),
  body("scopeOauth2").isString().optional(),
]

export const activate = async (request, response) => {
  try {
    validationResult(request).throw()
    const requestData = matchedData(request)

    if (await getUserDataByEmail(requestData.email)) {
      return response.status(400).send({ message: "Email already signed up" })
    }

    // User v2 - Activate: creates a withings user, links the devices related to the MAC address
    const data = await activateUser(clientId, clientSecret, requestData)
    if (data.status !== 0) {
      return response.status(400).send(data)
    }

    const { authCode, externalId, devices } = data
    if (!authCode || !externalId) {
      return response
        .status(400)
        .send({ message: "Error retreiving authorization code or externalId" })
    }

    // get access and refresh tokens from auth code
    const tokens = await requestAccessToken({
      authCode,
      clientId,
      clientSecret,
      redirectUri: callbackUrl,
    })

    if (tokens.error || tokens.status !== 0) {
      return response.status(400).send(tokens)
    }

    // save tokens, patient, and device info in database
    await saveUserData({
      patientId: externalId,
      devices,
      tokens,
      email: requestData.email,
    })

    // subscribe to Withings notification service
    const subscribed = await notifySubscribe({
      appli: bpmAppli,
      clientId,
      clientSecret,
      accessToken: tokens.accessToken,
      callbackUrl,
    })
    if (subscribed.status !== 0) {
      return response.status(400).send(subscribed)
    }

    return response.status(200).json({ message: "Recieved Withings device!" })
  } catch (err) {
    console.error(err)
    return response.status(500).json(err)
  }
}

export const devActivate = async (request, response) => {
  try {
    validationResult(request).throw()
    const requestData = matchedData(request)

    if (await getUserDataByEmail(requestData.email)) {
      return response.status(400).send({ message: "Email already signed up" })
    }

    // User v2 - Activate: creates a withings user, links the devices related to the MAC address
    const data = await activateUser(clientIdDev, clientSecretDev, requestData)
    if (data.status !== 0) {
      return response.status(400).send(data)
    }

    const { authCode, externalId, devices } = data
    if (!authCode || !externalId) {
      return response
        .status(400)
        .send({ message: "Error retreiving authorization code or externalId" })
    }

    // get access and refresh tokens from auth code
    const tokens = await requestAccessToken({
      authCode,
      clientId: clientIdDev,
      clientSecret: clientSecretDev,
      redirectUri: callbackUrlDev,
    })

    // save tokens, patient, and device info in database
    await saveUserData({
      patientId: externalId,
      devices,
      tokens,
      email: requestData.email,
    })

    // subscribe to Withings notification service
    const subscribed = await notifySubscribe({
      appli: bpmAppli,
      clientId: clientIdDev,
      clientSecret: clientSecretDev,
      accessToken: tokens.accessToken,
      callbackUrl: callbackUrlDev,
    })
    if (subscribed.status !== 0) {
      return response.status(400).send(subscribed)
    }

    return response.status(200).json({ message: "Recieved Withings device!" })
  } catch (err) {
    console.error(err)
    return response.status(500).json(err)
  }
}
