import { body, matchedData, validationResult } from "express-validator"
import { requestAccessToken } from "../service/requestAccessToken.mjs"
import { ids, secrets } from "../utils/client.mjs"
import { urls } from "../utils/callbackUrls.mjs"

export const requestTokensValidator = [
  body("authCode").isString().escape(),
  body("clientId").isString().isIn(ids).withMessage("Invalid client id").escape(),
  body("clientSecret")
    .isString()
    .isIn(secrets)
    .withMessage("Invalid client secret")
    .escape(),
  body("callbackUrl").isString().isIn(urls).withMessage("Invalid callback url").escape(),
]

export const requestTokens = async (request, response) => {
  try {
    validationResult(request).throw()
    const { authCode, clientId, clientSecret, callbackUrl } = matchedData(request)
    const tokens = await requestAccessToken({
      authCode,
      clientId,
      clientSecret,
      redirectUri: callbackUrl,
    })
    return response.status(201).json(tokens)
  } catch (error) {
    console.error(error)
    return response.status(500).json(error)
  }
}
