import { generateSignature } from "./generateSignature.mjs"

// Signature v2 - Getnonce: token used for api calls that require a signature, valid for 30 minutes
export const getNonceToken = async (clientId, clientSecret) => {
  const timestamp = Math.floor(Date.now() / 1000)

  const postParams = new URLSearchParams({
    action: "getnonce",
    client_id: clientId,
    timestamp,
    signature: generateSignature("getnonce", clientId, clientSecret, `${timestamp}`),
  })

  const nonce = fetch("https://wbsapi.us.withingsmed.net/v2/signature", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: postParams,
  })
    .then(response => {
      if (response.status == 200) {
        return response.json()
      }
      return {}
    })
    .then(data => {
      try {
        if (data["status"] == 0 && data["body"]) {
          return data["body"]["nonce"]
        }
        return ""
      } catch (e) {
        console.error(e)
        return ""
      }
    })
    .catch(err => {
      console.error(err)
      return ""
    })
  return nonce
}
