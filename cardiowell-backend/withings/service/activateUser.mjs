import { generateSignature } from "./generateSignature.mjs"
import { getNonceToken } from "./getNonceToken.mjs"
import { timezones } from "../utils/timezones.mjs"

// User v2 - Activate
export const activateUser = async (clientId, clientSecret, body) => {
  const nonce = await getNonceToken(clientId, clientSecret)
  if (!nonce) {
    return {
      status: 500,
      error: "Error retrieving nonce",
    }
  }

  const postParams = {
    action: "activate",
    client_id: clientId,
    nonce,
    signature: generateSignature("activate", clientId, clientSecret, nonce),
    mailingpref: body["mailingPref"],
    birthdate: body["birthDate"],
    measures: JSON.stringify(body["measures"]),
    gender: body["gender"],
    preflang: body["prefLang"],
    unit_pref: JSON.stringify(body["unitPref"]),
    email: body["email"],
    timezone: timezones[body["timeZone"]] || body["timeZone"],
    shortname: body["shortName"],
    external_id: body["patientId"],
    mac_addresses: body["macAddresses"],
    ...(body["firstName"] && { firstname: body["firstName"] }),
    ...(body["lastName"] && { lastname: body["lastName"] }),
    ...(body["phoneNumber"] && { phonenumber: body["phoneNumber"] }),
    ...(body["recoveryCode"] && { recovery_code: body["recoveryCode"] }),
    ...(body["goals"] && { goals: JSON.stringify(body["goals"]) }),
    ...(body["redirectUri"] && { redirect_uri: body["redirectUri"] }),
    ...(body["scopeOauth2"] && { scope_oauth2: body["scopeOauth2"] }),
  }

  return fetch("https://wbsapi.us.withingsmed.net/v2/user", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(postParams),
  })
    .then(response => {
      if (response.status === 200) {
        return response.json()
      }
      throw new Error(response.status)
    })
    .then(data => {
      const dataBody = data["body"]
      if (data["status"] == 0 && dataBody && dataBody["user"] && dataBody["devices"]) {
        return {
          status: data["status"],
          authCode: dataBody["user"]["code"],
          externalId: dataBody["user"]["external_id"],
          devices: dataBody["devices"],
          error: data["error"],
        }
      }
      return { status: data["status"], error: data["error"] }
    })
    .catch(err => {
      console.error(err)
      return { status: 500, error: err }
    })
}
