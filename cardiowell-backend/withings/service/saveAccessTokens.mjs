import withingsUserData from "../../models/withingsUserData.js"

export const saveAccessTokens = async (userId, tokens) => {
  const userData = await withingsUserData.findOneAndUpdate(
    {
      userId,
    },
    {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expireDate: tokens.expireDate,
      scope: tokens.scope,
      csrfToken: tokens.csrfToken,
      tokenType: tokens.tokenType,
    },
    (err, model) => {
      console.error(err)
    },
  )
  return userData
}
