import { expect, vi, describe, it, beforeEach } from "vitest"
import createFetchMock from "vitest-fetch-mock"

const fetchMocker = createFetchMock(vi)
fetchMocker.enableMocks()

vi.mock("../getNonceToken.mjs")
const getNonceTokenMock = await import("../getNonceToken.mjs")

import { clientId, clientSecret, accessToken, callbackUrl, nonce } from "./mockUtils.mjs"
import { notifySubscribe } from "../notifySubscribe.mjs"
import { generateSignature } from "../generateSignature.mjs"

const signature = generateSignature("subscribe", clientId, clientSecret, nonce)

const params = {
  appli: 4,
  clientId,
  clientSecret,
  accessToken,
  callbackUrl,
}

const expectedParams = new URLSearchParams({
  action: "subscribe",
  callbackurl: callbackUrl,
  appli: 4,
  signature,
  nonce,
  client_id: clientId,
})

const success = {
  status: 0,
  body: {},
}

describe("Testing Notify Subscribe Service Post Parameters", () => {
  beforeEach(() => {
    fetchMocker.resetMocks()
    fetchMocker.once(() => JSON.stringify(success))
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
  })

  it("sends correct body and header", async () => {
    await notifySubscribe(params)

    const request = fetchMocker.requests()[0]

    expect(request.body.toString()).toEqual(expectedParams.toString())

    expect(request.headers).toEqual(
      new Headers({
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Bearer ${accessToken}`,
      }),
    )
  })
})

describe("Testing Notify Subscribe response", () => {
  beforeEach(() => {
    fetchMocker.resetMocks()
  })

  it("handles successful response", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
    fetchMocker.once(() => JSON.stringify(success))

    const res = await notifySubscribe(params)

    expect(res).toStrictEqual(success)
    expect(fetchMocker.requests().length).toEqual(1)
    expect(fetchMocker.requests()[0].url).toEqual(
      "https://wbsapi.us.withingsmed.net/notify",
    )
  })

  it("handles a bad response", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
    fetchMocker.mockRejectOnce(new Error("fake error message"))

    const res = await notifySubscribe(params)

    expect(res).toStrictEqual({
      status: 500,
      error: Error("fake error message"),
    })
  })

  it("handles bad post parameters", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
    fetchMocker.once(() =>
      JSON.stringify({
        status: 503,
        error: "Invalid Params",
      }),
    )

    const res = await notifySubscribe(params)

    expect(res).toStrictEqual({
      status: 503,
      error: "Invalid Params",
    })
  })
})
