import { expect, vi, describe, it, beforeEach, afterEach } from "vitest"
import createFetchMock from "vitest-fetch-mock"

const fetchMocker = createFetchMock(vi)
fetchMocker.enableMocks()

import { clientId, clientSecret, nonce } from "./mockUtils.mjs"
import { getNonceToken } from "../getNonceToken.mjs"
import { generateSignature } from "../generateSignature.mjs"

const success = {
  status: 0,
  body: {
    nonce,
  },
}

const date = new Date(2000, 1, 1, 1)
const seconds = Math.floor(date.getTime() / 1000)
const signature = generateSignature("getnonce", clientId, clientSecret, seconds)
const expectedParams = new URLSearchParams({
  action: "getnonce",
  client_id: clientId,
  timestamp: seconds,
  signature,
})

describe("Testing Get Nonce Token parameters", () => {
  beforeEach(() => {
    vi.useFakeTimers()
    vi.setSystemTime(date)
    fetchMocker.resetMocks()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it("generates correct signature", async () => {
    fetchMocker.once(() => JSON.stringify(success))

    await getNonceToken(clientId, clientSecret)

    const request = fetchMocker.requests()[0]

    expect(request.body.toString()).toEqual(expectedParams.toString())
  })
})

describe("Testing Get Nonce Token response", () => {
  beforeEach(() => {
    fetchMocker.resetMocks()
  })

  it("returns a successful response", async () => {
    fetchMocker.once(() => JSON.stringify(success))

    const res = await getNonceToken(clientId, clientSecret)
    expect(res).toEqual(nonce)
  })

  it("handles a bad response", async () => {
    fetchMocker.mockRejectOnce(new Error("Mock error message"))

    const res = await getNonceToken(clientId, clientSecret)
    expect(res).toEqual("")
  })

  it("handles bad post parameters", async () => {
    fetchMocker.once(() =>
      JSON.stringify({
        status: 503,
        error: "error message",
      }),
    )

    const res = await getNonceToken(clientId, clientSecret)
    expect(res).toEqual("")
  })
})
