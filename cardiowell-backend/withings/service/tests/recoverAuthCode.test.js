import { expect, vi, describe, it, beforeEach } from "vitest"
import createFetchMock from "vitest-fetch-mock"

const fetchMocker = createFetchMock(vi)
fetchMocker.enableMocks()

import { clientId, clientSecret, nonce, authCode } from "./mockUtils.mjs"

vi.mock("../getNonceToken.mjs")
const getNonceTokenMock = await import("../getNonceToken.mjs")

import { recoverAuthCode } from "../recoverAuthCode.mjs"
import { generateSignature } from "../generateSignature.mjs"

const success = {
  status: 0,
  body: {
    user: {
      code: authCode,
    },
  },
}

const email = "<EMAIL>"

const params = new URLSearchParams({
  action: "recoverauthorizationcode",
  client_id: clientId,
  nonce,
  signature: generateSignature("recoverauthorizationcode", clientId, clientSecret, nonce),
  email,
})

describe("Testing Recover Auth Code Post parameters", () => {
  beforeEach(() => {
    fetchMocker.resetMocks()
    fetchMocker.once(() => JSON.stringify(success))
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
  })

  it("generates nonce and signature", async () => {
    await recoverAuthCode(clientId, clientSecret, email)

    const request = fetchMocker.requests()[0]

    expect(request.body.toString()).toEqual(params.toString())
  })
})

describe("Testing Recover Auth Code response", () => {
  beforeEach(() => {
    fetchMocker.resetMocks()
  })

  it("returns successful response", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
    fetchMocker.once(() => JSON.stringify(success))

    const res = await recoverAuthCode(clientId, clientSecret, email)
    expect(res).toEqual(authCode)
  })

  it("handles a bad nonce", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce("")
    fetchMocker.once(() => JSON.stringify(success))

    const res = await recoverAuthCode(clientId, clientSecret, email)
    expect(res).toEqual("")
  })

  it("handles a bad response", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce("")
    fetchMocker.mockRejectOnce(new Error("Mock error message"))

    const res = await recoverAuthCode(clientId, clientSecret, email)
    expect(res).toEqual("")
  })

  it("handles incorrect parameters", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce("")
    fetchMocker.once(() =>
      JSON.stringify({
        status: 503,
        error: "bad nonce",
      }),
    )

    const res = await recoverAuthCode(clientId, clientSecret, email)
    expect(res).toEqual("")
  })
})
