import { expect, vi, describe, it, beforeEach, afterEach } from "vitest"
import createFetchMock from "vitest-fetch-mock"

const fetchMocker = createFetchMock(vi)
fetchMocker.enableMocks()

import { accessToken } from "./mockUtils.mjs"
import { bpmAppli, bpmMeasTypes } from "../../utils/categories.mjs"
import { getMeas } from "../getMeas.mjs"

const successZero = {
  status: 0,
  body: {
    updatetime: "string",
    timezone: "string",
    measuregrps: [
      {
        grpid: 12,
        attrib: 1,
        date: 15942245600,
        created: 15942246600,
        modified: 15942257200,
        category: 15942257200,
        deviceid: "892359876fd8805ac45ba2b078c4828692f0276b1",
        hash_deviceid: "892359876fd8a805ac45bab078c4828692f0276b1",
        measures: [
          {
            value: 65750,
            type: 1,
            unit: -3,
            algo: 3425,
            fm: 0,
            position: 1,
          },
        ],
        comment: "A measurement comment",
        timezone: "Europe/Paris",
      },
    ],
    more: 0,
    offset: 0,
  },
}

const successOne = {
  status: 0,
  body: {
    updatetime: "string",
    timezone: "string",
    measuregrps: [
      {
        grpid: 12,
        attrib: 1,
        date: 1594245600,
        created: 1594246600,
        modified: 1594257200,
        category: 1594257200,
        deviceid: "892359876fd8805ac45bab078c4828692f0276b1",
        hash_deviceid: "892359876fd8805ac45bab078c4828692f0276b1",
        measures: [
          {
            value: 65750,
            type: 1,
            unit: -3,
            algo: 3425,
            fm: 0,
            position: 1,
          },
        ],
        comment: "A measurement comment",
        timezone: "Europe/Paris",
      },
    ],
    more: 1,
    offset: 10,
  },
}

const successTwo = {
  status: 0,
  body: {
    updatetime: "string",
    timezone: "string",
    measuregrps: [
      {
        grpid: 12,
        attrib: 1,
        date: 159424235600,
        created: 15941234246600,
        modified: 1594257200,
        category: 159234257200,
        deviceid: "89235asdfas9876fd8805ac45bab078c4828692f0276b1",
        hash_deviceid: "8923598ZXDd76fd8805ac45bab078c4828692f0276b1",
        measures: [
          {
            value: 6575032,
            type: 1,
            unit: -32,
            algo: 3425,
            fm: 0,
            position: 1,
          },
        ],
        comment: "A measurement comment",
        timezone: "Europe/Paris",
      },
    ],
    more: 1,
    offset: 10,
  },
}

const postParams = {
  action: "getmeas",
  meastypes: bpmMeasTypes,
  category: 1,
  lastupdate: 1000,
}

describe.skip("Testing Get Meas Post Parameters", () => {
  beforeEach(() => {
    fetchMocker.resetMocks()
    fetchMocker.once(() => JSON.stringify(successZero))
  })

  it("sends correct body and headers, no offset", async () => {
    const expectedParams = new URLSearchParams(postParams)

    await getMeas(accessToken, bpmAppli, 1000)

    const request = fetchMocker.requests()[0]

    expect(request.body.toString()).toEqual(expectedParams.toString())
    expect(request.headers).toEqual(
      new Headers({
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Bearer ${accessToken}`,
      }),
    )
  })

  it("sends correct body and headers, with offset", async () => {
    const expectedParams = new URLSearchParams({
      ...postParams,
      offset: 1,
    })

    await getMeas(accessToken, bpmAppli, 1000, 1)

    const request = fetchMocker.requests()[0]

    expect(request.body.toString()).toEqual(expectedParams.toString())
    expect(request.headers).toEqual(
      new Headers({
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Bearer ${accessToken}`,
      }),
    )
  })
})

describe("Testing Get Meas Response", () => {
  beforeEach(() => {
    fetchMocker.resetMocks()
  })

  it("handles single successful response", async () => {
    fetchMocker.once(() => JSON.stringify(successZero))
    const res = await getMeas(accessToken, bpmAppli, 1000)
    expect(res).toStrictEqual([successZero.body])
  })

  it("handles multiple successful responses", async () => {
    fetchMocker.once(() => JSON.stringify(successTwo))
    fetchMocker.once(() => JSON.stringify(successOne))
    fetchMocker.once(() => JSON.stringify(successZero))
    const res = await getMeas(accessToken, bpmAppli, 1000)
    expect(res).toStrictEqual([successTwo.body, successOne.body, successZero.body])
  })

  it("handles a failed response", async () => {
    fetchMocker.mockRejectOnce(new Error("error message"))
    const res = await getMeas(accessToken, bpmAppli, 1000)
    expect(res).toStrictEqual([])
  })

  it("handles incorrect parameters", async () => {
    fetchMocker.once(() =>
      JSON.stringify({
        status: 503,
        error: "error message",
      }),
    )
    const res = await getMeas(accessToken, bpmAppli, 1000)
    expect(res).toStrictEqual([])
  })
})
