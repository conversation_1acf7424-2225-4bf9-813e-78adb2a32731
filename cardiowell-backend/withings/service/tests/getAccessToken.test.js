import { expect, vi, describe, it, beforeEach, afterEach } from "vitest"
import createFetchMock from "vitest-fetch-mock"

const fetchMocker = createFetchMock(vi)
fetchMocker.enableMocks()

import {
  clientId,
  clientSecret,
  nonce,
  csrfToken,
  tokenType,
  scope,
  userId,
  callbackUrl,
  refreshToken,
  accessToken,
} from "./mockUtils.mjs"

vi.mock("../getNonceToken.mjs")
const getNonceTokenMock = await import("../getNonceToken.mjs")
getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValue(nonce)

vi.mock("../../../models/withingsUserData.js")
const mongoose = await import("../../../models/withingsUserData.js")
mongoose.default.findOneAndUpdate = vi.fn().mockResolvedValue(true)

import { getAccessToken } from "../getAccessToken.mjs"

const date = new Date(2000, 1, 1, 1)
const seconds = Math.floor(date.getTime() / 1000)

const newAccessToken = "new access token"
const newRefreshToken = "new refresh token"

const success = {
  status: 0,
  body: {
    userid: userId,
    access_token: newAccessToken,
    refresh_token: newRefreshToken,
    expires_in: 10800,
    scope,
    csrf_token: csrfToken,
    token_type: tokenType,
  },
}

describe("Testing Get Access Tokens response", () => {
  beforeEach(() => {
    vi.useFakeTimers()
    vi.setSystemTime(date)
    fetchMocker.resetMocks()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it("returns current token if not expired", async () => {
    fetchMocker.mockReject(() => new Error("Shoud not be requesting new tokens"))

    const userData = {
      expireDate: seconds + 1,
      accessToken,
      refreshToken,
      email: "<EMAIL>",
    }

    const res = await getAccessToken({
      clientId,
      clientSecret,
      userData,
      userId,
      callbackUrl,
    })

    expect(res).toBe(accessToken)
    expect(fetchMocker.requests().length).toBe(0)
  })

  it("refreshes access tokens if expired", async () => {
    fetchMocker.once(() => JSON.stringify(success))

    const userData = {
      expireDate: seconds,
      accessToken,
      refreshToken,
      email: "<EMAIL>",
    }

    const res = await getAccessToken({
      clientId,
      clientSecret,
      userData,
      userId,
      callbackUrl,
    })

    expect(res).toBe(newAccessToken)
    expect(fetchMocker.requests().length).toBe(1)
  })

  it("recovers auth code in case of invalid refresh token", async () => {
    fetchMocker.once(() =>
      JSON.stringify({
        status: 503,
        error: "Invalid Params: invalid refresh_token",
      }),
    )
    fetchMocker.once(() =>
      JSON.stringify({
        status: 0,
        body: {
          user: { code: "new auth code" },
        },
      }),
    )
    fetchMocker.once(() => JSON.stringify(success))

    const res = await getAccessToken({
      clientId,
      clientSecret,
      userData: {
        expireDate: seconds,
        accessToken,
        refreshToken,
        email: "<EMAIL>",
      },
      userId,
      callbackUrl,
    })

    expect(res).toBe(newAccessToken)
    expect(fetchMocker.requests().length).toBe(3)
  })

  it("handles a failed auth code recovery", async () => {
    fetchMocker.once(() =>
      JSON.stringify({
        status: 503,
        error: "Invalid Params: invalid refresh_token",
      }),
    )
    fetchMocker.once(() =>
      JSON.stringify({
        status: 503,
        error: "Invalid Params: invalid client_id",
      }),
    )

    const res = await getAccessToken({
      clientId,
      clientSecret,
      userData: {
        expireDate: seconds,
        accessToken,
        refreshToken,
        email: "<EMAIL>",
      },
      userId,
      callbackUrl,
    })

    expect(res).toBe(null)
    expect(fetchMocker.requests().length).toBe(2)
  })
})
