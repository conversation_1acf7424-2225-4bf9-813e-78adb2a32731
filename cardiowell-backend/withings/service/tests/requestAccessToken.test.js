import { expect, vi, describe, it, beforeEach, afterEach } from "vitest"
import createFetchMock from "vitest-fetch-mock"

const fetchMocker = createFetchMock(vi)
fetchMocker.enableMocks()

vi.mock("../getNonceToken.mjs")
const getNonceTokenMock = await import("../getNonceToken.mjs")

import {
  clientId,
  clientSecret,
  authCode,
  userId,
  accessToken,
  refreshToken,
  scope,
  csrfToken,
  tokenType,
  nonce,
  callbackUrl,
} from "./mockUtils.mjs"
import { requestAccessToken } from "../requestAccessToken.mjs"
import { generateSignature } from "../generateSignature.mjs"

const signature = generateSignature("requesttoken", clientId, clientSecret, nonce)

const params = {
  authCode,
  clientId,
  clientSecret,
  redirectUri: callbackUrl,
}

const expectedParams = new URLSearchParams({
  action: "requesttoken",
  client_id: clientId,
  nonce,
  signature,
  grant_type: "authorization_code",
  code: authCode,
  redirect_uri: callbackUrl,
})

const success = {
  status: 0,
  body: {
    userid: userId,
    access_token: accessToken,
    refresh_token: refreshToken,
    expires_in: 10800,
    scope,
    csrf_token: csrfToken,
    token_type: tokenType,
  },
}

describe("Testing Request Token Service Post Parameters", () => {
  beforeEach(() => {
    fetchMocker.resetMocks()
    fetchMocker.once(() => JSON.stringify(success))
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
  })

  it("parses required parameters", async () => {
    await requestAccessToken(params)

    expect(fetchMocker.requests()[0].body.toString()).toEqual(expectedParams.toString())
  })
})

describe("Testing RequestToken Response", () => {
  beforeEach(() => {
    vi.useFakeTimers()
    fetchMocker.resetMocks()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it("handles successful response", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
    fetchMocker.once(() => JSON.stringify(success))

    const date = new Date(2000, 1, 1, 1)
    const seconds = Math.floor(date.getTime() / 1000)
    vi.setSystemTime(date)

    const res = await requestAccessToken(params)

    expect(res).toStrictEqual({
      status: 0,
      userId,
      accessToken,
      refreshToken,
      expireDate: seconds + 10800,
      scope,
      csrfToken,
      tokenType,
    })

    expect(fetchMocker.requests().length).toEqual(1)
    expect(fetchMocker.requests()[0].url).toEqual(
      "https://wbsapi.us.withingsmed.net/v2/oauth2",
    )
  })

  it("handles a bad nonce", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(null)
    fetchMocker.mockReject(new Error("fake error message"))

    const res = await requestAccessToken(params)

    expect(res).toStrictEqual({
      status: 500,
      error: "Error retrieving nonce",
    })
  })

  it("handles a bad response", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
    fetchMocker.mockRejectOnce(new Error("fake error message"))

    const res = await requestAccessToken(params)

    expect(res).toStrictEqual({
      status: 500,
      error: Error("fake error message"),
    })
  })

  it("handles bad post parameters", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
    fetchMocker.once(() =>
      JSON.stringify({
        status: 503,
        error: "Invalid Params",
      }),
    )

    const res = await requestAccessToken(params)

    expect(res).toStrictEqual({
      status: 503,
      error: "Invalid Params",
    })
  })
})
