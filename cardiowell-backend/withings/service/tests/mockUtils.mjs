export const clientId = "mockClientId"
export const clientSecret = "mockClientSecret"
export const nonce = "aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d"
export const authCode = "490ed603fe9bd2ce10027bdba0c932069cd27085"
export const externalId = "3b7a6db0-ec7e-479b-9675-2a3d8d6a7e51"
export const userId = "123"
export const accessToken = "a075f8c14fb8df40b08ebc8508533dc332a6910a"
export const refreshToken = "f631236f02b991810feb774765b6ae8e6c6839ca"
export const scope = "user.info,user.metrics"
export const csrfToken = "PACnnxwHTaBQOzF7bQqwFUUotIuvtzSM"
export const tokenType = "Bearer"
export const callbackUrl = "https://www.google.com"
export const devices = [
  {
    mac_address: "00:24:e4:69:b2:30",
    type: "Scale",
    model: "Body Cardio",
    model_id: 6,
    battery: "medium",
    deviceid: "892359876fd8805ac45bab078c4828692f0276b1",
    timezone: "Europe/Paris",
    last_session_date: 1594159644,
  },
]
export const nonceSuccess = {
  status: 0,
  body: {
    nonce,
  },
}
