import { expect, vi, describe, it, beforeEach } from "vitest"
import createFetchMock from "vitest-fetch-mock"

const fetchMocker = createFetchMock(vi)
fetchMocker.enableMocks()

vi.mock("../getNonceToken.mjs")
const getNonceTokenMock = await import("../getNonceToken.mjs")

import {
  clientId,
  clientSecret,
  nonce,
  authCode,
  devices,
  externalId,
} from "./mockUtils.mjs"
import { generateSignature } from "../generateSignature.mjs"
import { activateUser } from "../activateUser.mjs"

const signature = generateSignature("activate", clientId, clientSecret, nonce)

const requiredParams = {
  mailingPref: false,
  birthDate: 923727600,
  measures: [
    {
      value: 175,
      unit: -2,
      type: 4,
    },
    {
      value: 66,
      unit: 0,
      type: 1,
    },
  ],
  gender: 0,
  prefLang: "en_US",
  unitPref: {
    weight: 2,
    height: 7,
    distance: 6,
    temperature: 13,
  },
  email: "<EMAIL>",
  timeZone: "AST",
  shortName: "AAA",
  patientId: "54321",
  macAddresses: ["12345"],
}

const expectedParams = {
  action: "activate",
  client_id: clientId,
  nonce,
  signature,
  mailingpref: false,
  birthdate: 923727600,
  measures: JSON.stringify([
    {
      value: 175,
      unit: -2,
      type: 4,
    },
    {
      value: 66,
      unit: 0,
      type: 1,
    },
  ]),
  gender: 0,
  preflang: "en_US",
  unit_pref: JSON.stringify({
    weight: 2,
    height: 7,
    distance: 6,
    temperature: 13,
  }),
  email: "<EMAIL>",
  timezone: "America/Puerto_Rico",
  shortname: "AAA",
  external_id: "54321",
  mac_addresses: ["12345"],
}

const optionalParams = {
  firstName: "Test",
  lastName: "User",
  phoneNumber: "**********",
  goals: {
    steps: 10000,
    sleep: 28800,
    weight: {
      value: 70500,
      unit: -3,
    },
  },
  redirectUri: "www.google.com",
  scopeOauth2: "asdf1234",
}

const success = {
  status: 0,
  body: {
    user: {
      code: authCode,
      external_id: externalId,
    },
    devices,
  },
}

describe("Testing Activate Service Post Parameters", () => {
  beforeEach(() => {
    fetchMocker.resetMocks()
    fetchMocker.once(() => JSON.stringify(success))
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
  })

  it("creates nonce and signature", async () => {
    await activateUser(clientId, clientSecret, {})
    expect(JSON.parse(fetchMocker.requests()[0].body)).toStrictEqual({
      action: "activate",
      client_id: clientId,
      nonce,
      signature,
    })
  })

  it("parses required parameters", async () => {
    await activateUser(clientId, clientSecret, requiredParams)
    expect(JSON.parse(fetchMocker.requests()[0].body)).toStrictEqual(expectedParams)
  })

  it("parses optional parameters", async () => {
    await activateUser(clientId, clientSecret, {
      ...requiredParams,
      ...optionalParams,
    })
    expect(JSON.parse(fetchMocker.requests()[0].body)).toStrictEqual({
      ...expectedParams,
      firstname: "Test",
      lastname: "User",
      phonenumber: "**********",
      goals: JSON.stringify({
        steps: 10000,
        sleep: 28800,
        weight: {
          value: 70500,
          unit: -3,
        },
      }),
      redirect_uri: "www.google.com",
      scope_oauth2: "asdf1234",
    })
  })
})

describe("Testing Activate Service Response", () => {
  beforeEach(() => {
    fetchMocker.resetMocks()
  })

  it("handles successful response", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
    fetchMocker.once(() => JSON.stringify(success))

    const res = await activateUser(clientId, clientSecret, {})
    expect(res.status).toBe(0)
    expect(res.authCode).toEqual(authCode)
    expect(res.externalId).toEqual(externalId)
    expect(fetchMocker.requests().length).toEqual(1)
    expect(fetchMocker.requests()[0].url).toEqual(
      "https://wbsapi.us.withingsmed.net/v2/user",
    )
  })

  it("handles a bad nonce", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(null)
    fetchMocker.mockReject(new Error("fake error message"))

    const res = await activateUser(clientId, clientSecret, {})
    expect(res).toStrictEqual({
      status: 500,
      error: "Error retrieving nonce",
    })
    expect(fetchMocker.requests().length).toEqual(0)
  })

  it("handles a bad response", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
    fetchMocker.mockRejectOnce(new Error("fake error message"))

    const res = await activateUser(clientId, clientSecret, {})
    expect(res.status).toBe(500)
    expect(res.error).toEqual(Error("fake error message"))
  })

  it("handles bad post parameters", async () => {
    getNonceTokenMock.getNonceToken = vi.fn().mockResolvedValueOnce(nonce)
    fetchMocker.once(() =>
      JSON.stringify({
        status: 503,
        error: "Invalid Params: mac_addresses is invalid",
      }),
    )

    const res = await activateUser(clientId, clientSecret, {})
    expect(res.status).toBe(503)
    expect(res.error).toBe("Invalid Params: mac_addresses is invalid")
  })
})
