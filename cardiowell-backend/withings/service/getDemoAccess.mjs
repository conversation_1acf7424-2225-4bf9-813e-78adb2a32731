import { generateSignature } from "../utils/generateSignature.mjs"
import { getNonceToken } from "../utils/getNonceToken.mjs"

// OAuth2 - get access to demo account
export const getDemoAccess = async (clientId, clientSecret) => {
  const nonce = await getNonceToken(clientId, clientSecret)
  const postParams = new URLSearchParams({
    action: "getdemoaccess",
    client_id: clientId,
    nonce,
    signature: generateSignature("getdemoaccess", clientId, clientSecret, nonce),
    scope_oauth2: "user.activity,user.metrics",
  })

  const currentDate = Math.floor(Date.now() / 1000)
  const tokenObj = {
    accessToken: "",
    refreshToken: "",
    expireDate: 0,
  }

  const tokens = await fetch("https://wbsapi.us.withingsmed.net/v2/oauth2", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: postParams,
  })
    .then(response => {
      if (response.status == 200) {
        return response.json()
      }
      return {}
    })
    .then(data => {
      if (data["status"] == 0 && data["body"]) {
        tokenObj.accessToken = data["body"]["access_token"]
        tokenObj.refreshToken = data["body"]["refresh_token"]
        tokenObj.expireDate = currentDate + data["body"]["expires_in"]
      }

      return tokenObj
    })
    .catch(err => {
      console.error(err)
      return tokenObj
    })

  return tokens
}
