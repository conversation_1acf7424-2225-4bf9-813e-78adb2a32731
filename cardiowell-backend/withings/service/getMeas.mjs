import { bpmAppli, bpmMeasTypes } from "../utils/categories.mjs"

// get measurement data
export const getMeas = async ({
  accessToken,
  appli,
  startDate,
  endDate,
  offset = undefined,
}) => {
  const measTypeArray = appli === bpmAppli ? bpmMeasTypes : []

  const postParams = new URLSearchParams({
    action: "getmeas",
    meastypes: measTypeArray,
    category: 1,
    startdate: startDate,
    enddate: endDate,
    ...(offset && { offset }),
  })

  let bodyArray = []
  const data = await fetch("https://wbsapi.us.withingsmed.net/measure", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization: `Bearer ${accessToken}`,
    },
    body: postParams,
  })
    .then(response => {
      if (response.status == 200) {
        return response.json()
      }
      return {}
    })
    .catch(err => {
      console.error(err)
      return {}
    })

  console.log("getMeas data:", JSON.stringify(data))
  if (data["status"] == 0 && data["body"]) {
    const body = data["body"]
    bodyArray.push(body)
    if (body["more"] && body["more"] == 1 && body["offset"]) {
      const nextRow = await getMeas({
        accessToken,
        appli,
        startDate,
        endDate,
        offset: body["offset"],
      })
      bodyArray.push(...nextRow)
    }
  }

  return bodyArray
}
