import { getNonceToken } from "./getNonceToken.mjs"
import { generateSignature } from "./generateSignature.mjs"

// recovery auth code in case access token is deleted
export const recoverAuthCode = async (clientId, clientSecret, email) => {
  const nonce = await getNonceToken(clientId, clientSecret)

  if (!nonce) {
    return ""
  }

  const postParams = new URLSearchParams({
    action: "recoverauthorizationcode",
    client_id: clientId,
    nonce,
    signature: generateSignature(
      "recoverauthorizationcode",
      clientId,
      clientSecret,
      nonce,
    ),
    email,
  })

  return await fetch("https://wbsapi.us.withingsmed.net/v2/oauth2", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: postParams,
  })
    .then(response => {
      if (response.status === 200) {
        return response.json()
      }
      return { status: response.status }
    })
    .then(data => {
      if (data.status === 0 && data["body"]["user"]) {
        return data["body"]["user"]["code"]
      }
      console.error("error recovering auth code:", data)
      return ""
    })
}
