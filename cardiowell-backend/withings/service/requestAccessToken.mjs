import { generateSignature } from "./generateSignature.mjs"
import { getNonceToken } from "./getNonceToken.mjs"

// OAuth2 - request an access token (using signature)
export const requestAccessToken = async ({
  authCode,
  clientId,
  clientSecret,
  redirectUri,
}) => {
  const nonce = await getNonceToken(clientId, clientSecret)

  if (!nonce) {
    return {
      status: 500,
      error: "Error retrieving nonce",
    }
  }

  const postParams = new URLSearchParams({
    action: "requesttoken",
    client_id: clientId,
    nonce,
    signature: generateSignature("requesttoken", clientId, clientSecret, nonce),
    grant_type: "authorization_code",
    code: authCode,
    redirect_uri: redirectUri,
  })

  const currentDate = Math.floor(Date.now() / 1000)

  const tokens = fetch("https://wbsapi.us.withingsmed.net/v2/oauth2", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: postParams,
  })
    .then(response => {
      if (response.status == 200) {
        return response.json()
      }
      return {}
    })
    .then(data => {
      if (data["status"] == 0 && data["body"]) {
        return {
          status: data["status"],
          userId: data["body"]["userid"],
          accessToken: data["body"]["access_token"],
          refreshToken: data["body"]["refresh_token"],
          expireDate: currentDate + data["body"]["expires_in"],
          scope: data["body"]["scope"],
          csrfToken: data["body"]["csrf_token"],
          tokenType: data["body"]["token_type"],
        }
      } else {
        console.error("error requesting access tokens:", data)
      }

      return { status: data["status"], error: data["error"] }
    })
    .catch(err => {
      console.error(err)
      return { status: 500, error: err }
    })

  return tokens
}
