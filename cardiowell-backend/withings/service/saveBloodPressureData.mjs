import withingsBloodPressureData from "../../models/withingsBloodPressureData.js"

// Insert blood pressure data. Return true if successful
export const saveBloodPressureData = async bodyArray => {
  let data = []

  // format data
  bodyArray.forEach(meas => {
    const updateTime = meas["updatetime"]
    const timezone = meas["timezone"]
    const measureGrps = meas["measuregrps"]
    if (measureGrps.length > 0) {
      const bpmData = measureGrps.map(measureGrp => {
        let dia = {}
        let sys = {}
        let pulse = {}
        let sp02 = {}
        const measures = measureGrp["measures"]
        measures.forEach(measure => {
          const type = measure["type"]
          const values = {
            value: measure.value,
            unit: measure.unit,
            algo: measure.algo,
            fm: measure.fm,
          }
          if (type == 9) {
            dia = values
          } else if (type == 10) {
            sys = values
          } else if (type == 11) {
            pulse = values
          } else if (type == 54) {
            sp02 = values
          }
        })

        return {
          updateTime,
          timezone,
          deviceId: measureGrp["hash_deviceid"],
          grpId: measureGrp["grpid"],
          attrib: measureGrp["attrib"],
          date: measureGrp["date"],
          created: measureGrp["created"],
          modified: measureGrp["modified"],
          category: measureGrp["category"],
          modelId: measureGrp["modelid"],
          model: measureGrp["model"],
          comment: measureGrp["comment"],
          dia,
          sys,
          pulse,
          sp02,
        }
      })
      data.push(...bpmData)
    }
  })

  console.log("amount of new data to be inserted:", data.length)
  // insert data
  if (data.length > 0) {
    const docs = await withingsBloodPressureData
      .insertMany(data, { ordered: false })
      .catch(error => {
        console.error(error)
        return false
      })
    return docs
  }
  return false
}
