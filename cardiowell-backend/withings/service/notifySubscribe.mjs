import { generateSignature } from "./generateSignature.mjs"
import { getNonceToken } from "./getNonceToken.mjs"

// Subscribe to Withings to receive notfications
export const notifySubscribe = async ({
  appli,
  clientId,
  clientSecret,
  accessToken,
  callbackUrl,
}) => {
  const nonce = await getNonceToken(clientId, clientSecret)
  if (!nonce) {
    return {
      status: 500,
      error: "Error retrieving nonce",
    }
  }

  const postParams = new URLSearchParams({
    action: "subscribe",
    callbackurl: callbackUrl,
    appli,
    signature: generateSignature("subscribe", clientId, clientSecret, nonce),
    nonce,
    client_id: clientId,
  })

  return await fetch("https://wbsapi.us.withingsmed.net/notify", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization: `Bearer ${accessToken}`,
    },
    body: postParams,
  })
    .then(response => {
      if (response.status === 200) {
        return response.json()
      }
      throw new Error(response.status)
    })
    .then(data => {
      if (data["status"] === 0) {
        return data
      }
      return { status: data["status"], error: data["error"] }
    })
    .catch(err => {
      console.error(err)
      return { status: 500, error: err }
    })
}
