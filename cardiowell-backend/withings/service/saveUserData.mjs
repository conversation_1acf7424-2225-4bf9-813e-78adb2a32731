import withingsUserData from "../../models/withingsUserData.js"

export const saveUserData = async ({ patientId, email, devices, tokens }) => {
  if (devices && devices instanceof Array && devices.length > 0) {
    const deviceArray = devices.map(device => {
      return {
        deviceId: device.deviceid,
        macAddress: device.mac_address,
        serialNumber: device.serial_number,
        deviceType: device.type,
        battery: device.battery,
        model: device.model,
        modelId: device.model_id,
        timezone: device.timezone,
        lastSessionDate: device.last_session_date,
      }
    })

    const { userId, accessToken, refreshToken, expireDate, scope, csrfToken, tokenType } =
      tokens
    const update = {
      patientId,
      email,
      devices: deviceArray,
      userId,
      accessToken,
      refreshToken,
      expireDate,
      scope,
      csrfToken,
      tokenType,
    }
    const filter = { patientId }
    const options = { upsert: true, new: true }

    withingsUserData.findOneAndUpdate(filter, update, options, (err, model) => {
      if (err) {
        console.error(err)
      }
    })
  }
}
