import { refreshAccessToken } from "./refreshAccessToken.mjs"
import { saveAccessTokens } from "./saveAccessTokens.mjs"
import { recoverAuthCode } from "./recoverAuthCode.mjs"
import { requestAccessToken } from "./requestAccessToken.mjs"

export const getAccessToken = async ({
  clientId,
  clientSecret,
  userData,
  userId,
  callbackUrl,
}) => {
  const currentDate = Math.floor(Date.now() / 1000)
  if (currentDate < userData.expireDate) {
    return userData.accessToken
  }

  const newTokens = await refreshAccessToken(
    userData.refreshToken,
    clientId,
    clientSecret,
  )

  if (newTokens?.accessToken && newTokens?.refreshToken) {
    await saveAccessTokens(userId, newTokens)
    return newTokens.accessToken
  }

  if (
    newTokens?.status === 503 &&
    newTokens?.error === "Invalid Params: invalid refresh_token"
  ) {
    console.error("Invalid refresh token, recover auth code")
    const authCode = await recoverAuthCode(clientId, clientSecret, userData.email)

    if (!authCode) {
      return null
    }

    const tokens = await requestAccessToken({
      authCode,
      clientId,
      clientSecret,
      redirectUri: callbackUrl,
    })
    if (tokens.status === 0 && tokens.accessToken && tokens.refreshToken) {
      await saveAccessTokens(userId, tokens)
      return tokens.accessToken
    }
  }

  return null
}
