const { Router } = require("express")
const glucoseRouter = Router()
const { getGlucoseData } = require("../controller/Iglucose")

glucoseRouter.route("/iglucose").post(async (request, response) => {
  try {
    const glucoseData = await getGlucoseData()
    response.status(200).json(glucoseData)
  } catch (err) {
    console.log("ERR", err)
    response.status(500).json(err)
  }
})
module.exports = glucoseRouter
