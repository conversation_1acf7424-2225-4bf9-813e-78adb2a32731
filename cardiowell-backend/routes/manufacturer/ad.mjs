import { Router } from "express"
import { handleMessage } from "../../device/service/ad/handleMessage.mjs"
import { getDeviceByAccessToken } from "../../device/service/ad/getDeviceByAccessToken.mjs"

const adRouter = Router()

adRouter.route("/default").post(async (request, response) => {
  try {
    await handleMessage(request.body)

    return response
      .status(200)
      .json({ message: "Message has been successfully processed" })
  } catch (err) {
    console.log("ERR", err)
    response.status(500).json(err)
  }
})

adRouter.route("/:accessToken").post(async (request, response) => {
  const { accessToken } = request.params
  const device = await getDeviceByAccessToken(accessToken)

  if (!device) {
    response.status(401).json({ error: "Unauthorized" })
    return
  }

  try {
    await handleMessage(request.body, device)

    return response
      .status(200)
      .json({ message: "Message has been successfully processed" })
  } catch (err) {
    console.log("ERR", err)
    response.status(500).json(err)
  }
})

export { adRouter }
