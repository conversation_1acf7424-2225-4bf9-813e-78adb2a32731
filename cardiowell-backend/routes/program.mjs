import { Router } from "express"
import { getProgramsForClinic } from "../program/controller/getProgramsForClinic.mjs"
import {
  createProgramValidator,
  createProgramAndThreshold,
} from "../program/controller/createProgramAndThreshold.mjs"
import { getProgram } from "../program/controller/getProgram.mjs"
import {
  updateProgramAndThreshold,
  updateProgramValidator,
} from "../program/controller/updateProgramAndThreshold.mjs"
import { isLoggedIn } from "./isLoggedIn.mjs"

export const programRouter = Router()

programRouter
  .route("/")
  .post(isLoggedIn, createProgramValidator, createProgramAndThreshold)

programRouter
  .route("/:id")
  .get(isLoggedIn, getProgram)
  .post(isLoggedIn, updateProgramValidator, updateProgramAndThreshold)

programRouter.route("/clinic/:id").get(isLoggedIn, getProgramsForClinic)
