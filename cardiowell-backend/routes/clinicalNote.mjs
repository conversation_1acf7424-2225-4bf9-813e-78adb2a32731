import { Router } from "express"
import {
  createClinicalNoteValidator,
  createClinicalNote,
} from "../clinicalNotes/controller/createClinicalNote.mjs"
import {
  getClinicalNotes,
  getClinicalNotesValidator,
} from "../clinicalNotes/controller/getClinicalNotes.mjs"
import { isLoggedIn } from "./isLoggedIn.mjs"

export const clinicalNoteRouter = Router()

clinicalNoteRouter
  .route("/")
  .post(isLoggedIn, createClinicalNoteValidator, createClinicalNote)

clinicalNoteRouter
  .route("/patient/:id")
  .get(isLoggedIn, getClinicalNotesValidator, getClinicalNotes)
