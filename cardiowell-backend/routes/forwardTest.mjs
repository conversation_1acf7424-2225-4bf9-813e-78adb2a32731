import { Router } from "express"

const TEST_FORWARD_ENDPOINT = process.env.TEST_FORWARD_ENDPOINT
const TEST_FORWARD_API_TOKEN = process.env.TEST_FORWARD_API_TOKEN
const TEST_FORWARD_HEADER_KEY = process.env.TEST_FORWARD_HEADER_KEY || "authorization"

export const forwardTestRouter = Router()

forwardTestRouter.route("/").post(async (request, response) => {
  const token = request.headers[TEST_FORWARD_HEADER_KEY]

  if (!token || token !== TEST_FORWARD_API_TOKEN) {
    return response.status(401).json({ error: "Unauthorized" })
  }

  return response.status(200).send({ message: "Device message received" })
})
