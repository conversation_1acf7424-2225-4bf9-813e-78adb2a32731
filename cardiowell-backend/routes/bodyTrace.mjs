import { Router } from "express"
import {
  handleBodyTraceMessage,
  BODY_TRACE_UNKNOWN_MESSAGE_TYPE_ERROR,
} from "../device/service/bodyTrace/handleBodyTraceMessage.mjs"

const BODY_TRACE_RECEIVING_API_KEY = process.env.BODY_TRACE_RECEIVING_API_KEY
const BODY_TRACE_RECEIVING_API_KEY_VALUE = process.env.BODY_TRACE_RECEIVING_API_KEY_VALUE

const bodyTraceRouter = Router()

bodyTraceRouter.route("/forward").post(async (request, response) => {
  const requestApikey = request.query[BODY_TRACE_RECEIVING_API_KEY]

  if (
    BODY_TRACE_RECEIVING_API_KEY &&
    requestApikey !== BODY_TRACE_RECEIVING_API_KEY_VALUE
  ) {
    return response.status(401).json({ error: "Unauthorized" })
  }

  try {
    await handleBodyTraceMessage(request.body)
    return response.status(200).send({ message: "Device data received" })
  } catch (err) {
    console.error(err)

    if (err.code === BODY_TRACE_UNKNOWN_MESSAGE_TYPE_ERROR) {
      return response.status(400).send({ message: err.message })
    }
    return response.status(500).json(err)
  }
})

export default bodyTraceRouter
