import { Router } from "express"
import { ShortUrl } from "../models/shortUrl.mjs"

export const shortUrlsRouter = Router()

shortUrlsRouter.route("/:shortToken").get(async (req, res) => {
  const { shortToken } = req.params

  const url = await ShortUrl.findOne({ shortToken })

  if (!url) {
    return res.status(404).json({ error: "URL not found" })
  }

  return res.status(200).json({ originalUrl: url.originalUrl })
})
