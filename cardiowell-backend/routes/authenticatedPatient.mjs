export function authenticatedPatient(req, res, next) {
  if (req.isAuthenticated() && req.user.role === "patient") {
    return next()
  }
  return res.status(401).send({ message: "You must be authenticated as a patient" })
}

export function checkAuthenticatedPatient(req, res, patientId) {
  if (!req.isAuthenticated() || req.user.role !== "patient") {
    res.status(401).send({ message: "You must be authenticated as a patient" })
    return false
  }
  if (req.user.id !== patientId) {
    res.status(401).send({ message: "You must be authenticated as a patient" })
    return false
  }
  return true
}
