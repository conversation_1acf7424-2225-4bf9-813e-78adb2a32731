import { Router } from "express"
import mongoose from "mongoose"
import { handleSphygmomanometerMessage } from "../device/service/transtek/handleSphygmomanometerMessage.mjs"
import { saveBloodPressureData } from "../device/service/saveBloodPressureData.mjs"
import { handleBloodGlucoseMeterMessage } from "../device/service/transtek/handleBloodGlucoseMeterMessage.mjs"
import { handleScaleMessage } from "../device/service/transtek/scale/handleScaleMessage.mjs"
import { getBpmByImei } from "../device/service/transtek/bpm/getBpmByImei.mjs"
import { isLoggedIn } from "./isLoggedIn.mjs"
import {
  isRawBgmTelemetry,
  isRawBgmStatus,
  isRawBgmHeartbeat,
} from "../device/service/transtek/bgm/types.mjs"

const transtekRouter = Router()

await mongoose.connect(process.env.mongoUri, {
  useUnifiedTopology: true,
  useNewUrlParser: true,
  useFindAndModify: false,
})
mongoose.Promise = global.Promise
const db = mongoose.connection

const bloodGlucoseApiKey =
  "0DErHLC4jtWYcXkv8dk8L7Y5BJiZZKEOu3d3BkiiHzNWshD04aqNnpah1qacAFDU"

const bloodPressureApiKey =
  "ZBRUbW1QiJVMTIwEulPIDRNLzLWdywBtGHqcjhvFmfXVKpuNCMWhbldge2vCP9zW"

const bloodGlucoseDevice = "bgm_gen1_measure"

const bloodPressureDevice = "bpm_gen2_measure"

const weightScaleDevice = "scale_gen2_measure"

/*
transtekRouter.route("/bodyTrace/forward").post(async (request, response) => {
    const authHeader = request.headers['authorization'];

    if (!authHeader || authHeader !== bloodGlucoseApiKey) { 
        return response.status(401).json({ error: 'Unauthorized' });
    }

    try {
        // check if it is has: imei
        if (request.body["status"] && request.body["messageType"] === "status") {
          await handleSphygmomanometerMessage(request.body);

          return response
            .status(200)
            .send({ message: "Device data received" });
        }
        return response.status(400).send({ message: "Post received, unknown message type" });
    } catch (err) {
        return response.status(500).json(err);
    }
});
*/

transtekRouter.route("/bloodGlucose/forwardtelemetry").post(async (request, response) => {
  const authHeader = request.headers["authorization"]

  if (!authHeader || authHeader !== bloodGlucoseApiKey) {
    return response.status(401).json({ error: "Unauthorized" })
  }
  try {
    if (
      isRawBgmTelemetry(request.body) ||
      isRawBgmStatus(request.body) ||
      isRawBgmHeartbeat(request.body)
    ) {
      await handleBloodGlucoseMeterMessage(request.body)

      return response.status(200).send({ message: "CardioWell Blood Glucose POST!" })
    }

    if (request.body["data"] && request.body["data"]["data_type"] === weightScaleDevice) {
      await handleScaleMessage(request.body)
      return response.status(200).send({ message: "CardioWell Weight Scale POST!" })
    }

    if (request.body["data"] && request.body["messageType"] === "telemetry") {
      const dataType = request.body["data"]["data_type"]

      if (dataType === bloodPressureDevice) {
        await handleSphygmomanometerMessage(request.body, true)
        return response.status(200).send({ message: "CardioWell BPM received" })
      }

      return response
        .status(400)
        .send({ message: "Telemetry data received, unknown data type" })
    }

    if (request.body["status"] && request.body["messageType"] === "status") {
      await handleSphygmomanometerMessage(request.body)

      return response.status(200).send({ message: "Device status received" })
    }
    return response.status(400).send({ message: "Post received, unknown message type" })
  } catch (err) {
    console.error(err)
    return response.status(500).json(err)
  }
})

// deprecated, use bloodGlucose/forwardtelemetry for all transtek requests
transtekRouter
  .route("/bloodPressure/forwardtelemetry")
  .post(async (request, response) => {
    const authHeader = request.headers["authorization"]

    if (!authHeader || authHeader !== bloodPressureApiKey) {
      response.status(401).json({ error: "Unauthorized" })
      return
    }

    try {
      // Telemetry
      if (request.body["data"] && request.body["messageType"] === "telemetry") {
        const dataType = request.body["data"]["data_type"]
        if (dataType === bloodPressureDevice) {
          saveBloodPressureData(request.body, response)
          return response.status(200).send({ message: "CardioWell BPM POST!" })
        }

        return response
          .status(400)
          .send({ message: "Telemetry data received, incorrect data type" })
      }

      // Status
      if (request.body["status"] && request.body["messageType"] === "status") {
        return response.status(200).send({ message: "Device status received" })
      }
    } catch (err) {
      return response.status(500).json(err)
    }

    return response.status(400).send({ message: "Post received, unknown message type" })
  })

//isLoggedIn
transtekRouter
  .route("/bloodPressure/:imei")
  .get(isLoggedIn, async (request, response) => {
    try {
      const { imei } = request.params
      const bpmData = await getBpmByImei(imei)

      if (!bpmData || bpmData.length === 0) {
        return response.status(200).json({ data: [] })
      }

      return response.status(200).json(bpmData)
    } catch (error) {
      console.error("Error retrieving BPM data:", error)
      return response.status(200).json({ data: [] })
    }
  })

export default transtekRouter
