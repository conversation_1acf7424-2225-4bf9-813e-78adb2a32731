import { Router } from "express"
import {
  createCustomer,
  createCustomerValidator,
} from "../customer/controller/createCustomer.mjs"
import {
  updateCustomer,
  updateCustomerValidator,
} from "../customer/controller/updateCustomer.mjs"
import { getCustomers } from "../customer/controller/getCustomers.mjs"
import { getOneCustomer } from "../customer/controller/getOneCustomer.mjs"
import {
  deleteCustomerValidator,
  deleteCustomer,
} from "../customer/controller/deleteCustomer.mjs"
import { getCustomerDetails } from "../customer/controller/getCustomerDetails.mjs"
import {
  customerTestsValidator,
  customerTests,
} from "../customer/controller/customerTests.mjs"
import {
  testEndpointStatusMessage,
  testEndpointStatusMessageValidator,
} from "../customer/controller/testEndpointStatusMessage.mjs"
import {
  testEndpointTelemetryMessage,
  testEndpointTelemetryMessageValidator,
} from "../customer/controller/testEndpointTelemetryMessage.mjs"
import {
  getCustomerTestReportsValidator,
  getCustomerTestReports,
} from "../customer/controller/getCustomerTestReports.mjs"

export const customerRouter = Router()

customerRouter.route("/").post(createCustomerValidator, createCustomer).get(getCustomers)

customerRouter
  .route("/:id")
  .put(updateCustomerValidator, updateCustomer)
  .delete(deleteCustomerValidator, deleteCustomer)
  .get(getOneCustomer)

customerRouter
  .route("/testEndpointStatus")
  .post(testEndpointStatusMessageValidator, testEndpointStatusMessage)

customerRouter
  .route("/testEndpointTelemetry")
  .post(testEndpointTelemetryMessageValidator, testEndpointTelemetryMessage)

customerRouter.route("/:id/details").get(getCustomerDetails)

customerRouter
  .route("/:id/tests")
  .put(customerTestsValidator, customerTests)
  .get(getCustomerTestReportsValidator, getCustomerTestReports)
