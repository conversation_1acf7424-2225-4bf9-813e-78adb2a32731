import { Router } from "express"
import {
  createDevice,
  createDeviceValidator,
} from "../device/controller/createDevice.mjs"
import { getDevices } from "../device/controller/getDevices.mjs"
import {
  deleteDevice,
  deleteDeviceValidator,
} from "../device/controller/deleteDevice.mjs"
import { getOneDevice } from "../device/controller/getOneDevice.mjs"
import {
  updateDevice,
  updateDeviceValidator,
} from "../device/controller/updateDevice.mjs"
import { getDeviceDetails } from "../device/controller/getDeviceDetails.mjs"
import { deviceTestsValidator, deviceTests } from "../device/controller/deviceTests.mjs"
import {
  bulkCreateDevicesValidator,
  bulkCreateDevices,
} from "../device/controller/bulkCreateDevices.mjs"
import { loadTestData } from "../device/controller/loadTestData.mjs"
import { getTestDataUploads } from "../device/controller/getTestDataUploads.mjs"
import { deleteTestData } from "../device/controller/deleteTestData.mjs"
import multer from "multer"

const upload = multer({ dest: "uploads/" })

const authorize = (request, response, next) => {
  const authHeader = request.headers["authorization"]
  if (!authHeader || authHeader !== process.env.LOAD_TEST_DATA_API_KEY) {
    return response.status(401).json({ error: "Forbidden" })
  }
  return next()
}

export const deviceRouter = Router()

deviceRouter.route("/").post(createDeviceValidator, createDevice).get(getDevices)

deviceRouter.route("/bulk").post(bulkCreateDevicesValidator, bulkCreateDevices)

deviceRouter.route("/test-data-uploads").get(authorize, getTestDataUploads)
deviceRouter.route("/test-data-uploads/:id").delete(authorize, deleteTestData)

deviceRouter
  .route("/:id")
  .put(updateDeviceValidator, updateDevice)
  .delete(deleteDeviceValidator, deleteDevice)
  .get(getOneDevice)

deviceRouter.route("/:id/details").get(getDeviceDetails)

deviceRouter.route("/:id/tests").put(deviceTestsValidator, deviceTests)

deviceRouter
  .route("/:imei/load-test-data")
  .post(authorize, upload.single("file"), loadTestData)
