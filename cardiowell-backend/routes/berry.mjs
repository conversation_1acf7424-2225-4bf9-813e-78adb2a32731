import { Router } from "express"
import { handleBerryMessage } from "../device/service/berry/handleBerryMessage.mjs"

const berryRouter = Router()

const BERRY_RECEIVING_API_TOKEN = process.env.BERRY_RECEIVING_API_TOKEN

berryRouter.route("/oximeter").post(async (request, response) => {
  const authHeader = request.headers["authorization"]

  if (!authHeader || authHeader !== BERRY_RECEIVING_API_TOKEN) {
    response.status(401).json({ error: "Unauthorized" })
    return
  }

  try {
    await handleBerryMessage(request.body)

    return response.status(200).json({ message: "CardioWell oximeter POST!" })
  } catch (err) {
    console.log("ERR", err)
    response.status(500).json(err)
  }
})

export default berryRouter
