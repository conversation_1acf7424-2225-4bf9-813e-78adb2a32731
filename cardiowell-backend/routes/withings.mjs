import { Router } from "express"
import {
  activateValida<PERSON>,
  activate,
  devActivate,
} from "../withings/controller/activate.mjs"
import { updateValidator, update, devUpdate } from "../withings/controller/update.mjs"
import { recoverValidator, recover } from "../withings/controller/recover.mjs"
import { requestTokensValidator, requestTokens } from "../withings/controller/tokens.mjs"

export const withingsRouter = Router()

withingsRouter.route("/activate").post(activateValidator, activate)

withingsRouter.route("/dev/activate").post(activateValidator, devActivate)

withingsRouter.route("/update").post(updateValidator, update)

withingsRouter.route("/dev/update").post(updateValidator, devUpdate)

// for development only
withingsRouter.route("/recover").post(recoverValidator, recover)

withingsRouter.route("/requesttokens").post(requestTokensValidator, requestTokens)
