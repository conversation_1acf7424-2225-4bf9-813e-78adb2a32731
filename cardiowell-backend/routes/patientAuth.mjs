import { Router } from "express"
import Patient from "../models/patient.js"
import passport from "passport"
import { Strategy } from "passport-local"
import { decodeToken, generateToken } from "../auth/token.mjs"
import { shortenUrl } from "../auth/shortUrls.mjs"

const PATIENT_SESSION_LIFETIME = 30 * 24 * 60 * 60 * 1000

passport.use(
  "magic-link",
  new Strategy(
    {
      usernameField: "token",
      passwordField: undefined,
      passReqToCallback: true,
    },
    async (req, token, password, done) => {
      try {
        const tokenInfo = decodeToken(token)
        const patient = await Patient.findById(tokenInfo["patientId"])
        return done(null, patient)
      } catch (err) {
        return done(err)
      }
    },
  ),
)

const patientAuthRouter = Router()

patientAuthRouter.post("/magic-link", async (req, res, next) => {
  passport.authenticate("magic-link", (err, user, info) => {
    if (err || !user) {
      return res.status(401).json({ message: "Unauthorized" })
    }
    req.login(user, loginErr => {
      if (loginErr) {
        return res.status(500).json({ message: "Login failed" })
      }

      req.session.cookie.maxAge = PATIENT_SESSION_LIFETIME

      return res.status(200).json({ message: "Authentication successful" })
    })
  })(req, res, next)
})

const createPatientMagicLink = (patientId, deviceImei) => {
  const payload = {
    type: "patient-magic-link",
    patientId: patientId,
    deviceImei: deviceImei,
    createdAt: Math.floor(Date.now() / 1000),
  }
  const token = generateToken(payload)
  const siteOrigin = process.env.WEB_APP_ORIGIN || "http://localhost:3010"
  return `${siteOrigin}/devices/${deviceImei}/magic-link/${token}`
}

const createShortBPMagicLink = patient => {
  const deviceImei = patient.getAnyBpIMEI()
  if (!deviceImei) {
    throw new Error("Patient has no BP devices")
  }
  const magicLink = createPatientMagicLink(patient.id, deviceImei)
  return shortenUrl(magicLink)
}

export { patientAuthRouter, createPatientMagicLink, createShortBPMagicLink }
