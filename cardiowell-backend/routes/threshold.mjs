import { Router } from "express"
import {
  updatePatientThreshold,
  updatePatientThresholdValidator,
} from "../threshold/controller/updatePatientThreshold.mjs"
import {
  createNewThreshold,
  createThresholdValidator,
} from "../threshold/controller/createThreshold.mjs"
import { isLoggedIn } from "./isLoggedIn.mjs"

export const thresholdRouter = Router()

thresholdRouter.route("/").post(isLoggedIn, createThresholdValidator, createNewThreshold)

thresholdRouter
  .route("/patient")
  .post(isLoggedIn, updatePatientThresholdValidator, updatePatientThreshold)
