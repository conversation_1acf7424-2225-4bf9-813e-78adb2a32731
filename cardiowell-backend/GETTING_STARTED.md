# Cardiowell Backend Getting Started

The Cardiowell Backend contains the server application code for Cardiowell's applications. It handles requests from the front-end as well as API calls from external services. It also makes external API calls to third party services and is connected to [MongoDB](https://cloud.mongodb.com/v2/5e4ded66af4352567f365fa6).

## Installation

**Requirements**
* Node Version > v20.13.0
* Yarn > v1.22.22

**Installation**
1. Clone the repo
	```sh
	<NAME_EMAIL>:Cardiowell/cardiowell-backend.git
	```
2. Install Node Packages with Yarn
	```sh
	yarn
	```
3. Create a `.env` file with the following
	```
	NODE_ENV="development"
	```
4. Navigate to the [Heroku dashboard](https://dashboard.heroku.com/apps/cardiowell-application) and go to the **Settings** tab
5. Click on **Reveal Config Vars** and copy all variables into the `.env` file. For example:
	```
	mongoPass="copy_mongodb_password_here"
	sendgridAPI="copy_sendgrid_api_here"
	twilioToken="copy_twilio_token_here"
	```

## Deployment

1. Push the changes into the `master` branch of the server code
2. Navigate to the **cardiowell-application** project in Heroku: <br />[https://dashboard.heroku.com/apps/cardiowell-application](https://dashboard.heroku.com/apps/cardiowell-application)
3. Go the the **Deploy** tab, and scroll down to the **Manual Deploy** option
4. Choose the **master** branch in the dropdown, then hit **Deploy Branch**
5. View project at https://careportal.cardiowell.io

## Available Scripts

In the project directory, you can run:  

### `yarn start`

Runs the app in the development mode

Open [http://localhost:8081](http://localhost:8081) to view the current build in the browser.

### `yarn test`
  
Launches the Vitest test runner

### `yarn auth:patient <idOrPhone>`

Creates a patient session in your default browser.

```sh
yarn auth:patient 64fba2bbfff797cc5a30133c
```
