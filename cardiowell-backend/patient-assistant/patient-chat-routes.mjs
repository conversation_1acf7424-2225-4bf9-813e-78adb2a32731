import { Router } from "express"
import { authenticatedPatient } from "../routes/authenticatedPatient.mjs"
import { findMessagesByPatientId } from "./messages.collection.mjs"
import { webMessageFromPatient } from "../models/message.mjs"
import { processNewMessage } from "./assistant.mjs"

export const patientChatRouter = Router()

patientChatRouter.route("/messages").get(authenticatedPatient, async (req, res) => {
  const patient = req.user
  const messages = await findMessagesByPatientId(patient.id)

  return res.status(200).json({
    messages,
  })
})

patientChatRouter.route("/messages").post(authenticatedPatient, async (req, res) => {
  const patient = req.user
  const { messageText } = req.body

  const message = webMessageFromPatient(patient.id, messageText)
  await message.save()

  const responseMessage = await processNewMessage(patient, message)

  return res.status(200).json({
    messages: [message, responseMessage],
  })
})
