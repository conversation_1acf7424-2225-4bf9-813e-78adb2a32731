import { Router } from "express"
import { phoneContact, textFromPatient } from "../models/message.mjs"
import { processNewMessage } from "./assistant.mjs"
import { findMessagesByPatientId } from "./messages.collection.mjs"
import patient from "../models/patient.js"
import { textPatientWithoutSaving } from "../deviceUpdates/service/sendText.mjs"

export const testPatientChatRouter = Router()

testPatientChatRouter.route("/phone/:phone").get(async (req, res) => {
  const { phone } = req.params

  const patientSender = await patient.findOne({
    cellNumber: phone,
  })
  if (!patientSender) {
    return res.status(404).json({
      error: "patient-not-found",
      message: "This cell phone number does not exist in any account in our records",
    })
  }

  const messages = await findMessagesByPatientId(patientSender._id)

  return res.status(200).json(messages)
})

testPatientChatRouter.route("/phone/:phone").post(async (req, res) => {
  const { phone } = req.params
  const { messageText } = req.body

  const patientSender = await patient.findOne({
    cellNumber: phone,
  })
  if (!patientSender) {
    return res.status(404).json({
      error: "patient-not-found",
      message: "This cell phone number does not exist in any account in our records",
    })
  }

  const systemContact = phoneContact(process.env.twilioNumber)
  const message = textFromPatient(
    patientSender._id,
    phoneContact(phone),
    systemContact,
    messageText,
  )
  await message.save()

  const responseMessage = await processNewMessage(patientSender, message)
  await textPatientWithoutSaving(phone, responseMessage.messageText)

  const messages = await findMessagesByPatientId(patientSender._id)

  return res.status(200).json(messages)
})
