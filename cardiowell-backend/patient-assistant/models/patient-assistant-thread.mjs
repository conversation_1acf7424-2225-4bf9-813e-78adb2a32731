import mongoose from "mongoose"

const PatientAssistantThreadSchema = new mongoose.Schema({
  user: {
    userId: { type: String, required: true },
    userType: { type: String, required: true },
  },
  threadId: { type: String, required: true },
  lastMessageId: { type: String },
})

PatientAssistantThreadSchema.index(
  { "user.userId": 1, "user.userType": 1 },
  { unique: true },
)

export const PatientAssistantThread = mongoose.model(
  "PatientAssistantThread",
  PatientAssistantThreadSchema,
  "patient-assistant-thread",
)
