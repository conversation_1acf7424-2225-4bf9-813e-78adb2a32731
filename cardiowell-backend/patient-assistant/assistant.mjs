import OpenAI from "openai"
import { PatientAssistantThread } from "./models/patient-assistant-thread.mjs"
import { phoneContact, textToPatient } from "../models/message.mjs"
import { createShortBPMagicLink } from "../routes/patientAuth.mjs"
import { findThreadByPatientId } from "./threads.collection.mjs"
import { findMessagesByPatientId } from "./messages.collection.mjs"
import { Sentry } from "../observability/sentry.mjs"

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

const messageToOpenAIMessage = message => ({
  content: message.messageText,
  role: message.isFromUser() ? "user" : "assistant",
})

const messagesToOpenAIMessages = messages => messages.map(messageToOpenAIMessage)

const createNewThread = async patient => {
  const messages = await findMessagesByPatientId(patient._id) // All messages from oldest to newest
  const assistantMessages = messagesToOpenAIMessages(messages)

  // OpenAI has a limit of 32 messages for thread creation
  const first32Messages = assistantMessages.slice(0, 32)
  const remainingMessages = assistantMessages.slice(32)

  const openaiThread = await openai.beta.threads.create({
    messages: first32Messages,
  })
  for (const message of remainingMessages) {
    await openai.beta.threads.messages.create(openaiThread.id, message)
  }

  const thread = PatientAssistantThread({
    user: {
      userId: patient._id,
      userType: "patient",
    },
    threadId: openaiThread.id,
    lastMessageId: messages.length > 0 ? messages[messages.length - 1].messageId : null,
  })
  await thread.save()
  return thread
}

const syncExistingThread = async (thread, patient) => {
  const messages = await findMessagesByPatientId(patient._id) // All messages from oldest to newest
  const lastSyncedIndex = messages.findIndex(m => m.messageId == thread.lastMessageId)
  const newMessages =
    lastSyncedIndex === -1 ? messages : messages.slice(lastSyncedIndex + 1)

  for (const message of newMessages) {
    await openai.beta.threads.messages.create(
      thread.threadId,
      messageToOpenAIMessage(message),
    )
    thread.lastMessageId = message.messageId
    await thread.save()
  }
}

const runThreadAndSave = async (patient, thread, contact) => {
  let run = await openai.beta.threads.runs.createAndPoll(thread.threadId, {
    assistant_id: process.env.OPENAI_ASSISTANT_ID,
  })

  while (run.status === "requires_action" && run.required_action !== null) {
    const calls = run.required_action.submit_tool_outputs.tool_calls
    const outputs = await Promise.all(
      calls.map(async call => {
        let output = "Processed"
        if (call.function.name === "create_magic_link") {
          output = await createShortBPMagicLink(patient)
        }
        return {
          tool_call_id: call.id,
          output: output,
        }
      }),
    )
    run = await openai.beta.threads.runs.submitToolOutputsAndPoll(
      thread.threadId,
      run.id,
      {
        tool_outputs: outputs,
      },
    )
  }

  const messages = await openai.beta.threads.messages.list(thread.threadId, {
    order: "desc",
    runId: run.id,
  })
  const assistantMessage = messages.data[0]

  const systemContact = phoneContact(process.env.twilioNumber)
  const newMessage = textToPatient(
    patient._id,
    contact,
    systemContact,
    assistantMessage.content[0].text.value,
  )
  await newMessage.save()

  thread.lastMessageId = newMessage.messageId
  await thread.save()

  return newMessage
}

export const processNewMessage = async (patient, message) => {
  let thread = await Sentry.startSpan(
    { op: "db.mongo.query", name: "DB Query: Find existing PatientAssistantThread" },
    async () => {
      return findThreadByPatientId(patient._id)
    },
  )

  if (!thread) {
    thread = await Sentry.startSpan(
      {
        op: "assistant.create_new_thread",
        name: "Assistant: Create new Thread in OpenAI",
      },
      async () => {
        return createNewThread(patient)
      },
    )
  } else {
    await Sentry.startSpan(
      {
        op: "assistant.sync_existing_thread",
        name: "Assistant: Sync an existing Thread",
      },
      async () => {
        return syncExistingThread(thread, patient)
      },
    )
  }

  return await Sentry.startSpan(
    { op: "assistant.run_thread_and_save", name: "Assistant: Run the Thread and Save" },
    async () => {
      return runThreadAndSave(patient, thread, message.getUserContact())
    },
  )
}
