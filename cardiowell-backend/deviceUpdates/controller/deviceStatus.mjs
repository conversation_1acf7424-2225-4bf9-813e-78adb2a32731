import { validationResult } from "express-validator"
import { getPatientFromImei } from "../service/getPatientFromImei.mjs"

export const getDeviceStatus = async (req, res) => {
  try {
    validationResult(req).throw()
    const { imei } = req.params
    const result = await getPatientFromImei(imei)
    if (!result) {
      return res.status(404).send({
        error: "imei-account-not-found",
        message: "This IMEI does not exist in any account in our records",
      })
    }
    return res.status(200).send({
      message: "Found",
      activated: !!result.deviceNotificationsEnabled && !!result.cellNumber,
    })
  } catch (error) {
    console.error(error)
    return res.status(400).json(error)
  }
}
