import { validationResult } from "express-validator"
import { translateTimezone } from "../../users/service/patientData.mjs"
import { getMeasurements } from "../service/getMeasurementsAndTimezone.mjs"
import { getPatientFromImei } from "../service/getPatientFromImei.mjs"
import { checkAuthenticatedPatient } from "../../routes/authenticatedPatient.mjs"

export const getDeviceMeasures = async (req, res) => {
  try {
    validationResult(req).throw()
    const { imei } = req.params

    const patient = await getPatientFromImei(imei)

    if (!patient) {
      return res.status(404).send({
        error: "imei-account-not-found",
        message: "This IMEI does not exist in any account in our records",
      })
    }

    if (!checkAuthenticatedPatient(req, res, patient.id)) {
      return
    }

    if (!patient.deviceNotificationsEnabled) {
      return res.status(409).send({
        error: "imei-account-not-registered",
        message: "The account tied to this IMEI has not been signed up for notifications",
      })
    }

    const measurements = await getMeasurements(patient, imei)
    return res.status(200).json({
      ...measurements,
      imei,
      firstName: patient.firstName,
      lastName: patient.lastName,
      timeZone: translateTimezone(patient.timeZone),
      deviceNotificationsEnabled: patient.deviceNotificationsEnabled,
    })
  } catch (error) {
    console.error(error)
    return res.status(500).json(error)
  }
}
