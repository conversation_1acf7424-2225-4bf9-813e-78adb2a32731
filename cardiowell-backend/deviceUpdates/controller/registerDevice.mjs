import { body, validationResult, matchedData } from "express-validator"
import { textPatientAndSave } from "../service/sendText.mjs"
import { getPatientFromImei } from "../service/getPatientFromImei.mjs"
import { createPatientMagicLink } from "../../routes/patientAuth.mjs"
import { shortenUrl } from "../../auth/shortUrls.mjs"

export const registerValidator = [
  body("cellNumber").isMobilePhone().escape(),
  body("firstName").isString().escape(),
  body("lastName").isString().escape(),
  body("imei").isIMEI().escape(),
]

export const registerDevice = async (req, res) => {
  try {
    validationResult(req).throw()
    const { imei, firstName, lastName, cellNumber } = matchedData(req)
    /**
     * The patient with IMEI and Clinic are setup prior to device shipping
     * Only supporting Transtek Blood Pressure Devices currently
     **/
    const patientUser = await getPatientFromImei(imei)

    if (!patientUser) {
      return res.status(404).send({
        error: "imei-account-not-found",
        message: "This IMEI does not exist in any account in our records",
        imei,
      })
    }

    if (patientUser.deviceNotificationsEnabled && patientUser.cellNumber) {
      return res.status(409).send({
        error: "imei-account-registered",
        message:
          "The account tied to this IMEI has already been signed up for notifications",
        imei,
      })
    }

    patientUser.cellNumber = cellNumber
    patientUser.deviceNotificationsEnabled = true
    if (firstName) patientUser.firstName = firstName
    if (lastName) patientUser.lastName = lastName
    await patientUser.save()

    const magicLink = await shortenUrl(createPatientMagicLink(patientUser._id, imei))

    // send text notification
    textPatientAndSave(
      patientUser._id,
      cellNumber,
      `Hi ${firstName}, this is your BP Buddy from Cardiowell. Here is a link to view your results anytime: ${magicLink}. Message and data rates may apply. If you wish to stop receiving this message, reply STOP. Contact <NAME_EMAIL> for assistance.`,
    )

    return res.status(201).send({ message: "Success", imei })
  } catch (error) {
    console.error(error)
    return res.status(400).json(error)
  }
}
