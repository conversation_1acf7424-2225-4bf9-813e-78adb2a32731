import { validationResult } from "express-validator"
import { getPatientFromImei } from "../service/getPatientFromImei.mjs"
import { createPatientMagicLink } from "../../routes/patientAuth.mjs"
import { shortenUrl } from "../../auth/shortUrls.mjs"
import { textPatientAndSave } from "../service/sendText.mjs"

function maskPhoneNumber(cellNumber) {
  if (!cellNumber || typeof cellNumber !== "string") return ""
  return cellNumber.replace(/.(?=.{4})/g, "*")
}

export const generateMagicLink = async (req, res) => {
  try {
    validationResult(req).throw()
    const { imei } = req.params

    const patient = await getPatientFromImei(imei)

    if (!patient) {
      return res.status(404).send({
        error: "imei-account-not-found",
        message: "This IMEI does not exist in any account in our records",
      })
    }

    if (!patient.deviceNotificationsEnabled) {
      return res.status(409).send({
        error: "imei-account-not-registered",
        message: "The account tied to this IMEI has not been signed up for notifications",
      })
    }

    if (!patient.cellNumber) {
      return res.status(409).send({
        error: "no-phone-on-file",
        message: "No phone number on file for this account",
      })
    }

    const link = await shortenUrl(createPatientMagicLink(patient._id, imei))

    await textPatientAndSave(
      patient._id,
      patient.cellNumber,
      `Tap this link ${link} to view your Cardiowell blood pressure results`,
    )

    return res.status(200).json({
      phone: maskPhoneNumber(patient.cellNumber),
    })
  } catch (error) {
    console.error(error)
    return res.status(500).json(error)
  }
}
