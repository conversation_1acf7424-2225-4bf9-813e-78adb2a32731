import twilio from "twilio"
import { phoneContact, textToPatient } from "../../models/message.mjs"

const accountSid = process.env.twilioSID
const authToken = process.env.twilioToken
const client = twilio(accountSid, authToken)

export const sendText = async (phoneNumber, messageText) => {
  await client.messages.create({
    body: messageText,
    from: process.env.twilioNumber,
    to: phoneNumber,
  })
}

export const textPatientAndSave = async (patientId, phoneNumber, messageText) => {
  const patientContact = phoneContact(phoneNumber)
  const systemContact = phoneContact(process.env.twilioNumber)
  const message = textToPatient(patientId, patientContact, systemContact, messageText)
  await sendText(patientContact.contact, messageText)
  await message.save()
}

export const textPatientWithoutSaving = async (phoneNumber, messageText) => {
  await sendText(phoneNumber, messageText)
}
