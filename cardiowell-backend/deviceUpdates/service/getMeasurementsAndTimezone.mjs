import mongoose from "mongoose"

mongoose.connect(process.env.mongoUri, {
  useUnifiedTopology: true,
  useNewUrlParser: true,
  useFindAndModify: false,
})
mongoose.Promise = global.Promise
const db = mongoose.connection

const getTestFilters = showTestData => {
  return showTestData
    ? {
        transtekBpmTestFilter: {},
        btBpmTestFilter: {},
        bodyTraceMessagesTestFilter: [],
      }
    : {
        transtekBpmTestFilter: { isTest: { $ne: true } },
        btBpmTestFilter: { isTest: { $ne: true } },
        bodyTraceMessagesTestFilter: [{ $ne: ["$message.isTest", true] }],
      }
}

export const getMeasurements = async (user, imei) => {
  const { transtekBpmTestFilter, btBpmTestFilter, bodyTraceMessagesTestFilter } =
    getTestFilters(Boolean(user.showTestData))

  if (user?.ttBpIMEI === imei) {
    const bpm = await db
      .collection("Transtek_BPM")
      .find({ imei, ...transtekBpmTestFilter })
      .sort({ ts: -1 })
      .toArray()
    return {
      bpm,
      manufacturer: "Transtek",
    }
  } else if (user?.bpIMEI === imei) {
    const bodytrace = db
      .collection("BT_BPM")
      .find({ imei, ...btBpmTestFilter })
      .sort({ _created_at: -1 })
      .toArray()
    const messages = db
      .collection("bodytracemessages")
      .find({
        $expr: {
          $and: [
            { $eq: ["$message.imei", imei] },
            { $ne: [{ $type: "$message.values.systolic" }, "missing"] },
            { $ne: [{ $type: "$message.values.diastolic" }, "missing"] },
            ...bodyTraceMessagesTestFilter,
          ],
        },
      })
      .sort({ createdAt: -1 })
      .toArray()
    const [bpm, bpmMessages] = await Promise.all([bodytrace, messages])
    return {
      bpm,
      bpmMessages,
      manufacturer: "BodyTrace",
    }
  }

  return {
    bpm: [],
    manufacturer: "Unknown",
  }
}
