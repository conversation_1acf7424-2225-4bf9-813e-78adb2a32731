import { ShortUrl } from "../models/shortUrl.mjs"
import { nanoid } from "nanoid"

export async function shortenUrl(originalUrl) {
  let shortToken
  let urlDoc

  do {
    shortToken = nanoid(8)
    urlDoc = await ShortUrl.findOne({ shortToken })
  } while (urlDoc)

  urlDoc = new ShortUrl({ originalUrl, shortToken })
  await urlDoc.save()

  const siteOrigin = process.env.WEB_APP_ORIGIN || "http://localhost:3010"
  return `${siteOrigin}/s/${shortToken}`
}
