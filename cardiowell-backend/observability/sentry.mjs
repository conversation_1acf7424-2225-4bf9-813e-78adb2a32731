import * as Sentry from "@sentry/node"
import { nodeProfilingIntegration } from "@sentry/profiling-node"

const SENTRY_DSN = process.env.SENTRY_DSN
const SERVICE_ENV = process.env.SERVICE_ENV ?? "unknown"
const NODE_ENV = process.env.NODE_ENV

const isSentryEnabled = () =>
  SENTRY_DSN && NODE_ENV === "production" && !["unknown", "local"].includes(SERVICE_ENV)

if (isSentryEnabled()) {
  Sentry.init({
    dsn: SENTRY_DSN,
    integrations: [nodeProfilingIntegration(), Sentry.expressIntegration()],
    // Tracing
    tracesSampleRate: 1.0, //  Capture 100% of the transactions
    environment: SERVICE_ENV,
  })
}

export { Sentry }
