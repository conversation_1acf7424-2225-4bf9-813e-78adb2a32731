import { ClinicalNote } from "../../models/clinicalNote.mjs"

export const getClinicalNotesByPatient = async ({ patientId, from }) => {
  return ClinicalNote.aggregate([
    {
      $match: { patientId, createdAt: { $gte: new Date(from) } },
    },
    { $sort: { createdAt: -1 } },
    {
      $lookup: {
        from: "providers",
        let: {
          localProviderId: { $toObjectId: "$providerId" },
        },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", "$$localProviderId"] } } },
          { $limit: 1 },
          {
            $project: {
              _id: 0,
              firstName: 1,
              lastName: 1,
            },
          },
        ],
        as: "providerName",
      },
    },
    {
      $unwind: {
        path: "$providerName",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 1,
        patientId: 1,
        createdAt: 1,
        providerId: 1,
        note: 1,
        providerName: 1,
        iv: 1,
      },
    },
  ])
}
