import crypto from "crypto"

const algorithm = "aes-256-cbc"
const key = Buffer.from(process.env.clinicalNote<PERSON>ey)

export const encryptNote = note => {
  const iv = crypto.randomBytes(16)
  const cipher = crypto.createCipheriv(algorithm, key, iv)
  const encrypted = cipher.update(note, "utf8", "hex") + cipher.final("hex")
  return {
    iv,
    encrypted,
  }
}

export const decryptNote = ({ iv, encryptedNote }) => {
  if (!iv?.buffer) {
    return ""
  }
  const decipher = crypto.createDecipheriv(algorithm, key, iv?.buffer)
  const decrypted = decipher.update(encryptedNote, "hex", "utf8") + decipher.final("utf8")
  return decrypted
}
