import { validationResult, param, query } from "express-validator"
import { getClinicalNotesByPatient } from "../service/getClinicalNotesByPatient.mjs"
import { decryptNote } from "../service/encryption.mjs"

export const getClinicalNotesValidator = [
  param("id").isString().escape(),
  query("from").isISO8601().escape(),
] // TODO: add query validation?

export const getClinicalNotes = async (req, res) => {
  try {
    validationResult(req).throw()
    const { id } = req.params
    const { from } = req.query
    const clinicalNotes = await getClinicalNotesByPatient({ patientId: id, from })
    const decryptedNotes = clinicalNotes.map(clinicalNote => {
      const { note, iv, patientId, providerName, createdAt } = clinicalNote
      return {
        patientId,
        providerName,
        createdAt,
        note: decryptNote({
          encryptedNote: note,
          iv,
        }),
      }
    })
    return res.status(200).send({ message: "Success", clinicalNotes: decryptedNotes })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
}
