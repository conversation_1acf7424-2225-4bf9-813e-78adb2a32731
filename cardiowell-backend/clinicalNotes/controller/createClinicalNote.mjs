import { body, validationResult, matchedData } from "express-validator"
import { createClinicalNote as createNote } from "../service/createClinicalNote.mjs"
import { encryptNote } from "../service/encryption.mjs"
import Provider from "../../models/provider.js"

export const createClinicalNoteValidator = [
  body("patientId").isString().notEmpty().escape(),
  body("createdAt").isISO8601().optional().escape(),
  body("providerId").isString().notEmpty().escape(),
  body("note").isString().escape(),
]

export const createClinicalNote = async (req, res) => {
  try {
    validationResult(req).throw()
    const requestData = matchedData(req)
    const { patientId, createdAt, providerId, note } = requestData
    const { iv, encrypted } = encryptNote(note)
    const newNote = await createNote({
      patientId,
      createdAt,
      providerId,
      note: encrypted,
      iv,
    })
    const provider = await Provider.findById(providerId)
    const clinicalNote = {
      patientId: newNote.patientId,
      providerId: newNote.providerId,
      createdAt: newNote.createdAt,
      note,
      providerName: {
        firstName: provider.firstName,
        lastName: provider.lastName,
      },
    }
    return res.status(201).send({ message: "Success", clinicalNote })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
}
