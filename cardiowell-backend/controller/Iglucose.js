const axios = require("axios")
var GlucoseData = require("../models/glucoseData")

/* eslint-disable no-async-promise-executor */
exports.getGlucoseData = async () => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await axios({
        url: "https://api.iglucose.com/readings/",
        method: "post",
        data: {
          api_key: "EA452207-9A77-42A2-8A75-0C454E4CFE2E-1616420479",
          device_ids: ["9999996"],
          reading_type: ["blood_glucose"],
        },
      })
      let glucoseData = await GlucoseData.find()
      let glucose_data
      if (!glucoseData.length) {
        glucose_data = await GlucoseData.create(res.data)
      } else {
        glucose_data = await GlucoseData.replaceOne({}, { readings: res.data.readings })
      }
      if (glucose_data) {
        console.log("data updated in the db------------------**********")
        resolve(res.data)
      }
    } catch (e) {
      console.log("error--*****", e)
      reject(e)
    }
  })
}
/* eslint-enable no-async-promise-executor */
