export const lookupPatientProfileTimes = (providerId, milliseconds) => [
  {
    $lookup: {
      from: "patientprofiletimes",
      let: {
        patientId: { $toString: "$_id" },
        providerId: providerId,
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$$patientId", "$patientId"] },
                { $eq: ["$$providerId", "$providerId"] },
                { $gte: ["$ts", milliseconds] },
              ],
            },
          },
        },
        {
          $group: {
            _id: "$patientId",
            totalTime: { $sum: "$time" },
          },
        },
      ],
      as: "rt",
    },
  },
  {
    $unwind: {
      path: "$rt",
      preserveNullAndEmptyArrays: true,
    },
  },
]
