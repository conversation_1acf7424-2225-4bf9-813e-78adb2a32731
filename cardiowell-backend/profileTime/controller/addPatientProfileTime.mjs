import { body, validationResult, matchedData } from "express-validator"
import { savePatientTimes } from "../service/savePatientTimes.mjs"

export const addPatientProfileTimeValidator = [
  body("providerId").isAlphanumeric().isLength({ min: 1 }),
  body("patientId").isAlphanumeric().isLength({ min: 1 }),
  body("time").isNumeric(),
  body("ts").isNumeric(),
]
export const addPatientProfileTime = async (request, response) => {
  try {
    validationResult(request).throw()
    const { providerId, patientId, time, ts } = matchedData(request)
    const saveTime = await savePatientTimes({ providerId, patientId, time, ts })
    return response.status(201).send({ message: "Success", saveTime })
  } catch (error) {
    return response.status(500).json(error)
  }
}
