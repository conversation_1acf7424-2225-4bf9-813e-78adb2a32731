import { body, validationResult, matchedData } from "express-validator"
import * as service from "../service/testEndpointTelemetryMessage.mjs"

export const testEndpointTelemetryMessageValidator = [
  body("name").notEmpty().escape(),
  body("url")
    .exists()
    .withMessage("is required")
    .isURL({
      require_tld: false,
    })
    .withMessage("Invalid Url"),
  body("keyName").notEmpty().escape(),
  body("keyValue").notEmpty().escape(),
]

export const testEndpointTelemetryMessage = async function (req, res, next) {
  try {
    const result = validationResult(req)

    if (!result.isEmpty()) {
      const message = result
        .formatWith(error => error.msg)
        .array()
        .join("; ")

      res.status(400).json({ code: "validation", message })
    } else {
      const endpoint = matchedData(req)

      const result = await service.testEndpointTelemetryMessage({
        endpoint,
      })

      res.status(201).json(result)
    }
  } catch (err) {
    console.log("Error while createing the customer", err)
    res.status(500).json(err)
  }
}
