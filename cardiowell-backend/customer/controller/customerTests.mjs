import { startCustomerTest } from "../service/startCustomerTest.mjs"
import { stopCustomerTest } from "../service/stopCustomerTest.mjs"
import { param, body, validationResult, matchedData } from "express-validator"

export const customerTestsValidator = [
  param("id").notEmpty().escape(),
  body("start").optional(),
  body("stop").optional(),
]

export const customerTests = async function (req, res, next) {
  try {
    validationResult(req).throw()
    const { id, start, stop } = matchedData(req)

    let customer = null
    if (start) {
      customer = await startCustomerTest({ id })
    } else if (stop) {
      customer = await stopCustomerTest({ id })
    }

    if (!customer) {
      res.status(404).send({
        error: "customer-not-found",
        message: "This id does not exist in any customer in our records",
      })
    } else {
      res.status(200).json(customer)
    }
  } catch (err) {
    console.log(err)
    res.status(400).json(err)
  }
}
