import * as service from "../service/getCustomerTestReports.mjs"
import { param, validationResult, matchedData } from "express-validator"

export const getCustomerTestReportsValidator = [param("id").notEmpty().escape()]

export const getCustomerTestReports = async function (req, res, next) {
  try {
    validationResult(req).throw()
    const { id } = matchedData(req)

    const reports = await service.getCustomerTestReports({ id })

    res.status(200).json(reports)
  } catch (err) {
    console.log(err)
    res.status(500).json(err)
  }
}
