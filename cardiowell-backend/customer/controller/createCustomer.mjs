import { Customer } from "../../models/customer.mjs"
import { body, validationResult, matchedData } from "express-validator"

export const createCustomerValidator = [
  body("name").notEmpty().escape(),
  body("contact").notEmpty().escape(),
  body("contactNumber").notEmpty().escape(),
  body("email").notEmpty().escape(),
  body("address").notEmpty().escape(),
  body("clinics").isArray({ min: 1 }).escape(),
  body("endpoints").isArray(),
  body("endpoints.*.name").notEmpty().escape(),
  body("endpoints.*.url")
    .exists()
    .withMessage("is required")
    .isURL({
      require_tld: false,
    })
    .withMessage("Invalid Url"),
  body("endpoints.*.keyName").optional().escape(),
  body("endpoints.*.keyValue").optional().escape(),
]

export const createCustomer = async function (req, res, next) {
  try {
    const result = validationResult(req)

    if (!result.isEmpty()) {
      const message = result
        .formatWith(error => error.msg)
        .array()
        .join("; ")

      res.status(400).json({ code: "validation", message })
    } else {
      const createCustomerRequest = matchedData(req)

      const newCustomer = await Customer.create(createCustomerRequest)
      res.status(201).json(newCustomer)
    }
  } catch (err) {
    console.log("Error while createing the customer", err)
    res.status(500).json(err)
  }
}
