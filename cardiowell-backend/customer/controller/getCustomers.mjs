import { Customer } from "../../models/customer.mjs"
import { createMongoFilter } from "../../mongoFilter/createMongoFilter.mjs"

export const getCustomers = async function (req, res, next) {
  try {
    const { page = 0, pageSize = 0, sort, filterValue, filterProps } = req.query
    const [sortField, sortType] = sort?.split(":") || []
    const [filterField, filterOperator] = filterProps?.split(":") || []
    const skip = page * pageSize

    const filter =
      filterField !== "clinics"
        ? createMongoFilter(filterField, filterOperator, filterValue)
        : createMongoFilter("clinicNames", filterOperator, filterValue, "array")

    const [result] = await Customer.aggregate(
      [
        {
          $addFields: {
            clinics: {
              $map: {
                input: "$clinics",
                as: "clinicId",
                in: {
                  $toObjectId: "$$clinicId",
                },
              },
            },
          },
        },
        {
          $lookup: {
            from: "clinics",
            localField: "clinics",
            foreignField: "_id",
            as: "clinics",
          },
        },
        {
          $addFields: {
            clinicNames: "$clinics.name",
          },
        },
        {
          $match: filter,
        },
        sortField
          ? {
              $sort: { [sortField]: sortType === "asc" ? 1 : -1 },
            }
          : undefined,
        {
          $facet: {
            customers: [{ $skip: +skip || 0 }, { $limit: +pageSize || 10 }],
            info: [{ $count: "totalRowCount" }],
          },
        },
      ].filter(Boolean),
    )

    res.status(200).json({
      customers: result.customers,
      pageInfo: {
        page,
        pageSize,
        totalRowCount: result?.info[0]?.totalRowCount || 0,
      },
    })
  } catch (err) {
    console.log(err)
    res.status(400).json(err)
  }
}
