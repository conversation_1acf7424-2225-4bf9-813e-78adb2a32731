import * as service from "../service/getCustomerDetails.mjs"

export const getCustomerDetails = async function (req, res, next) {
  try {
    const { id } = req.params
    const customer = await service.getCustomerDetails({ id })

    if (!customer) {
      res.status(404).send({
        error: "customer-not-found",
        message: "This id does not exist in any customer in our records",
      })
    } else {
      res.status(200).json(customer)
    }
  } catch (err) {
    console.log(err)
    res.status(400).json(err)
  }
}
