import { Customer } from "../../models/customer.mjs"
import { param, validationResult, matchedData } from "express-validator"

export const deleteCustomerValidator = [param("id").notEmpty().escape()]

export const deleteCustomer = async function (req, res, next) {
  try {
    validationResult(req).throw()

    const { id } = matchedData(req)

    await Customer.findByIdAndDelete(id)
    res.status(201).send()
  } catch (err) {
    console.log("Error while createing the customer", err)
    res.status(400).json(err)
  }
}
