import { Customer, TestReport, TestMessage } from "../../models/customer.mjs"

export const stopCustomerTest = async ({ id }) => {
  const customer = await Customer.findByIdAndUpdate(id, {
    $unset: {
      testReport: "",
    },
  })

  const testReport = await TestReport.findByIdAndUpdate(
    customer.testReport,
    {
      $set: {
        finishAt: Date.now(),
      },
    },
    { new: true },
  )

  const testMessages = await TestMessage.find({
    testReport: customer.testReport,
  })

  return TestReport.findByIdAndUpdate(
    customer.testReport,
    {
      $set: {
        messages: testMessages,
      },
    },
    { new: true },
  )
}
