import axios from "axios"
import { generateSphygmomanometerTelemetry } from "../../device/service/test/generateSphygmomanometerTelemetry.mjs"

export const testEndpointTelemetryMessage = async ({ endpoint }) => {
  const requestData = generateSphygmomanometerTelemetry()

  const headers = {
    [endpoint.keyName]: endpoint.keyValue,
  }

  const response = await axios.post(endpoint.url, requestData, {
    validateStatus: () => true,
    headers,
  })

  return {
    request: requestData,
    response: {
      status: response.status,
      data: response.data,
    },
  }
}
