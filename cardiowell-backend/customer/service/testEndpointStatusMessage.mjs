import axios from "axios"
import { generateSphygmomanometerStatus } from "../../device/service/test/generateSphygmomanometerStatus.mjs"

export const testEndpointStatusMessage = async ({ endpoint }) => {
  const requestData = generateSphygmomanometerStatus()

  const headers = {
    [endpoint.keyName]: endpoint.keyValue,
  }

  const response = await axios.post(endpoint.url, requestData, {
    validateStatus: () => true,
    headers,
  })

  return {
    request: requestData,
    response: {
      status: response.status,
      data: response.data,
    },
  }
}
