name: Common Pipeline

on:
  push:
    branches:
      - '**'

jobs:
  static-checks:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.9.0"
          cache: "yarn"

      - name: Cache Yarn Modules
        id: yarn-cache
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/yarn.lock') }}

      - name: Install Dependencies
        if: steps.yarn-cache.outputs.cache-hit != 'true'
        run: yarn

      - name: Lint
        run: yarn lint --quiet
