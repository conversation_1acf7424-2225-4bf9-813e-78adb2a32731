import js from '@eslint/js'
import react from 'eslint-plugin-react'
import prettier from 'eslint-plugin-prettier'
import config<PERSON>rettier from 'eslint-config-prettier'
import globals from 'globals'

export default [
  js.configs.recommended,
  {
    files: ['**/*.{js,jsx}'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.jest,
      },
    },
    plugins: {
      react,
      prettier,
    },
    rules: {
      'prettier/prettier': 'error',
      'react/prop-types': 'off',
      'no-console': 'warn',
      'react/jsx-uses-react': 'error', // Prevent 'React' from being marked as unused
      'react/jsx-uses-vars': 'error', // Prevent variables used in JSX from being marked as unused
      'no-unused-vars': 'warn', // Show warnings for truly unused imports
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },
  config<PERSON><PERSON><PERSON>,
]
