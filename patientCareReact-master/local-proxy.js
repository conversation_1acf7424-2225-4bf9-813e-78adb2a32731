const express = require('express')
const { createProxyMiddleware } = require('http-proxy-middleware')

const app = express()

app.use(
  '/routes',
  createProxyMiddleware({
    target: process.env.API,
    changeOrigin: true,
  })
)

app.use(
  '/socket.io',
  createProxyMiddleware({
    target: process.env.API,
    changeOrigin: true,
    ws: true,
  })
)
app.use(
  '/',
  createProxyMiddleware({
    target: process.env.WEB_UI,
    changeOrigin: true,
  })
)

app.listen(process.env.PROXY_PORT, () => {
  console.log(`Proxy server is running on port ${process.env.PROXY_PORT}`)
})
