/**
 * Chart utility functions for data processing and calculations
 */
import moment from 'moment-timezone'

/**
 * Get maximum value from array of objects by specific key
 * @param {Array} data - Array of data objects
 * @param {string} key - Key to get max value for
 * @returns {number} Maximum value
 */
export const getMaxValue = (data, key) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return 0
  }

  try {
    return Math.max(...data.map((item) => (item && item[key]) || 0))
  } catch (error) {
    console.warn('Error calculating max value:', error)
    return 0
  }
}

/**
 * Get minimum value from array of objects by specific key
 * @param {Array} data - Array of data objects
 * @param {string} key - Key to get min value for
 * @returns {number} Minimum value
 */
export const getMinValue = (data, key) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return 0
  }

  try {
    return Math.min(...data.map((item) => (item && item[key]) || 0))
  } catch (error) {
    console.warn('Error calculating min value:', error)
    return 0
  }
}

/**
 * Calculate X-axis interval for chart based on data length and filters
 * @param {number} datesLength - Number of data points
 * @param {number} durationDays - Duration in days
 * @param {boolean} isAllTimeFilter - Is all time filter active
 * @param {boolean} isMobile - Is mobile device
 * @returns {number} Calculated interval
 */
export const getXAxisInterval = (
  datesLength,
  durationDays,
  isAllTimeFilter,
  isMobile
) => {
  if (!datesLength || datesLength === 0) {
    return 0
  }

  const mobileFactor = isMobile ? 2 : 1

  // Special cases for specific duration days
  if ([93, 186, 365, 730].includes(durationDays)) {
    return 0
  }

  if (isAllTimeFilter) {
    if (datesLength <= 10) return 0
    if (datesLength <= 30) return Math.max(2, 4 * mobileFactor)
    if (datesLength <= 100) return Math.floor(datesLength / (isMobile ? 8 : 15))
    if (datesLength <= 365)
      return Math.floor(datesLength / (isMobile ? 10 : 20))
    return Math.floor(datesLength / (isMobile ? 15 : 25))
  }

  const targetPointsVisible = 19
  const interval = Math.max(
    0,
    Math.floor((datesLength - 1) / (targetPointsVisible - 1))
  )
  return Math.max(interval, interval * mobileFactor)
}

/**
 * Calculate chart width based on data points and filters
 * @param {number} dataPoints - Number of data points
 * @param {boolean} isAllTimeFilter - Is all time filter active
 * @param {boolean} fitView - Should fit to view
 * @returns {string} Width percentage or '100%'
 */
export const getChartWidth = (dataPoints, isAllTimeFilter, fitView) => {
  if (isAllTimeFilter || dataPoints <= 19) {
    return '100%'
  }

  const targetPointsVisible = 19
  const widthPercentage = Math.max(
    200,
    (dataPoints / targetPointsVisible) * 100
  )

  return fitView ? '100%' : `${widthPercentage}%`
}

/**
 * Convert timestamp to milliseconds if needed
 * @param {number} timestamp - Timestamp to convert
 * @returns {number} Timestamp in milliseconds
 */
export const convertToMilliseconds = (timestamp) => {
  if (!timestamp) return null

  return typeof timestamp === 'number' && timestamp < 10000000000
    ? timestamp * 1000
    : timestamp
}

/**
 * Format date for chart display with timezone support
 * @param {number} timestamp - Timestamp to format
 * @param {string} timeZone - Timezone string
 * @param {boolean} hasMultipleYears - Whether data spans multiple years
 * @returns {string} Formatted date string
 */
export const formatChartDate = (
  timestamp,
  timeZone,
  hasMultipleYears = false
) => {
  if (!timestamp || !timeZone) {
    return ''
  }

  try {
    const timestampMs = convertToMilliseconds(timestamp)
    const momentDate = moment(timestampMs)

    if (!momentDate.isValid()) {
      console.warn('Invalid date:', timestamp)
      return ''
    }

    const dateFormat = 'MM/DD/YY'
    return (
      momentDate.tz(timeZone).format(dateFormat) +
      '\n' +
      momentDate.tz(timeZone).format('hh:mmA')
    )
  } catch (error) {
    console.warn('Error formatting date:', error)
    return ''
  }
}

/**
 * Filter chart data by date range
 * @param {Array} data - Chart data to filter
 * @param {string|Date} startDate - Start date
 * @param {string|Date} endDate - End date
 * @returns {Array} Filtered data
 */
export const filterDataByDateRange = (data, startDate, endDate) => {
  if (!data || !Array.isArray(data)) {
    return []
  }

  if (!startDate || !endDate) {
    return data
  }

  try {
    return data
      .filter((item) => {
        if (!item || !item.ts) return false

        const timestampMs = convertToMilliseconds(item.ts)
        const pointDate = moment(timestampMs)
        const startMoment = moment(startDate).startOf('day')
        const endMoment = moment(endDate).endOf('day')

        return pointDate.isBetween(startMoment, endMoment, 'day', '[]')
      })
      .sort((a, b) => b.ts - a.ts)
  } catch (error) {
    console.warn('Error filtering data by date range:', error)
    return data
  }
}

/**
 * Get symbol size for chart based on data length and filters
 * @param {boolean} isAllTimeFilter - Is all time filter active
 * @param {number} dataLength - Length of data
 * @returns {number} Symbol size
 */
export const getSymbolSize = (isAllTimeFilter, dataLength) => {
  return isAllTimeFilter && dataLength > 50 ? 6 : 10
}

/**
 * Get label font size for chart based on data length and filters
 * @param {boolean} isAllTimeFilter - Is all time filter active
 * @param {number} dataLength - Length of data
 * @returns {number} Font size
 */
export const getLabelFontSize = (isAllTimeFilter, dataLength) => {
  return isAllTimeFilter && dataLength > 50 ? 10 : 12
}
