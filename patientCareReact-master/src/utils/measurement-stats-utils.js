import { format } from 'date-fns'

const getUniqueDatesSet = (readings, timeZone) => {
  return new Set(
    readings
      .filter((reading) => reading.date instanceof Date)
      .map((reading) => format(reading.date, 'MM/dd/yy', { timeZone }))
  )
}

export function calculateBPMeasurementStats(readings, timeZone) {
  if (!readings || readings.length === 0) {
    return { totalMeasurements: 0, totalDailyMeasurements: 0 }
  }

  const totalMeasurements = readings.length

  const uniqueDates = getUniqueDatesSet(readings, timeZone)
  const totalDailyMeasurements = uniqueDates.size

  return { totalMeasurements, totalDailyMeasurements }
}

export function calculateWeightMeasurementStats(readings, timeZone) {
  if (!readings || readings.length === 0) {
    return { totalMeasurements: 0, totalDailyMeasurements: 0 }
  }

  const totalMeasurements = readings.length

  const uniqueDates = getUniqueDatesSet(readings, timeZone)
  const totalDailyMeasurements = uniqueDates.size

  return { totalMeasurements, totalDailyMeasurements }
}

export function calculatePulseMeasurementStats(readings, timeZone) {
  if (!readings || readings.length === 0) {
    return { totalMeasurements: 0, totalDailyMeasurements: 0 }
  }

  const totalMeasurements = readings.length

  const uniqueDates = getUniqueDatesSet(readings, timeZone)
  const totalDailyMeasurements = uniqueDates.size

  return { totalMeasurements, totalDailyMeasurements }
}

export function calculateGlucoseMeasurementStats(readings, timeZone) {
  if (!readings || readings.length === 0) {
    return { totalMeasurements: 0, totalDailyMeasurements: 0 }
  }

  const totalMeasurements = readings.length

  const uniqueDates = getUniqueDatesSet(readings, timeZone)
  const totalDailyMeasurements = uniqueDates.size

  return { totalMeasurements, totalDailyMeasurements }
}
