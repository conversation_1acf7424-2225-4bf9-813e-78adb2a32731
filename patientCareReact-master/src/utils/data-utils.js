export function formatDate(date) {
  return date.toISOString().split('T')[0]
}

export function formatTime(date) {
  return date.toTimeString().split(' ')[0].substring(0, 5)
}

export function calculateAverages(readings) {
  if (!readings || readings.length === 0) {
    return { systolic: 0, diastolic: 0, pulse: 0 }
  }

  const systolicSum = readings.reduce(
    (sum, reading) => sum + reading.systolic,
    0
  )
  const diastolicSum = readings.reduce(
    (sum, reading) => sum + reading.diastolic,
    0
  )
  const pulseSum = readings.reduce((sum, reading) => sum + reading.pulse, 0)
  const count = readings.length

  return {
    systolic: Math.round(systolicSum / count),
    diastolic: Math.round(diastolicSum / count),
    pulse: Math.round(pulseSum / count),
  }
}

export function calculateTrends(readings) {
  if (!readings || readings.length < 2) {
    return { systolicTrend: 0, diastolicTrend: 0 }
  }

  // Sort readings by timestamp (oldest first)
  const sortedReadings = [...readings].sort((a, b) => a.ts - b.ts)

  // Calculate monthly averages
  const monthlyData = {}

  sortedReadings.forEach((reading) => {
    const date = new Date(reading.ts * 1000)
    const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`

    if (!monthlyData[monthKey]) {
      monthlyData[monthKey] = { systolicSum: 0, diastolicSum: 0, count: 0 }
    }

    monthlyData[monthKey].systolicSum += reading.systolic
    monthlyData[monthKey].diastolicSum += reading.diastolic
    monthlyData[monthKey].count += 1
  })

  // Convert to array of monthly averages
  const monthlyAverages = Object.keys(monthlyData)
    .map((month) => {
      const data = monthlyData[month]
      return {
        month,
        systolic: Math.round(data.systolicSum / data.count),
        diastolic: Math.round(data.diastolicSum / data.count),
      }
    })
    .sort((a, b) => a.month.localeCompare(b.month))

  // Calculate trend (difference between first and last month)
  if (monthlyAverages.length < 2) {
    return { systolicTrend: 0, diastolicTrend: 0 }
  }

  const firstMonth = monthlyAverages[0]
  const lastMonth = monthlyAverages[monthlyAverages.length - 1]

  return {
    systolicTrend: lastMonth.systolic - firstMonth.systolic,
    diastolicTrend: lastMonth.diastolic - firstMonth.diastolic,
  }
}
