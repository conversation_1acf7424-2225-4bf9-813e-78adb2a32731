import autoTable from 'jspdf-autotable'
import { format } from 'date-fns'
import { generateChartImage } from './chart-generator'
import { calculateBPMeasurementStats } from './measurement-stats-utils'
import { calculateOverallForBpm2 } from '../components/PatientData/calculateOverallForBpm'
import { addMeasurementStats } from './addMeasurementStats'

const DATE_FORMAT = 'MM/dd/yy'

export async function addBloodPressureSection(
  doc,
  readings,
  yPosition,
  commonAvgDia,
  commonAvgSys,
  timeZone
) {
  // Check if we need a new page
  if (yPosition > 220) {
    doc.addPage()
    yPosition = 20
  }

  // Add section title
  doc.setFontSize(14)
  doc.text('Blood Pressure Trend', 14, yPosition)
  yPosition += 10

  // Generate and add chart with smaller dimensions
  try {
    const chartImage = await generateChartImage({
      type: 'bloodPressure',
      data: readings,
      width: 500,
      height: 200,
    })

    // Add chart to PDF with smaller size
    doc.addImage(chartImage, 'PNG', 14, yPosition, 160, 64)
    yPosition += 70
  } catch (error) {
    console.error('Error generating blood pressure chart:', error)
    yPosition += 10
  }

  // Check if we need a new page for the table
  if (yPosition > 200) {
    doc.addPage()
    yPosition = 20
  }

  // Add table title
  doc.setFontSize(14)
  doc.text('Blood Pressure Readings', 14, yPosition)
  yPosition += 5

  // Add the table
  autoTable(doc, {
    startY: yPosition,
    head: [['Date', 'Time', 'Systolic', 'Diastolic', 'Pulse']],
    body: readings.map((reading) => [
      format(reading.date, DATE_FORMAT, { timeZone }),
      reading.time,
      reading.systolic.toString(),
      reading.diastolic.toString(),
      reading.pulse.toString(),
    ]),
    margin: { top: 10 },
    styles: { fontSize: 8 },
    headStyles: { fillColor: [66, 66, 66] },
  })

  // Get the final y position after the table is drawn
  let finalY = doc.lastAutoTable.finalY + 10

  // Add measurement statistics
  const bpStats = calculateBPMeasurementStats(readings, timeZone)
  finalY = addMeasurementStats(doc, bpStats, 'Blood Pressure', finalY)

  const bpChange = calculateOverallForBpm2({
    readings,
  })

  if (bpChange.diastolic !== null && bpChange?.systolic !== null) {
    finalY = addBpChangeTable(doc, bpChange, finalY)
  }

  return finalY
}

function addBpChangeTable(doc, bpChange, yPosition) {
  // Check if we need a new page
  if (yPosition > 250) {
    doc.addPage()
    yPosition = 20
  }

  // Add statistics section
  doc.setFontSize(12)
  doc.text(`Blood Pressure Changes`, 14, yPosition)
  yPosition += 10

  // Create changes table
  const changeBpData = [
    ['Systolic', bpChange.systolic.toString()],
    ['Diastolic', bpChange.diastolic.toString()],
  ]

  autoTable(doc, {
    startY: yPosition,
    body: changeBpData,
    margin: { top: 5 },
    columnStyles: {
      0: { fontStyle: 'bold' },
      1: { halign: 'center' },
    },
    tableWidth: 100,
  })

  // Get the final y position after the table is drawn
  const finalY = doc.lastAutoTable.finalY + 15
  return finalY
}
