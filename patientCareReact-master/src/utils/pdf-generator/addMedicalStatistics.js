import autoTable from 'jspdf-autotable'

export function addMedicalStatistics(
  doc,
  data,
  yPosition,
  {
    isBloodPressureVisible,
    isWeightScaleVisible,
    isSpo2Visible,
    isGlucoseVisible,
  }
) {
  // Calculate statistics
  const bpStats = calculateBPStatistics(data.bpmTableArray)
  const spo2Stats = calculateSPO2Statistics(data.pulseTableArray)
  const glucoseStats = calculateGlucoseStatistics(data.glucoseTableArray)

  // Check if we need a new page
  if (yPosition > 200) {
    doc.addPage()
    yPosition = 20
  }

  // Add section title
  doc.setFontSize(14)
  doc.text('Medical Statistics Summary', 14, yPosition)
  yPosition += 15

  // Create statistics table
  const statisticsData = [['Metric', 'Units', 'Value']]

  if (isBloodPressureVisible) {
    statisticsData.push([
      'Average BP',
      'mmHg',
      bpStats.avgSystolic > 0
        ? `${bpStats.avgSystolic}/${bpStats.avgDiastolic}`
        : 'N/A',
    ])
  }

  if (isWeightScaleVisible) {
    statisticsData.push([
      'High BP',
      'mmHg',
      bpStats.highSystolic > 0
        ? `${bpStats.highSystolic}/${bpStats.highDiastolic}`
        : 'N/A',
    ])
  }

  if (isSpo2Visible) {
    statisticsData.push([
      'Low BP',
      'mmHg',
      bpStats.lowSystolic > 0
        ? `${bpStats.lowSystolic}/${bpStats.lowDiastolic}`
        : 'N/A',
    ])
  }

  if (isBloodPressureVisible) {
    statisticsData.push([
      'BP Readings',
      '',
      data.bpmTableArray.length > 0
        ? data.bpmTableArray.length.toString()
        : 'N/A',
    ])
  }

  if (isSpo2Visible) {
    statisticsData.push([
      'Average SPO2',
      '%',
      spo2Stats.avgSPO2 > 0 ? spo2Stats.avgSPO2.toString() : 'N/A',
    ])
  }

  if (isGlucoseVisible) {
    statisticsData.push([
      'Average Glucose',
      'mg/dL',
      glucoseStats.avgGlucose > 0 ? glucoseStats.avgGlucose : 'N/A',
    ])
  }

  if (isWeightScaleVisible) {
    statisticsData.push([
      'Average Weight',
      'lbs',
      data.wsTableArray.length > 0
        ? data.wsTableArray.length.toString()
        : 'N/A',
    ])
  }

  autoTable(doc, {
    startY: yPosition,
    head: [statisticsData[0]],
    body: statisticsData.slice(1),
    margin: { top: 10 },
    styles: { fontSize: 10 },
    headStyles: { fillColor: [66, 66, 66] },
    columnStyles: {
      0: { fontStyle: 'bold' },
      1: { fontStyle: 'bold' },
    },
  })

  // Get the final y position after the table is drawn
  const finalY = doc.lastAutoTable.finalY + 20
  return finalY
}

function calculateGlucoseStatistics(glucoseReadings) {
  if (!glucoseReadings || glucoseReadings.length === 0) {
    return {
      avgGlucose: 0,
      highGlucose: 0,
      lowGlucose: 0,
    }
  }

  const glucoseValues = glucoseReadings.map((reading) => reading.glucose)
  const avgGlucose = Math.round(
    glucoseValues.reduce((sum, val) => sum + val, 0) / glucoseValues.length
  )
  const highGlucose = Math.max(...glucoseValues)
  const lowGlucose = Math.min(...glucoseValues)

  return {
    avgGlucose,
    highGlucose,
    lowGlucose,
  }
}

function calculateSPO2Statistics(pulseReadings) {
  if (!pulseReadings || pulseReadings.length === 0) {
    return {
      avgSPO2: 0,
    }
  }

  const spo2Values = pulseReadings.map((reading) => reading.spo2)
  const avgSPO2 = Math.round(
    spo2Values.reduce((sum, val) => sum + val, 0) / spo2Values.length
  )

  return {
    avgSPO2,
  }
}

export function calculateBPStatistics(bpReadings) {
  if (!bpReadings || bpReadings.length === 0) {
    return {
      avgSystolic: 0,
      avgDiastolic: 0,
      highSystolic: 0,
      highDiastolic: 0,
      lowSystolic: 0,
      lowDiastolic: 0,
    }
  }

  const systolicValues = bpReadings.map((reading) => reading.systolic)
  const diastolicValues = bpReadings.map((reading) => reading.diastolic)

  const avgSystolic = Math.round(
    systolicValues.reduce((sum, val) => sum + val, 0) / systolicValues.length
  )
  const avgDiastolic = Math.round(
    diastolicValues.reduce((sum, val) => sum + val, 0) / diastolicValues.length
  )

  const highSystolic = Math.max(...systolicValues)
  const highDiastolic = Math.max(...diastolicValues)

  const lowSystolic = Math.min(...systolicValues)
  const lowDiastolic = Math.min(...diastolicValues)

  return {
    avgSystolic,
    avgDiastolic,
    highSystolic,
    highDiastolic,
    lowSystolic,
    lowDiastolic,
  }
}
