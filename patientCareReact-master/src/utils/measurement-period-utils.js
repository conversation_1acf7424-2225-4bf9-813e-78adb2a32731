export function calculateMeasurementPeriod(
  bpReadings = [],
  weightReadings = [],
  pulseReadings = []
) {
  // Collect all dates from all measurement types
  const allDates = []

  bpReadings.forEach((reading) => {
    const date = reading.date
    if (date) allDates.push(date)
  })

  bpReadings.map((reading) => {
    const date = reading.date
    if (date) allDates.push(date)
  })

  // Add weight reading dates
  weightReadings.forEach((reading) => {
    const date = reading.date
    if (date) allDates.push(date)
  })

  // Add pulse reading dates
  pulseReadings.forEach((reading) => {
    const date = reading.date
    if (date) allDates.push(date)
  })

  if (allDates.length === 0) {
    return 'No data'
  }

  if (allDates.length === 1) {
    return formatDateAmerican(allDates[0])
  }

  // Find earliest and latest dates
  const earliestDate = new Date(
    Math.min(...allDates.map((date) => date.getTime()))
  )
  const latestDate = new Date(
    Math.max(...allDates.map((date) => date.getTime()))
  )

  // If same date, return single date
  if (earliestDate.getTime() === latestDate.getTime()) {
    return formatDateAmerican(earliestDate)
  }

  return `${formatDateAmerican(earliestDate)} - ${formatDateAmerican(latestDate)}`
}

function parseAmericanDate(dateString) {
  try {
    // Parse MM/DD/YYYY format
    const parts = dateString.split('/')
    if (parts.length !== 3) return null

    const month = Number.parseInt(parts[0], 10) - 1 // Month is 0-indexed
    const day = Number.parseInt(parts[1], 10)
    const year = Number.parseInt(parts[2], 10)

    const date = new Date(year, month, day)

    // Validate the date
    if (isNaN(date.getTime())) return null

    return date
  } catch (error) {
    return null
  }
}

function formatDateAmerican(date) {
  // American format: MM/DD/YYYY
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const year = date.getFullYear()
  return `${month}/${day}/${year}`
}
