import React from 'react'
import { createRoot } from 'react-dom/client'
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'

export async function generateChartImage(config) {
  return new Promise((resolve, reject) => {
    // Create a temporary container
    const container = document.createElement('div')
    container.style.width = `${config.width}px`
    container.style.height = `${config.height}px`
    container.style.position = 'absolute'
    container.style.left = '-9999px'
    container.style.background = 'white'
    document.body.appendChild(container)

    // Create chart component based on type
    let chartComponent

    switch (config.type) {
      case 'bloodPressure':
        chartComponent = createBloodPressureChart(config.data)
        break
      case 'weight':
        chartComponent = createWeightChart(
          config.data,
          config.weightUnit || 'Lbs'
        )
        break
      case 'pulse':
        chartComponent = createPulseChart(config.data)
        break
      case 'glucose':
        chartComponent = createGlucoseChart(config.data)
        break
      default:
        reject(new Error('Unknown chart type'))
        return
    }

    // Render the chart
    const root = createRoot(container)
    root.render(chartComponent)

    // Wait for the chart to render, then convert to image
    setTimeout(async () => {
      try {
        // Use html2canvas to convert the chart to image
        const html2canvas = (await import('html2canvas')).default
        const canvas = await html2canvas(container, {
          backgroundColor: '#ffffff',
          scale: 2,
        })

        const imageData = canvas.toDataURL('image/png')

        // Cleanup
        root.unmount()
        document.body.removeChild(container)

        resolve(imageData)
      } catch (error) {
        // Cleanup on error
        root.unmount()
        document.body.removeChild(container)
        reject(error)
      }
    }, 1000) // Wait 1 second for chart to fully render
  })
}

function createBloodPressureChart(data) {
  // Sort and format data for Recharts
  const chartData = [...data]
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .map((reading) => ({
      date: new Date(reading.date).toLocaleDateString('en-US'),
      systolic: reading.systolic,
      diastolic: reading.diastolic,
      pulse: reading.pulse,
    }))

  // Calculate Y-axis domain for blood pressure
  const systolicValues = chartData.map((d) => d.systolic)
  const diastolicValues = chartData.map((d) => d.diastolic)
  const minValue = Math.min(...systolicValues, ...diastolicValues)
  const maxValue = Math.max(...systolicValues, ...diastolicValues)
  const padding = (maxValue - minValue) * 0.1 // 10% padding
  const yAxisMin = Math.max(0, Math.floor(minValue - padding))
  const yAxisMax = Math.ceil(maxValue + padding)

  return React.createElement(
    ResponsiveContainer,
    { width: '100%', height: '100%' },
    React.createElement(
      LineChart,
      {
        data: chartData,
        margin: { top: 10, right: 20, left: 10, bottom: 40 },
      },
      React.createElement(CartesianGrid, { strokeDasharray: '3 3' }),
      React.createElement(XAxis, {
        dataKey: 'date',
        angle: -45,
        textAnchor: 'end',
        height: 50,
        interval: Math.floor(chartData.length / 6) || 0,
        fontSize: 10,
      }),
      React.createElement(YAxis, {
        fontSize: 10,
        domain: [yAxisMin, yAxisMax],
      }),
      React.createElement(Tooltip),
      React.createElement(Legend),
      React.createElement(Line, {
        type: 'monotone',
        dataKey: 'systolic',
        stroke: '#8b5cf6',
        strokeWidth: 2,
        name: 'Systolic',
      }),
      React.createElement(Line, {
        type: 'monotone',
        dataKey: 'diastolic',
        stroke: '#10b981',
        strokeWidth: 2,
        name: 'Diastolic',
      })
    )
  )
}

function createWeightChart(data, weightUnit) {
  // Sort and format data for Recharts
  const chartData = [...data]
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .map((reading) => ({
      date: new Date(reading.date).toLocaleDateString('en-US'),
      weight: Number.parseFloat(reading.weight),
    }))

  // Calculate Y-axis domain for weight
  const weightValues = chartData.map((d) => d.weight)
  const minWeight = Math.min(...weightValues)
  const maxWeight = Math.max(...weightValues)
  const padding = (maxWeight - minWeight) * 0.1 // 10% padding
  const yAxisMin = Math.max(0, Math.floor(minWeight - padding))
  const yAxisMax = Math.ceil(maxWeight + padding)

  return React.createElement(
    ResponsiveContainer,
    { width: '100%', height: '100%' },
    React.createElement(
      LineChart,
      {
        data: chartData,
        margin: { top: 10, right: 20, left: 10, bottom: 40 },
      },
      React.createElement(CartesianGrid, { strokeDasharray: '3 3' }),
      React.createElement(XAxis, {
        dataKey: 'date',
        angle: -45,
        textAnchor: 'end',
        height: 50,
        interval: Math.floor(chartData.length / 6) || 0,
        fontSize: 10,
      }),
      React.createElement(YAxis, {
        fontSize: 10,
        domain: [yAxisMin, yAxisMax],
      }),
      React.createElement(Tooltip),
      React.createElement(Legend),
      React.createElement(Line, {
        type: 'monotone',
        dataKey: 'weight',
        stroke: '#3b82f6',
        strokeWidth: 2,
        name: `Weight (${weightUnit === 'Lbs' ? 'lbs' : 'kgs'})`,
      })
    )
  )
}

function createPulseChart(data) {
  // Sort and format data for Recharts
  const chartData = [...data]
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .map((reading) => ({
      date: new Date(reading.date).toLocaleDateString('en-US'),
      spo2: reading.spo2,
      pr: reading.pr,
    }))

  // Calculate Y-axis domain for SPO2 and pulse rate
  const spo2Values = chartData.map((d) => d.spo2)
  const prValues = chartData.map((d) => d.pr)
  const minValue = Math.min(...spo2Values, ...prValues)
  const maxValue = Math.max(...spo2Values, ...prValues)
  const padding = (maxValue - minValue) * 0.1 // 10% padding
  const yAxisMin = Math.max(0, Math.floor(minValue - padding))
  const yAxisMax = Math.ceil(maxValue + padding)

  return React.createElement(
    ResponsiveContainer,
    { width: '100%', height: '100%' },
    React.createElement(
      LineChart,
      {
        data: chartData,
        margin: { top: 10, right: 20, left: 10, bottom: 40 },
      },
      React.createElement(CartesianGrid, { strokeDasharray: '3 3' }),
      React.createElement(XAxis, {
        dataKey: 'date',
        angle: -45,
        textAnchor: 'end',
        height: 50,
        interval: Math.floor(chartData.length / 6) || 0,
        fontSize: 10,
      }),
      React.createElement(YAxis, {
        fontSize: 10,
        domain: [yAxisMin, yAxisMax],
      }),
      React.createElement(Tooltip),
      React.createElement(Legend),
      React.createElement(Line, {
        type: 'monotone',
        dataKey: 'spo2',
        stroke: '#ef4444',
        strokeWidth: 2,
        name: 'SPO2 (%)',
      }),
      React.createElement(Line, {
        type: 'monotone',
        dataKey: 'pr',
        stroke: '#f59e0b',
        strokeWidth: 2,
        name: 'Pulse Rate (BPM)',
      })
    )
  )
}

function createGlucoseChart(data) {
  // Sort and format data for Recharts
  const chartData = [...data]
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .map((reading) => ({
      date: new Date(reading.date).toLocaleDateString('en-US'),
      glucose: reading.data,
    }))

  // Calculate Y-axis domain for glucose
  const glucoseValues = chartData.map((d) => d.glucose)
  const minGlucose = Math.min(...glucoseValues)
  const maxGlucose = Math.max(...glucoseValues)
  const padding = (maxGlucose - minGlucose) * 0.1 // 10% padding
  const yAxisMin = Math.max(0, Math.floor(minGlucose - padding))
  const yAxisMax = Math.ceil(maxGlucose + padding)

  return React.createElement(
    ResponsiveContainer,
    { width: '100%', height: '100%' },
    React.createElement(
      LineChart,
      {
        data: chartData,
        margin: { top: 10, right: 20, left: 10, bottom: 40 },
      },
      React.createElement(CartesianGrid, { strokeDasharray: '3 3' }),
      React.createElement(XAxis, {
        dataKey: 'date',
        angle: -45,
        textAnchor: 'end',
        height: 50,
        interval: Math.floor(chartData.length / 6) || 0,
        fontSize: 10,
      }),
      React.createElement(YAxis, {
        fontSize: 10,
        domain: [yAxisMin, yAxisMax],
      }),
      React.createElement(Tooltip),
      React.createElement(Legend),
      React.createElement(Line, {
        type: 'monotone',
        dataKey: 'glucose',
        stroke: '#22c55e',
        strokeWidth: 2,
        name: 'Glucose',
      })
    )
  )
}
