import autoTable from 'jspdf-autotable'

export function addMeasurementStats(doc, stats, title, yPosition) {
  // Check if we need a new page
  if (yPosition > 250) {
    doc.addPage()
    yPosition = 20
  }

  // Add statistics section
  doc.setFontSize(12)
  doc.text(`${title} Statistics`, 14, yPosition)
  yPosition += 10

  // Create statistics table
  const statsData = [
    ['Metric', 'Value'],
    ['Total Measurements', stats.totalMeasurements.toString()],
    ['Total Daily Measurements', stats.totalDailyMeasurements.toString()],
  ]

  autoTable(doc, {
    startY: yPosition,
    head: [statsData[0]],
    body: statsData.slice(1),
    margin: { top: 5 },
    styles: { fontSize: 9 },
    headStyles: { fillColor: [66, 66, 66] },
    columnStyles: {
      0: { fontStyle: 'bold' },
      1: { halign: 'center' },
    },
    tableWidth: 100,
  })

  // Get the final y position after the table is drawn
  const finalY = doc.lastAutoTable.finalY + 15
  return finalY
}
