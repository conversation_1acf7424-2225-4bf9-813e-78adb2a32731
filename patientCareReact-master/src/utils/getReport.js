import moment from 'moment-timezone'
import { CARDIOWELL } from '../common/manufacters'
import { getClinicalNotes } from '../components/PatientData/ClinicalNote/getClinicalNotes'
import { parseBpMeasures } from '../components/PatientData/BloodPressure/bloodPressure'
import { generatePDF } from './pdf-generator'
import { parseWeightScaleMeasures } from '../components/PatientData/WeightScale/weightScale'
import { parsePulseMeasures } from '../components/PatientData/Pulse/pulseOximeter'
import { parseGlucoseMeasures } from '../components/PatientData/Glucose/glucose'
import { sendTimerData } from '../components/PatientData/sendTimerData'

const PERIOD = 1 // months

const getReportInterval = (timeZone) => {
  const startDate = moment
    .tz(timeZone)
    .subtract(PERIOD, 'month')
    .startOf('day')
    .toDate()
  const endDate = moment.tz(timeZone).subtract(1, 'day').endOf('day').toDate()

  return { startDate, endDate }
}

const getWeightUnit = () => {
  const weightUnit = localStorage.getItem('weightUnit')
  return weightUnit || 'Lbs'
}

const getFirstReading = ({ bpFirstReading }) => {
  return bpFirstReading?.ts && new Date(bpFirstReading?.ts)
}

export const getReport = async (patientId, providerId) => {
  await sendTimerData(patientId, false)

  const { data: patientData } = await fetch('/routes/users/getPatientData', {
    method: 'POST',
    body: JSON.stringify({ id: patientId, providerId }),
    headers: { 'Content-Type': 'application/json' },
  })
    .then((response) => response.json())
    .catch((error) => {
      console.error(error)
    })

  const bpDevice = patientData.selectedBpDevice || CARDIOWELL
  const weightDevice = patientData.selectedWeightDevice || CARDIOWELL
  const threshold = patientData.threshold || {}

  const timeZone =
    patientData?.timeZone && patientData.timeZone !== 'local'
      ? patientData.timeZone
      : moment.tz.guess()

  const { startDate, endDate } = getReportInterval(timeZone)

  const {
    arrayBP,
    bpmTableArray,
    firstReading: bpFirstReading,
    avgDia: commonAvgDia,
    avgSys: commonAvgSys,
  } = parseBpMeasures({
    bpm: patientData?.bpm,
    btMessagesBpm: patientData?.btMessagesBpm,
    ttBpm: patientData?.ttBpm,
    adBpm: patientData?.adBpm,
    withingsBpm: patientData?.withingsBpm,
    bpDevice,
    timeframe: startDate,
    endDate,
    timeZone,
  })

  const weightUnit = getWeightUnit()

  const { arrayWS, wsTableArray, lowWeight, highWeight } =
    parseWeightScaleMeasures({
      ws: patientData?.ws,
      btMessagesWs: patientData?.btMessagesWs,
      ttWs: patientData?.ttWs,
      weightDevice,
      weightUnit,
      timeframe: startDate,
      timeZone,
    })

  const clinicalNotesResponse = await getClinicalNotes({
    patientId: patientId,
    from: new Date(startDate).toISOString(),
  })

  const { pulseChartArray, pulseTableArray, averageSpo2 } = parsePulseMeasures({
    pulse: patientData?.pulse,
    timeframe: startDate,
    timeZone,
  })

  const { glucoseChartArray, glucoseTableArray, averageGlucose } =
    parseGlucoseMeasures({
      glucose: patientData?.glucose,
      timeframe: startDate,
    })

  const clinicalNotes =
    clinicalNotesResponse?.message === 'Success'
      ? clinicalNotesResponse.clinicalNotes
      : []

  generatePDF(
    {
      patientInfo: patientData,
      clinicalNotes,
      bpmTableArray,
      wsTableArray,
      pulseTableArray,
      glucoseTableArray,
      arrayBP,
      arrayWS,
      commonAvgDia,
      commonAvgSys,
      firstReading: getFirstReading({ bpFirstReading }), // TODO: add other first readings
    },
    {
      isBloodPressureVisible: bpmTableArray.length > 0,
      isWeightScaleVisible: wsTableArray.length > 0,
      isSpo2Visible: pulseTableArray.length > 0,
      isGlucoseVisible: glucoseTableArray.length > 0,
      startDate,
      endDate,
    }
  )
}
