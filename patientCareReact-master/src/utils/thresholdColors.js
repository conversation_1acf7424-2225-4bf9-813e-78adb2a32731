const colors = {
  normal: '#4CAF50',
  elevated: '#FFC107',
  stage1: '#FF9800',
  stage2: '#F44336',
  crisis: '#B71C1C',
  low: '#2196F3',
  high: '#F44336',
  critical: '#B71C1C',
  default: '#9E9E9E',
}

export const getBloodPressureColorForLevel = (
  level,
  systolic,
  diastolic,
  thresholds
) => {
  if (!thresholds || !diastolic || !systolic) return '#9E9E9E'

  switch (level) {
    case 'crisis':
      return colors.crisis
    case 'stageTwo':
      return colors.stage2
    case 'stageOne':
      return colors.stage1
    case 'elevated':
      return colors.elevated
    case 'normal':
      return colors.normal
    default:
      return '#9E9E9E'
  }
}

export const getPulseColor = (value, threshold) => {
  if (!threshold) return colors.default

  const pulseThreshold = threshold?.pulse || threshold
  if (!pulseThreshold) return colors.default

  const hasEnabledThresholds =
    pulseThreshold.upperHigh?.enabled ||
    pulseThreshold.upperMedium?.enabled ||
    pulseThreshold.lowerMedium?.enabled ||
    pulseThreshold.lowerHigh?.enabled

  if (!hasEnabledThresholds) return colors.default

  if (
    pulseThreshold.upperHigh?.enabled &&
    value >= pulseThreshold.upperHigh.value
  )
    return colors.crisis
  if (
    pulseThreshold.upperMedium?.enabled &&
    value >= pulseThreshold.upperMedium.value
  )
    return colors.stage2
  if (
    pulseThreshold.lowerMedium?.enabled &&
    value <= pulseThreshold.lowerMedium.value
  )
    return colors.stage1
  if (
    pulseThreshold.lowerHigh?.enabled &&
    value <= pulseThreshold.lowerHigh.value
  )
    return colors.crisis
  return colors.normal
}

export const getBloodGlucoseColor = (value, threshold) => {
  if (!threshold) return colors.default

  const glucoseThreshold = threshold?.bloodGlucose?.glucose || threshold
  if (!glucoseThreshold) return colors.default

  const hasEnabledThresholds =
    glucoseThreshold.upperHigh?.enabled ||
    glucoseThreshold.upperMedium?.enabled ||
    glucoseThreshold.lowerMedium?.enabled ||
    glucoseThreshold.lowerHigh?.enabled

  if (!hasEnabledThresholds) return colors.default

  if (
    glucoseThreshold.upperHigh?.enabled &&
    value >= glucoseThreshold.upperHigh.value
  )
    return colors.crisis
  if (
    glucoseThreshold.upperMedium?.enabled &&
    value >= glucoseThreshold.upperMedium.value
  )
    return colors.stage2
  if (
    glucoseThreshold.lowerMedium?.enabled &&
    value <= glucoseThreshold.lowerMedium.value
  )
    return colors.stage1
  if (
    glucoseThreshold.lowerHigh?.enabled &&
    value <= glucoseThreshold.lowerHigh.value
  )
    return colors.crisis
  return colors.normal
}
