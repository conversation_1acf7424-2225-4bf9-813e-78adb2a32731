export function formatMonitoringTime(seconds) {
  if (!seconds || seconds <= 0) {
    return '0 minutes and 0 seconds'
  }

  // Round to whole number
  const totalSeconds = Math.round(seconds)

  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const remainingSeconds = totalSeconds % 60

  if (hours > 0) {
    return `${hours} hours, ${minutes} minutes and ${remainingSeconds} seconds`
  } else {
    return `${minutes} minutes and ${remainingSeconds} seconds`
  }
}
