import { standardizeBtWs } from '../standardizeBtWs'
import { btWsMockData } from './btWsMockData'

describe('standardizeBtWs', () => {
  beforeEach(() => {
    jest.useFakeTimers('modern')
    jest.setSystemTime(new Date('2023-12-25'))
  })

  it('standardizes Bodytrace measurement', () => {
    const { ts, weight } = standardizeBtWs(btWsMockData[0])
    expect(ts).toEqual(1718908739477)
    expect(weight).toEqual(77800)
  })

  it('handles missing measurement', () => {
    const { ts, weight } = standardizeBtWs({})
    expect(ts).toEqual(new Date('2023-12-25').valueOf())
    expect(weight).toEqual(undefined)
  })
})
