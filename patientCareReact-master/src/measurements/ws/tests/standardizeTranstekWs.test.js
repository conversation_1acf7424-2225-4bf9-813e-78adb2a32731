import { standardizeTranstekWs } from '../standardizeTranstekWs'
import { ttWsMockData } from './ttWsMockData'

describe('standardizeTranstekWs', () => {
  it('standardizes Transtek WS mesasurement', () => {
    const { ts, weight } = standardizeTranstekWs(ttWsMockData[0])
    expect(ts).toEqual(1546272360000)
    expect(weight).toEqual(67000)
  })

  it('handles missing measurement', () => {
    const { ts, weight } = standardizeTranstekWs({})
    expect(ts).toEqual(0)
    expect(weight).toEqual(undefined)
  })
})
