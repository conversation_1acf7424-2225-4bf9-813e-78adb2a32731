import { standardizeBtMessageWs } from '../standardizeBtMessageWs'
import { btMessageWsMockData } from './btMessageWsMockData'

describe('standardizeBtMessageWs', () => {
  beforeEach(() => {
    jest.useFakeTimers('modern')
    jest.setSystemTime(new Date('2023-12-25'))
  })

  it('standardizes Bodytrace Message WS measurement', () => {
    const { ts, weight } = standardizeBtMessageWs(btMessageWsMockData[0])
    expect(ts).toEqual(1734021977209)
    expect(weight).toEqual(76200)
  })

  it('handles missing measurement', () => {
    const { ts, weight } = standardizeBtMessageWs({})
    expect(ts).toEqual(new Date('2023-12-25').valueOf())
    expect(weight).toEqual(undefined)
  })
})
