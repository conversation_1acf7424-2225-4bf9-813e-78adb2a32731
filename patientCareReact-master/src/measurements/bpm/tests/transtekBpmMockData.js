export const transtekBpmMockData = [
  {
    _id: '66985f85c2040059c0c5acd3',
    deviceId: 'forward-telemetry-device-id',
    createdAt: 1702672536,
    dataType: 'bpm_gen2_measure',
    imei: '867420043349754',
    sn: '112233445566',
    iccid: '8944501207218552103',
    systolic: 120,
    diastolic: 85,
    pulse: 65,
    ihb: false,
    hand: false,
    tri: true,
    bat: 10,
    signalStrength: 25,
    ts: 1721261958,
    upload_time: 1546272400,
    tz: 'UTC+8',
    isTest: true,
    modelNumber: 'forward-telemetry-model-number',
    receivedByMdms: 1721261957842,
    __v: 0,
  },
  {
    _id: '668c49573544b25ad4eb35e9',
    deviceId: 'forward-telemetry-device-id',
    createdAt: 1702672536,
    dataType: 'bpm_gen2_measure',
    imei: '867420043349754',
    sn: '112233445566',
    iccid: '8944501207218552103',
    systolic: 120,
    diastolic: 85,
    pulse: 65,
    ihb: false,
    hand: false,
    tri: true,
    bat: 10,
    signalStrength: 25,
    ts: 1720469847,
    upload_time: 1546272400,
    tz: 'UTC+8',
    isTest: true,
    modelNumber: 'forward-telemetry-model-number',
    receivedByMdms: 1720469847048,
    __v: 0,
  },
  {
    _id: '668c494f3544b25ad4eb35df',
    deviceId: 'forward-telemetry-device-id',
    createdAt: 1702672536,
    dataType: 'bpm_gen2_measure',
    imei: '867420043349754',
    sn: '112233445566',
    iccid: '8944501207218552103',
    systolic: 130,
    diastolic: 81,
    pulse: 65,
    ihb: false,
    hand: false,
    tri: true,
    bat: 10,
    signalStrength: 25,
    ts: 1720469839,
    upload_time: 1546272400,
    tz: 'UTC+8',
    isTest: true,
    modelNumber: 'forward-telemetry-model-number',
    receivedByMdms: 1720469839390,
    __v: 0,
  },
]
