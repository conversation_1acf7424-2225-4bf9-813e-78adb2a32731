export const adBpmMockData = [
  {
    payload: {
      model: 'UA-1020CEL',
      serial: '7240800065',
      iccId: '8988228066605471547',
      deviceEndUserId: null,
      readingType: 'bloodPressure',
      timestamp: '2024-11-09T07:34:27-08:00',
      uploadTimestamp: '2024-11-09T15:35:13+00:00',
      battery: 100,
      rsrp: 92,
      rsrq: 8,
      imei: '864178064721466',
      measurementError: false,
      cuffFitError: false,
      bodyMovementError: false,
      correctMeasurementWithIHB: false,
      triCheck: 'none',
      measurements: [
        {
          measurementType: 'pulse',
          value: 54,
          unit: 'bpm',
        },
        {
          measurementType: 'systolic',
          value: 133,
          unit: 'mmHg',
        },
        {
          measurementType: 'diastolic',
          value: 80,
          unit: 'mmHg',
        },
      ],
      roomTemperature: null,
    },
  },
  {
    payload: {
      model: 'UA-1020CEL',
      serial: '7240800065',
      iccId: '8988228066605471547',
      deviceEndUserId: null,
      readingType: 'bloodPressure',
      timestamp: '2024-11-01T07:34:27-08:00',
      uploadTimestamp: '2024-11-01T15:35:13+00:00',
      battery: 100,
      rsrp: 92,
      rsrq: 8,
      imei: '864178064721466',
      measurementError: false,
      cuffFitError: false,
      bodyMovementError: false,
      correctMeasurementWithIHB: false,
      triCheck: 'none',
      measurements: [
        {
          measurementType: 'pulse',
          value: 60,
          unit: 'bpm',
        },
        {
          measurementType: 'systolic',
          value: 150,
          unit: 'mmHg',
        },
        {
          measurementType: 'diastolic',
          value: 90,
          unit: 'mmHg',
        },
      ],
      roomTemperature: null,
    },
  },
  {
    payload: {
      model: 'UA-1020CEL',
      serial: '7240800065',
      iccId: '8988228066605471547',
      deviceEndUserId: null,
      readingType: 'bloodPressure',
      timestamp: '2024-10-09T07:34:27-08:00',
      uploadTimestamp: '2024-10-09T15:35:13+00:00',
      battery: 100,
      rsrp: 92,
      rsrq: 8,
      imei: '864178064721466',
      measurementError: false,
      cuffFitError: false,
      bodyMovementError: false,
      correctMeasurementWithIHB: false,
      triCheck: 'none',
      measurements: [
        {
          measurementType: 'pulse',
          value: 45,
          unit: 'bpm',
        },
        {
          measurementType: 'systolic',
          value: 120,
          unit: 'mmHg',
        },
        {
          measurementType: 'diastolic',
          value: 75,
          unit: 'mmHg',
        },
      ],
      roomTemperature: null,
    },
  },
]

export const standardizedAdBpm = {
  ts: 1731166467000,
  pulse: 54,
  systolic: 133,
  diastolic: 80,
}
