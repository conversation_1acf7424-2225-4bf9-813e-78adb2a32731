export const withingsBpmMockData = [
  {
    _id: '667e053b2ff345002f8d649e',
    updateTime: 1719534896,
    timezone: 'America/Los_Angeles',
    deviceId: 'f9a3fec6c2a77a0fd603b50842a738ee1b28e768',
    grpId: 5647192851,
    attrib: 0,
    date: 1719534817,
    created: 1719534896,
    modified: 1719534896,
    category: 1,
    modelId: 46,
    model: 'BPM Connect Pro',
    comment: null,
    dia: {
      value: 69,
      unit: 0,
      algo: 0,
      fm: 3,
    },
    sys: {
      value: 114,
      unit: 0,
      algo: 0,
      fm: 3,
    },
    pulse: {
      value: 77,
      unit: 0,
      algo: 0,
      fm: 3,
    },
    __v: 0,
  },
  {
    _id: '667e05382ff345002f8d6497',
    updateTime: 1719534896,
    timezone: 'America/Los_Angeles',
    deviceId: 'f9a3fec6c2a77a0fd603b50842a738ee1b28e768',
    grpId: 5647192850,
    attrib: 0,
    date: 1719534759,
    created: 1719534893,
    modified: 1719534893,
    category: 1,
    modelId: 46,
    model: 'BPM Connect Pro',
    comment: null,
    dia: {
      value: 70,
      unit: 0,
      algo: 0,
      fm: 3,
    },
    sys: {
      value: 114,
      unit: 0,
      algo: 0,
      fm: 3,
    },
    pulse: {
      value: 76,
      unit: 0,
      algo: 0,
      fm: 3,
    },
    __v: 0,
  },
  {
    _id: '667e05352ff345002f8d6490',
    updateTime: 1719534896,
    timezone: 'America/Los_Angeles',
    deviceId: 'f9a3fec6c2a77a0fd603b50842a738ee1b28e768',
    grpId: 5647192848,
    attrib: 0,
    date: 1719534695,
    created: 1719534890,
    modified: 1719534890,
    category: 1,
    modelId: 46,
    model: 'BPM Connect Pro',
    comment: null,
    dia: {
      value: 73,
      unit: 0,
      algo: 0,
      fm: 3,
    },
    sys: {
      value: 120,
      unit: 0,
      algo: 0,
      fm: 3,
    },
    pulse: {
      value: 82,
      unit: 0,
      algo: 0,
      fm: 3,
    },
    __v: 0,
  },
]
