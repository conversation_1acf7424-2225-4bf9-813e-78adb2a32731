import { standardizeAdBpm } from '../standardizeAdBpm'
import { adBpmMockData } from './adMockData'

test('Standadizes A&D BPM Data', () => {
  const bpm = standardizeAdBpm(adBpmMockData[0])
  expect(bpm).toEqual({
    ts: 1731166467000,
    pulse: 54,
    systolic: 133,
    diastolic: 80,
  })
})

test('Gracefully Handles Missing timestamp', () => {
  const bpm = standardizeAdBpm({
    payload: {
      model: 'UA-1020CEL',
      serial: '7240800065',
      iccId: '8988228066605471547',
      deviceEndUserId: null,
      readingType: 'bloodPressure',
      uploadTimestamp: '2024-11-09T15:35:13+00:00',
      battery: 100,
      rsrp: 92,
      rsrq: 8,
      imei: '864178064721466',
      measurementError: false,
      cuffFitError: false,
      bodyMovementError: false,
      correctMeasurementWithIHB: false,
      triCheck: 'none',
      measurements: [
        {
          measurementType: 'pulse',
          value: 54,
          unit: 'bpm',
        },
        {
          measurementType: 'systolic',
          value: 133,
          unit: 'mmHg',
        },
        {
          measurementType: 'diastolic',
          value: 80,
          unit: 'mmHg',
        },
      ],
      roomTemperature: null,
    },
  })
  expect(bpm).toEqual({
    ts: NaN,
    pulse: 54,
    systolic: 133,
    diastolic: 80,
  })
})

test('Handles Missing Measurement', () => {
  const bpm = standardizeAdBpm({
    payload: {
      model: 'UA-1020CEL',
      serial: '7240800065',
      iccId: '8988228066605471547',
      deviceEndUserId: null,
      readingType: 'bloodPressure',
      timestamp: '2024-11-09T07:34:27-08:00',
      uploadTimestamp: '2024-11-09T15:35:13+00:00',
      battery: 100,
      rsrp: 92,
      rsrq: 8,
      imei: '864178064721466',
      measurementError: false,
      cuffFitError: false,
      bodyMovementError: false,
      correctMeasurementWithIHB: false,
      triCheck: 'none',
      measurements: [
        {
          measurementType: 'systolic',
          value: 133,
          unit: 'mmHg',
        },
        {
          measurementType: 'diastolic',
          value: 80,
          unit: 'mmHg',
        },
      ],
      roomTemperature: null,
    },
  })
  expect(bpm).toEqual({
    ts: 1731166467000,
    pulse: undefined,
    systolic: 133,
    diastolic: 80,
  })
})
