/**
 * Custom hook for synchronizing scroll between two elements
 */
import { useEffect, useCallback } from 'react'

/**
 * Hook to synchronize scrolling between two elements
 * @param {Object} sourceRef - Source scroll element ref
 * @param {Object} targetRef - Target scroll element ref
 * @param {Object} options - Configuration options
 * @param {boolean} options.enabled - Whether sync is enabled
 * @param {number} options.debounceMs - Debounce delay in milliseconds
 */
export const useScrollSync = (sourceRef, targetRef, options = {}) => {
  const { enabled = true, debounceMs = 0 } = options

  const handleScroll = useCallback(() => {
    if (!enabled || !sourceRef?.current || !targetRef?.current) {
      return
    }

    // Prevent recursive updates
    let isSyncing = false

    return (event) => {
      if (isSyncing) return

      isSyncing = true

      try {
        // Sync scroll position
        targetRef.current.scrollLeft = sourceRef.current.scrollLeft
      } catch (error) {
        console.warn('useScrollSync: Error syncing scroll', error)
      } finally {
        // Reset syncing flag after a microtask
        if (debounceMs > 0) {
          setTimeout(() => {
            isSyncing = false
          }, debounceMs)
        } else {
          // Use microtask for immediate reset
          Promise.resolve().then(() => {
            isSyncing = false
          })
        }
      }
    }
  }, [enabled, sourceRef, targetRef, debounceMs])

  useEffect(() => {
    if (!enabled || !sourceRef?.current || !targetRef?.current) {
      return
    }

    const sourceElement = sourceRef.current
    const scrollHandler = handleScroll()

    if (!sourceElement || !scrollHandler) {
      return
    }

    // Add event listener with passive option for better performance
    sourceElement.addEventListener('scroll', scrollHandler, { passive: true })

    // Cleanup function
    return () => {
      if (sourceElement && scrollHandler) {
        sourceElement.removeEventListener('scroll', scrollHandler)
      }
    }
  }, [handleScroll, enabled, sourceRef, targetRef])
}

/**
 * Hook for bidirectional scroll synchronization
 * @param {Object} ref1 - First element ref
 * @param {Object} ref2 - Second element ref
 * @param {Object} options - Configuration options
 */
export const useBidirectionalScrollSync = (ref1, ref2, options = {}) => {
  useScrollSync(ref1, ref2, options)
  useScrollSync(ref2, ref1, options)
}
