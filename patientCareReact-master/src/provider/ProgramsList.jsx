import { useEffect, useState } from 'react'
import {
  List,
  ListItem,
  ListItemText,
  Paper,
  ListItemAvatar,
  Avatar,
} from '@material-ui/core'
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety'
import { ListItemButton } from '@mui/material'
import { getPrograms } from '../thresholds/program/getPrograms'
import { EditProgramModal } from '../thresholds/program/ProgramThresholdsModal'

export const ProgramsList = ({ clinicId }) => {
  const [programs, setPrograms] = useState([])
  const [selectedProgram, setSelectedProgram] = useState('')

  useEffect(() => {
    getPrograms(clinicId).then((data) => {
      if (data) {
        setPrograms(data)
      }
    })
  }, [clinicId])

  return (
    <>
      <Paper>
        <List component="nav" aria-label="progams list">
          {programs.map((program) => (
            <ListItem key={program._id}>
              <ListItemAvatar>
                <div className="MuiAvatar-root MuiAvatar-circular MuiAvatar-colorDefault css-17o22dy-MuiAvatar-root">
                  <svg
                    className="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiAvatar-fallback css-10mi8st-MuiSvgIcon-root-MuiAvatar-fallback"
                    focusable="false"
                    aria-hidden="true"
                    viewBox="0 0 24 24"
                    data-testid="PersonIcon"
                  >
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"></path>
                  </svg>
                </div>
              </ListItemAvatar>
              <ListItemButton onClick={() => setSelectedProgram(program._id)}>
                <ListItemText primary={program.name} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Paper>
      <EditProgramModal
        open={!!selectedProgram}
        programId={selectedProgram}
        onClose={() => setSelectedProgram('')}
      />
    </>
  )
}
