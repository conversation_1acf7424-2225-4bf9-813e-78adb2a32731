const dummyPatient = {
  email: 'ken<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com',
  phoneNumber: '**********',
  cellNumber: '**********',
  city: '',
  state: '',
  zip: '',
  address: '',
  clinic: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  bpIMEI: '866771020030614',
  ttBpIMEI: '998414532864144',
  weightIMEI: '866771020197538',
  ttWeightIMEI: '864475041535658',
  pulseIMEI: '351358815533523',
  glucoseIMEI: '865648069724441',
  selectedBpDevice: 'Withings',
  selectedWeightDevice: 'TT_Tele_RPM',
  deviceNotificationsEnabled: true,
  programId: '6642a2c3d28c1641684b3fb8',
  adBpIMEI: 'asdf',
  bpm: [
    {
      _id: 'ZWjyAa25rv',
      diastolic: 11200,
      imei: '866771020030614',
      _updated_at: '2016-06-03T05:07:46.329Z',
      pulse: 76,
      systolic: 17200,
      ts: 1464930457229,
      batteryVoltage: 6081,
      _created_at: '2016-06-03T05:07:46.329Z',
      unit: 1,
      signalStrength: 48,
    },
  ],
  btMessagesBpm: [
    {
      _id: '6672e4d2a1668f0032a15a55',
      message: {
        signalStrength: 58,
        imei: '866771020030614',
        ts: 1717088395680,
        batteryVoltage: 5300,
        values: {
          diastolic: 10267,
          pulse: 87,
          systolic: 14266,
          unit: 1,
          irregula: 0,
        },
        isTest: true,
      },
      createdAt: '2024-06-19T14:01:54.927Z',
      updatedAt: '2024-06-19T14:01:55.059Z',
      __v: 0,
      forwardedAt: 1718805715059,
    },
  ],
  ttBpm: [
    {
      _id: '66044e6a3c0ecc003dd9473e',
      deviceId: 'forward-telemetry-device-id',
      createdAt: 1702672536,
      dataType: 'bpm_gen2_measure',
      imei: '998414532864144',
      sn: '112233445566',
      iccid: '8944501207218552103',
      systolic: 131,
      diastolic: 78,
      pulse: 65,
      ihb: false,
      hand: false,
      tri: true,
      bat: 10,
      signalStrength: 25,
      ts: 1647349680,
      upload_time: 1546272400,
      tz: 'UTC+8',
      isTest: true,
      modelNumber: 'forward-telemetry-model-number',
    },
  ],
  ws: [
    {
      imei: '866771020197538',
      ts: 1718908728103,
      batteryVoltage: 5669,
      signalStrength: 37,
      unit: 1,
      weight: 77800,
      _created_at: '2024-06-20T18:38:59.477+00:00',
      _updated_at: '2024-06-20T18:38:59.477+00:00',
    },
  ],
  btMessagesWs: [
    {
      message: {
        imei: '866771020197538',
        ts: 1733894179238,
        batteryVoltage: 5475,
        signalStrength: 46,
        values: {
          unit: 1,
          tare: 0,
          weight: 77600,
        },
        rssi: 91,
        deviceId: 86677102019753,
      },
      createdAt: '2024-12-11T05:16:23.565+00:00',
      updatedAt: '2024-12-11T05:16:23.565+00:00',
    },
  ],
  adBpm: [
    {
      payload: {
        model: 'UA-1020CEL',
        serial: '7240800065',
        iccId: '8988228066605471547',
        deviceEndUserId: null,
        readingType: 'bloodPressure',
        timestamp: '2024-11-09T07:34:27-08:00',
        uploadTimestamp: '2024-11-09T15:35:13+00:00',
        battery: 100,
        rsrp: 92,
        rsrq: 8,
        imei: '864178064721466',
        measurementError: false,
        cuffFitError: false,
        bodyMovementError: false,
        correctMeasurementWithIHB: false,
        triCheck: 'none',
        measurements: [
          {
            measurementType: 'pulse',
            value: 54,
            unit: 'bpm',
          },
          {
            measurementType: 'systolic',
            value: 133,
            unit: 'mmHg',
          },
          {
            measurementType: 'diastolic',
            value: 80,
            unit: 'mmHg',
          },
        ],
        roomTemperature: null,
      },
    },
  ],
  ttWs: [
    {
      _id: '657cdb63327d232768d402c0',
      deviceId: 'forward-telemetry-device-id',
      createdAt: 1702672536,
      uid: '123456789012',
      wt: 65000,
      ts: 1546272360,
      wet: 5,
      lts: 10,
      imei: '864475041535658',
      iccid: '89860446091891349344',
      sig: 26,
      bat: 100,
      tz: 'UTC+8',
      upload_time: 1546272400,
      isTest: true,
      modelNumber: 'forward-telemetry-model-number',
    },
  ],
  pulse: [
    {
      _id: '67210027cec312002d87c470',
      imei: '351358815533523',
      iccid: '8944500711213145103F',
      time: '24/10/29,15:32:42-28',
      rsrp: 45,
      err: 0,
      spo2: 100,
      pr: 60,
      pi: 7,
      battery: 100,
      ver: 'V1.05.03.FF',
      receivedByMdms: 1730215975693,
      __v: 0,
    },
  ],
  glucose: [
    {
      _id: '6586c33c8692b80040c2678f',
      deviceId: '381A10000F3',
      createdAt: 1703330614,
      dataType: 'bgm_gen1_measure',
      imei: '865648069724441',
      iccid: '8944501311216311569f',
      sn: '381A10000F3',
      data: 184,
      unit: 2,
      sample: 1,
      target: 1,
      meal: 0,
      sigLevel: 4,
      ts: 1703200216,
      uptime: 1703330610,
      isTest: false,
      modelNumber: 'TMB-2282-G',
    },
  ],
  withingsBpDevices: {
    deviceIds: 'f9a3fec6c2a77a0fd603b50842a738ee1b28e768',
  },
  withingsBpm: [
    {
      _id: '667e053b2ff345002f8d649e',
      updateTime: 1719534896,
      timezone: 'America/Los_Angeles',
      deviceId: 'f9a3fec6c2a77a0fd603b50842a738ee1b28e768',
      grpId: 5647192851,
      attrib: 0,
      date: 1719534817,
      created: 1719534896,
      modified: 1719534896,
      category: 1,
      modelId: 46,
      model: 'BPM Connect Pro',
      comment: null,
      dia: {
        value: 69,
        unit: 0,
        algo: 0,
        fm: 3,
      },
      sys: {
        value: 114,
        unit: 0,
        algo: 0,
        fm: 3,
      },
      pulse: {
        value: 77,
        unit: 0,
        algo: 0,
        fm: 3,
      },
      __v: 0,
    },
  ],
  threshold: {
    _id: '66452812cdd992544442d73a',
    bloodPressure: {
      diastolic: {
        normal: {
          value: 30,
          enabled: true,
        },
        elevated: {
          value: 100,
          enabled: true,
        },
        stageOne: {
          value: 0,
          enabled: false,
        },
        stageTwo: {
          value: 0,
          enabled: false,
        },
        crisis: {
          value: 0,
          enabled: false,
        },
      },
      systolic: {
        normal: {
          value: 120,
          enabled: true,
        },
        elevated: {
          value: 100,
          enabled: true,
        },
        stageOne: {
          value: 0,
          enabled: false,
        },
        stageTwo: {
          value: 0,
          enabled: false,
        },
        crisis: {
          value: 0,
          enabled: false,
        },
      },
      pulse: {
        upperHigh: {
          value: 101,
          enabled: true,
        },
        upperMedium: {
          value: 80,
          enabled: true,
        },
        lowerHigh: {
          value: 50,
          enabled: false,
        },
        lowerMedium: {
          value: 70,
          enabled: false,
        },
      },
    },
    weight: {
      weight: {
        upperHigh: {
          value: 0,
          enabled: false,
        },
        upperMedium: {
          value: 0,
          enabled: false,
        },
        lowerHigh: {
          value: 0,
          enabled: false,
        },
        lowerMedium: {
          value: 0,
          enabled: false,
        },
      },
    },
    pulseOximeter: {
      spo2: {
        upperHigh: {
          value: 0,
          enabled: false,
        },
        upperMedium: {
          value: 0,
          enabled: false,
        },
        lowerHigh: {
          value: 0,
          enabled: false,
        },
        lowerMedium: {
          value: 0,
          enabled: false,
        },
      },
      pulse: {
        upperHigh: {
          value: 0,
          enabled: false,
        },
        upperMedium: {
          value: 0,
          enabled: false,
        },
        lowerHigh: {
          value: 0,
          enabled: false,
        },
        lowerMedium: {
          value: 0,
          enabled: false,
        },
      },
    },
    bloodGlucose: {
      glucose: {
        upperHigh: {
          value: 200,
          enabled: true,
        },
        upperMedium: {
          value: 175,
          enabled: true,
        },
        lowerHigh: {
          value: 100,
          enabled: true,
        },
        lowerMedium: {
          value: 125,
          enabled: true,
        },
      },
    },
    __v: 0,
  },
  id: '657e15bc3bc43400409ffb67',
  name: 'Ken Chong',
  mrn: null,
  timeZone: 'America/Los_Angeles',
  timeZone2: 'America/Los_Angeles',
  btRecentBpms: 1,
  ttRecentBpms: 2,
  btRecentWs: 3,
  ttRecentWs: 4,
  recentPulse: 5,
  recentGlucose: 6,
  withingsRecentBpm: 7,
  btMessagesRecentBpm: 8,
  btMessagesRecentWs: 9,
  adRecentBpms: 10,
  ttBpmDaysWithReadings: [],
  btBpmDaysWithReadings: [],
  btMessagesBpmDaysWithReadings: [],
  adBpmDaysWithReadings: [],
  withingsBpmDaysWithReadings: [],
  ttWsDaysWithReadings: [],
  btWsDaysWithReadings: [],
  btMessagesWsDaysWithReadings: [],
  pulseDaysWithReadings: [],
  glucoseDaysWithReadings: [],
  rt: 0,
}

export const generatePatient = (overrides = {}) => ({
  ...dummyPatient,
  ...overrides,
})
