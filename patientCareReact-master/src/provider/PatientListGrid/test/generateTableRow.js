import { generatePatient } from './generatePatient'

const dummyTableRow = {
  id: '657e15bc3bc43400409ffb67',
  name: '<PERSON>',
  timezone: 'America/Los_Angeles',
  bptaken: new Date('2024-06-28T00:34:56.000Z'),
  bp: '114/69',
  bpThresholdLevel: 'elevated',
  pulse: 77,
  weighttaken: new Date('2018-12-31T16:06:00.000Z'),
  weight: 65000,
  weightChange: 0,
  spo2Date: new Date('2024-10-29T15:32:42.000Z'),
  spo2: 100,
  pulseRate: 60,
  glucose: 184,
  glucoseDate: new Date('2023-12-21T23:10:16.000Z'),
  deviceNotificationsEnabled: true,
  patient: generatePatient(),
  cellNumber: '**********',
  email: '<EMAIL>',
  username: null,
  recentBp: 7,
  recentWs: 4,
  recentPulse: 5,
  recentGlucose: 6,
  bpDaysWithReadings: 0,
  wsDaysWithReadings: 0,
  pulseDaysWithReadings: 0,
  glucoseDaysWithReadings: 0,
  rt: 0,
}

export const generateTableRow = (overrides = {}) => ({
  ...dummyTableRow,
  ...overrides,
})
