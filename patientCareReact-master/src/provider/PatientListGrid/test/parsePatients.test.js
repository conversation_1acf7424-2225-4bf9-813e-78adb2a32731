import { parsePatients } from '../parsePatients'
import { generatePatient } from './generatePatient'
import { generateTableRow } from './generateTableRow'
import { CARDIOWELL, TRANSTEK, WITHINGS, AD } from '../../../common/manufacters'

describe('parsePatients', () => {
  it('parses entire patient', () => {
    const tableData = parsePatients([generatePatient()])
    expect(tableData.length).toBe(1)
    const tableRow = tableData[0]
    expect(tableRow).toEqual(generateTableRow())
  })
})

describe('parsePatients Blood Pressure', () => {
  it('parses patients using Bodytrace', () => {
    const tableRow = parsePatients([
      generatePatient({ selectedBpDevice: CARDIOWELL }),
    ])[0]
    expect(tableRow.bptaken).toEqual(new Date('2024-06-19T14:01:54.927Z'))
    expect(tableRow.bp).toEqual('107/77')
    expect(tableRow.pulse).toEqual(87)
    expect(tableRow.recentBp).toEqual(9)
    expect(tableRow.bpDaysWithReadings).toEqual(0)
    expect(tableRow.bpThresholdLevel).toEqual('elevated')
  })

  // Blood Pressure
  it('parses patients using Transtek', () => {
    const tableRow = parsePatients([
      generatePatient({ selectedBpDevice: TRANSTEK }),
    ])[0]
    expect(tableRow.bptaken).toEqual(new Date(********** * 1000))
    expect(tableRow.bp).toEqual('131/78')
    expect(tableRow.pulse).toEqual(65)
    expect(tableRow.recentBp).toEqual(2)
    expect(tableRow.bpDaysWithReadings).toEqual(0)
    expect(tableRow.bpThresholdLevel).toEqual('elevated')
  })

  it('parses patients using Withings', () => {
    const tableRow = parsePatients([
      generatePatient({ selectedBpDevice: WITHINGS }),
    ])[0]
    expect(tableRow.bptaken).toEqual(new Date(********** * 1000))
    expect(tableRow.bp).toEqual('114/69')
    expect(tableRow.pulse).toEqual(77)
    expect(tableRow.recentBp).toEqual(7)
    expect(tableRow.bpDaysWithReadings).toEqual(0)
    expect(tableRow.bpThresholdLevel).toEqual('elevated')
  })

  it('parses patients using A&D', () => {
    const tableRow = parsePatients([
      generatePatient({ selectedBpDevice: AD }),
    ])[0]
    expect(tableRow.bptaken).toEqual(new Date(1731166467000))
    expect(tableRow.bp).toEqual('133/80')
    expect(tableRow.pulse).toEqual(54)
    expect(tableRow.recentBp).toEqual(10)
    expect(tableRow.bpDaysWithReadings).toEqual(0)
    expect(tableRow.bpThresholdLevel).toEqual('elevated')
  })

  it('parses patients with no selected device', () => {
    const tableRow = parsePatients([
      generatePatient({ selectedBpDevice: 'Empty' }),
    ])[0]
    expect(tableRow.bptaken).toEqual(null)
    expect(tableRow.bp).toEqual('/')
    expect(tableRow.pulse).toEqual('')
    expect(tableRow.recentBp).toEqual(0)
    expect(tableRow.bpDaysWithReadings).toEqual(0)
    expect(tableRow.bpThresholdLevel).toEqual('default')
  })
})

describe('parsePatients Weight Scale', () => {
  it('parses patients using Transtek', () => {
    const tableRow = parsePatients([
      generatePatient({
        selectedWeightDevice: TRANSTEK,
        ttWs: [
          {
            _id: '657cdb63327d232768d402c0',
            deviceId: 'forward-telemetry-device-id',
            createdAt: **********,
            uid: '123456789012',
            wt: 65000,
            ts: **********,
            wet: 5,
            lts: 10,
            imei: '864475041535658',
            iccid: '89860446091891349344',
            sig: 26,
            bat: 100,
            tz: 'UTC+8',
            upload_time: **********,
            isTest: true,
            modelNumber: 'forward-telemetry-model-number',
          },
          {
            _id: '657cdb63327d232768d402c0',
            deviceId: 'forward-telemetry-device-id',
            createdAt: **********,
            uid: '123456789012',
            wt: 70000,
            ts: **********,
            wet: 5,
            lts: 10,
            imei: '864475041535658',
            iccid: '89860446091891349344',
            sig: 26,
            bat: 100,
            tz: 'UTC+8',
            upload_time: **********,
            isTest: true,
            modelNumber: 'forward-telemetry-model-number',
          },
        ],
      }),
    ])[0]
    expect(tableRow.weight).toEqual(65000)
    expect(tableRow.weightChange).toEqual(-5000)
    expect(tableRow.weighttaken).toEqual(new Date(********** * 1000))
    expect(tableRow.recentWs).toEqual(4)
    expect(tableRow.wsDaysWithReadings).toEqual(0)
  })

  test('parses patients using Bodytrace', () => {
    const tableRow = parsePatients([
      generatePatient({
        selectedWeightDevice: CARDIOWELL,
        ws: [
          {
            imei: '866771020197538',
            ts: 1734719939477,
            batteryVoltage: 5669,
            signalStrength: 37,
            unit: 1,
            weight: 77800,
            _created_at: '2024-12-20T18:38:59.477+00:00',
            _updated_at: '2024-12-20T18:38:59.477+00:00',
          },
        ],
      }),
    ])[0]
    expect(tableRow.weight).toEqual(77800)
    expect(tableRow.weightChange).toEqual(200)
    expect(tableRow.weighttaken).toEqual(
      new Date('2024-12-20T18:38:59.477+00:00')
    )
    expect(tableRow.recentWs).toEqual(12)
    expect(tableRow.wsDaysWithReadings).toEqual(0)
  })

  it('parses patients using Bodytrace where bodytracemessages is the most recent reading', () => {
    const tableRow = parsePatients([
      generatePatient({
        selectedWeightDevice: CARDIOWELL,
      }),
    ])[0]
    expect(tableRow.weight).toEqual(77600)
    expect(tableRow.weightChange).toEqual(-200)
    expect(tableRow.weighttaken).toEqual(
      new Date('2024-12-11T05:16:23.565+00:00')
    )
    expect(tableRow.recentWs).toEqual(12)
    expect(tableRow.wsDaysWithReadings).toEqual(0)
  })

  it('parses patients with no selected device', () => {
    const tableRow = parsePatients([
      generatePatient({ selectedWeightDevice: 'Empty' }),
    ])[0]
    expect(tableRow.weight).toEqual('')
    expect(tableRow.weighttaken).toEqual(null)
    expect(tableRow.recentWs).toEqual(0)
    expect(tableRow.wsDaysWithReadings).toEqual(0)
  })
})
