import { useMemo } from 'react'
import moment from 'moment-timezone'
import { useResponseState } from '../../../common/useResponsiveState.js'
import { EchartsLine } from '../../common/EChartsLine.jsx'
import { getBloodGlucoseColor } from '../../../utils/thresholdColors'
import { Box } from '@mui/material'
import {
  getMaxValue,
  getMinValue,
  getXAxisInterval,
  getChartWidth,
  convertToMilliseconds,
  formatChartDate,
  filterDataByDateRange,
  getSymbolSize,
  getLabelFontSize,
} from '../../../utils/chartUtils'

export const GlucoseChart = ({
  timeZone,
  threshold,
  chartData = [],
  imei,
  startDate,
  endDate,
  durationDays,
  fitView = false,
  isAllTimeFilter = false,
}) => {
  const { isMobile } = useResponseState()

  // Filter data based on date range
  const filteredChartData = useMemo(() => {
    if (!startDate || !endDate) {
      return chartData
    }

    const filteredData = chartData.filter((item) => {
      if (!item || !item.ts) return false

      const timestampMs =
        typeof item.ts === 'number' && item.ts < 10000000000
          ? item.ts * 1000
          : item.ts

      const pointDate = moment(timestampMs)
      const startMoment = moment(startDate).startOf('day')
      const endMoment = moment(endDate).endOf('day')
      return pointDate.isBetween(startMoment, endMoment, 'day', '[]')
    })

    return filteredData.sort((a, b) => b.ts - a.ts)
  }, [chartData, startDate, endDate])

  // Calculate width based on number of data points
  const chartWidth = useMemo(() => {
    const dataPoints = filteredChartData.length

    if (isAllTimeFilter) {
      return '100%'
    }

    if (dataPoints <= 19) {
      return '100%'
    }

    const targetPointsVisible = 19
    const widthPercentage = Math.max(
      200,
      (dataPoints / targetPointsVisible) * 100
    )
    return fitView ? '100%' : `${widthPercentage}%`
  }, [filteredChartData.length, fitView, isAllTimeFilter, isMobile])

  const { lines, dates } = useMemo(() => {
    const years = new Set()
    filteredChartData.forEach((item) => {
      if (item && item.ts) {
        const timestampMs =
          typeof item.ts === 'number' && item.ts < 10000000000
            ? item.ts * 1000
            : item.ts
        const year = moment(timestampMs).year()
        years.add(year)
      }
    })
    const hasMultipleYears = years.size > 1

    const data = filteredChartData.map((item) => {
      let taken = ''
      const timestampMs =
        typeof item.ts === 'number' && item.ts < 10000000000
          ? item.ts * 1000
          : item.ts

      const momentDate = moment(timestampMs)

      if (momentDate.isValid()) {
        const dateFormat = 'MM/DD/YY'
        taken =
          momentDate.tz(timeZone).format(dateFormat) +
          '\n' +
          momentDate.tz(timeZone).format('hh:mmA')
      } else {
        console.warn('Invalid date:', item.ts)
      }

      return {
        glucose: item.glucose,
        date: taken,
        timestamp: momentDate.valueOf(),
      }
    })

    data.sort((a, b) => b.timestamp - a.timestamp)

    const symbolSize = isAllTimeFilter && data.length > 50 ? 6 : 10
    const labelFontSize = isAllTimeFilter && data.length > 50 ? 10 : 12

    const lines = [
      {
        name: 'Glucose',
        data: data.map((item) => item.glucose),
        type: 'line',
        smooth: 0.2,
        symbol: 'circle',
        symbolSize: symbolSize,
        showSymbol: true,
        label: {
          show: true,
          position: 'top',
          fontSize: labelFontSize,
          color: '#000000',
          formatter: '{c}',
          distance: 1,
          align: 'center',
          fontWeight: 'bold',
          padding: [0, 0, 10, 0],
        },
        itemStyle: {
          color: (params) => {
            const glucoseValue = params.value
            return getBloodGlucoseColor(glucoseValue, threshold)
          },
        },
        lineStyle: {
          width: 2,
          color: '#3F51B5',
        },
      },
    ]

    const dates = data.map((item) => item.date)
    return { lines, dates }
  }, [filteredChartData, timeZone, threshold, isAllTimeFilter])

  const defaultOptions = {
    grid: {
      top: '10%',
      right: isMobile ? '25%' : '10%',
      bottom: '5%',
      left: '1%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#999',
          width: 2,
        },
      },
      formatter: function (params) {
        const glucose = params[0]?.data
        const date = params[0]?.axisValueLabel
        const color = getBloodGlucoseColor(glucose, threshold)

        return `
    <div style="white-space: nowrap; color: #333; font-size: 14px;">
      <strong>${date}</strong><br />
      <div style="display: flex; align-items: center; margin-top: 4px;">
        <div style="width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></div>
        <span>Glucose: ${glucose}</span>
      </div>
    </div>
        `
      },
    },
    xAxis: {
      type: 'category',
      data: dates,
      boundaryGap: false,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#E0E0E0',
          width: 0.5,
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
        margin: 10,
        interval: getXAxisInterval(
          dates.length,
          durationDays,
          isAllTimeFilter,
          isMobile
        ),
        rich: {
          date: {
            fontWeight: 'lighter',
            fontSize: isMobile ? 8 : 9,
            color: '#000000',
            lineHeight: isMobile ? 10 : 12,
          },
          time: {
            fontWeight: 'normal',
            fontSize: isMobile ? 8 : 10,
            color: '#9E9E9E',
            lineHeight: isMobile ? 10 : 14,
          },
        },
        formatter: function (value) {
          const [date, time] = value.split('\n')
          if (date && time) {
            return '{date|' + date + '}\n{time|' + time + '}'
          } else if (date) {
            return '{date|' + date + '}'
          } else {
            return value
          }
        },
      },
    },
    yAxis: {
      show: true,
      type: 'value',
      min: getMinValue(filteredChartData, 'glucose') - 10,
      max: getMaxValue(filteredChartData, 'glucose') + 10,
      splitLine: {
        show: false,
      },
      axisLabel: {
        margin: 30,
      },
    },
    series: lines,
    legend: {
      show: true,
      orient: 'vertical',
      right: isMobile ? '0%' : '2%',
      top: '25%',
      itemGap: 40,
      itemWidth: 30,
      itemHeight: 0,
      textStyle: {
        fontSize: 16,
        color: 'rgba(0, 0, 0, 0.6)',
        fontWeight: 'bold',
      },
      data: ['Glucose'],
    },
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        position: 'relative',
        display: 'flex',
        flexDirection: 'row',
      }}
    >
      <Box
        sx={{
          width: 60,
          minWidth: 60,
          maxWidth: 60,
          height: '100%',
          position: 'sticky',
          zIndex: 2,
          background: '#fff',
        }}
      >
        <EchartsLine
          option={{
            grid: {
              left: 0,
              right: 0,
              top: 25,
              bottom: 45,
              containLabel: true,
            },
            xAxis: { show: false },
            yAxis: {
              show: true,
              type: 'value',
              min: getMinValue(filteredChartData, 'glucose') - 10,
              max: getMaxValue(filteredChartData, 'glucose') + 10,
              splitLine: { show: false },
              axisLabel: { margin: 30 },
            },
            series: [],
            animation: false,
            tooltip: { show: false },
            legend: { show: false },
          }}
        />
      </Box>
      <Box
        sx={{
          flex: 1,
          height: '100%',
          overflowX:
            isMobile ||
            (isAllTimeFilter && !isMobile) ||
            filteredChartData.length <= 19
              ? 'hidden'
              : 'auto',
          overflowY: 'hidden',
          '&::-webkit-scrollbar': {
            height: '8px',
            display:
              isMobile ||
              (isAllTimeFilter && !isMobile) ||
              filteredChartData.length <= 19
                ? 'none'
                : 'block',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#E0E0E0',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: '#999',
            },
          },
        }}
      >
        <Box sx={{ width: chartWidth, height: '100%' }}>
          <EchartsLine
            option={{
              ...defaultOptions,
              yAxis: {
                show: false,
                type: 'value',
                min: getMinValue(filteredChartData, 'glucose') - 10,
                max: getMaxValue(filteredChartData, 'glucose') + 10,
              },
              grid: {
                ...defaultOptions.grid,
                left: 0,
              },
            }}
          />
        </Box>
      </Box>
    </Box>
  )
}
