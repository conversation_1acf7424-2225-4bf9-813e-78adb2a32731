import { useState } from 'react'
import { IconButton, Menu, MenuItem, Typography, Divider } from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import IosShareIcon from '@mui/icons-material/IosShare'
import { useResponseState } from '../../common/useResponsiveState'
import { generatePdf } from '../../reports/generatePdf'

export const InfoDropdown = ({
  items = [],
  fitView,
  setFitView,
  imei,
  firstName,
  lastName,
  manufacturer,
  months,
  bpmData,
  avgSys,
  avgDia,
}) => {
  const [anchorEl, setAnchorEl] = useState(null)
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false)
  const { isMobile } = useResponseState()
  const open = Boolean(anchorEl)

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  const toggleFitView = () => {
    setFitView((prev) => !prev)
    handleClose()
  }

  const handleShareReport = async () => {
    try {
      setIsGeneratingPdf(true)
      await generatePdf({
        firstName,
        lastName,
        imei,
        manufacturer,
        months,
        bpmData,
        avgSys,
        avgDia,
      })
    } finally {
      setIsGeneratingPdf(false)
      handleClose()
    }
  }

  return (
    <div>
      <IconButton
        aria-label="info-dropdown"
        size="large"
        onClick={handleClick}
        color="black"
      >
        <ExpandMoreIcon fontSize="inherit" />
      </IconButton>
      <Menu
        id="info-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
      >
        {items.map((item, index) => (
          <MenuItem style={{ backgroundColor: 'white' }} key={index}>
            {item}
          </MenuItem>
        ))}
        {isMobile && (
          <MenuItem
            style={{ backgroundColor: 'white' }}
            onClick={() => toggleFitView()}
          >
            <Typography variant="button">
              {fitView ? 'Show recent data' : 'Show all data'}
            </Typography>
          </MenuItem>
        )}
        <MenuItem
          style={{
            backgroundColor: 'white',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: isGeneratingPdf ? 'rgba(0, 0, 0, 0.38)' : 'inherit',
          }}
          onClick={handleShareReport}
          disabled={isGeneratingPdf}
        >
          <Typography variant="button">
            {isGeneratingPdf ? 'GENERATING REPORT...' : 'SHARE REPORT'}
          </Typography>
        </MenuItem>
        <MenuItem style={{ backgroundColor: 'white' }}>
          <Typography>IMEI: {imei || 'N/A'}</Typography>
        </MenuItem>
      </Menu>
    </div>
  )
}
