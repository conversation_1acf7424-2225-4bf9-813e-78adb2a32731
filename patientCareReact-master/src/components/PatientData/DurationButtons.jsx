import { Button } from '@material-ui/core'
import { useState } from 'react'

export const useDurationDays = () => {
  const [durationText, setDurationText] = useState('Last 1 month')
  const [months, setMonths] = useState(1)
  return {
    durationText,
    setDurationText,
    months,
    setMonths,
  }
}

export const DurationButtons = ({ days, setDurationText, setMonths }) => {
  return (
    <div style={{ float: 'right', marginTop: '5px' }}>
      <h6>
        <b>Duration</b>
      </h6>{' '}
      {days === 1 ? (
        <Button
          style={{ backgroundColor: '#F0F0F0' }}
          onClick={() => setMonths(1)}
        >
          1 month
        </Button>
      ) : (
        <Button
          onClick={() => {
            setMonths(1)
            setDurationText('Last 1 month')
          }}
        >
          1 month
        </Button>
      )}
      {days === 3 ? (
        <Button
          style={{ backgroundColor: '#F0F0F0' }}
          onClick={() => setMonths(3)}
        >
          3 months
        </Button>
      ) : (
        <Button
          onClick={() => {
            setMonths(3)
            setDurationText('Last 3 months')
          }}
        >
          3 months
        </Button>
      )}
      {days === 6 ? (
        <Button
          style={{ backgroundColor: '#F0F0F0' }}
          onClick={() => setMonths(6)}
        >
          6 months
        </Button>
      ) : (
        <Button
          onClick={() => {
            setMonths(6)
            setDurationText('Last 6 months')
          }}
        >
          6 months
        </Button>
      )}
      {days === 12 ? (
        <Button
          style={{ backgroundColor: '#F0F0F0' }}
          onClick={() => setMonths(12)}
        >
          1 year
        </Button>
      ) : (
        <Button
          onClick={() => {
            setMonths(12)
            setDurationText('Last 1 year')
          }}
        >
          1 year
        </Button>
      )}
      {days === 24 ? (
        <Button
          style={{ backgroundColor: '#F0F0F0' }}
          onClick={() => setMonths(24)}
        >
          2 years
        </Button>
      ) : (
        <Button
          onClick={() => {
            setMonths(24)
            setDurationText('Last 2 years')
          }}
        >
          2 years
        </Button>
      )}
      {days === 20 ? (
        <Button
          style={{ backgroundColor: '#F0F0F0' }}
          onClick={() => setMonths(20)}
        >
          All
        </Button>
      ) : (
        <Button
          onClick={() => {
            setMonths(20)
            setDurationText('All time')
          }}
        >
          All
        </Button>
      )}
    </div>
  )
}
