import { calculateOverallForBpm } from './calculateOverallForBpm'

describe('calculateOverallForBpm', () => {
  it('should calculate the difference between first and last week averages', () => {
    const testData = [
      {
        date: new Date('2025-01-10T21:37:40.000Z'),
        time: '1:37 PM',
        systolic: 122,
        diastolic: 85,
        pulse: 73,
      },
      {
        date: new Date('2024-09-26T22:55:18.000Z'),
        time: '3:55 PM',
        systolic: 127,
        diastolic: 81,
        pulse: 74,
      },
      {
        date: new Date('2024-08-21T23:16:12.000Z'),
        time: '4:16 PM',
        systolic: 112,
        diastolic: 81,
        pulse: 81,
      },
      {
        date: new Date('2024-08-14T21:15:06.000Z'),
        time: '2:15 PM',
        systolic: 117,
        diastolic: 74,
        pulse: 73,
      },
      {
        date: new Date('2024-07-16T14:33:24.000Z'),
        time: '7:33 AM',
        systolic: 116,
        diastolic: 80,
        pulse: 60,
      },
      {
        date: new Date('2024-07-16T14:32:02.000Z'),
        time: '7:32 AM',
        systolic: 116,
        diastolic: 77,
        pulse: 59,
      },
      {
        date: new Date('2024-07-16T14:27:56.000Z'),
        time: '7:27 AM',
        systolic: 116,
        diastolic: 77,
        pulse: 59,
      },
      {
        date: new Date('2024-06-25T18:34:46.000Z'),
        time: '11:34 AM',
        systolic: 163,
        diastolic: 100,
        pulse: 60,
      },
      {
        date: new Date('2024-06-25T16:37:28.000Z'),
        time: '9:37 AM',
        systolic: 125,
        diastolic: 78,
        pulse: 61,
      },
      {
        date: new Date('2024-06-19T22:37:14.000Z'),
        time: '3:37 PM',
        systolic: 117,
        diastolic: 74,
        pulse: 67,
      },
      {
        date: new Date('2024-06-18T17:42:12.000Z'),
        time: '10:42 AM',
        systolic: 125,
        diastolic: 79,
        pulse: 63,
      },
      {
        date: new Date('2024-06-17T14:03:18.000Z'),
        time: '7:03 AM',
        systolic: 130,
        diastolic: 80,
        pulse: 64,
      },
      {
        date: new Date('2024-06-07T14:46:32.000Z'),
        time: '7:46 AM',
        systolic: 124,
        diastolic: 82,
        pulse: 56,
      },
      {
        date: new Date('2024-06-07T14:42:46.000Z'),
        time: '7:42 AM',
        systolic: 124,
        diastolic: 81,
        pulse: 57,
      },
      {
        date: new Date('2024-05-23T15:31:32.000Z'),
        time: '8:31 AM',
        systolic: 124,
        diastolic: 81,
        pulse: 77,
      },
      {
        date: new Date('2024-05-21T21:55:34.000Z'),
        time: '2:55 PM',
        systolic: 132,
        diastolic: 84,
        pulse: 77,
      },
      {
        date: new Date('2024-05-21T20:47:24.000Z'),
        time: '1:47 PM',
        systolic: 136,
        diastolic: 79,
        pulse: 85,
      },
      {
        date: new Date('2024-05-15T15:26:20.000Z'),
        time: '8:26 AM',
        systolic: 123,
        diastolic: 81,
        pulse: 82,
      },
      {
        date: new Date('2024-05-03T23:29:32.000Z'),
        time: '4:29 PM',
        systolic: 114,
        diastolic: 77,
        pulse: 88,
      },
      {
        date: new Date('2024-05-03T23:27:10.000Z'),
        time: '4:27 PM',
        systolic: 117,
        diastolic: 75,
        pulse: 77,
      },
      {
        date: new Date('2024-05-03T23:24:50.000Z'),
        time: '4:24 PM',
        systolic: 114,
        diastolic: 74,
        pulse: 82,
      },
      {
        date: new Date('2024-04-19T06:42:42.000Z'),
        time: '11:42 PM',
        systolic: 124,
        diastolic: 86,
        pulse: 93,
      },
      {
        date: new Date('2024-04-19T06:40:34.000Z'),
        time: '11:40 PM',
        systolic: 131,
        diastolic: 88,
        pulse: 101,
      },
      {
        date: new Date('2024-04-15T00:20:40.000Z'),
        time: '5:20 PM',
        systolic: 126,
        diastolic: 87,
        pulse: 69,
      },
      {
        date: new Date('2024-04-13T00:01:28.000Z'),
        time: '5:01 PM',
        systolic: 123,
        diastolic: 82,
        pulse: 64,
      },
      {
        date: new Date('2024-04-11T01:27:46.000Z'),
        time: '6:27 PM',
        systolic: 122,
        diastolic: 86,
        pulse: 67,
      },
      {
        date: new Date('2024-04-10T01:26:52.000Z'),
        time: '6:26 PM',
        systolic: 146,
        diastolic: 85,
        pulse: 79,
      },
      {
        date: new Date('2024-04-06T23:06:12.000Z'),
        time: '4:06 PM',
        systolic: 129,
        diastolic: 86,
        pulse: 54,
      },
      {
        date: new Date('2024-04-05T01:11:10.000Z'),
        time: '6:11 PM',
        systolic: 134,
        diastolic: 84,
        pulse: 65,
      },
      {
        date: new Date('2024-04-05T01:06:00.000Z'),
        time: '6:06 PM',
        systolic: 134,
        diastolic: 83,
        pulse: 63,
      },
      {
        date: new Date('2024-04-05T01:03:56.000Z'),
        time: '6:03 PM',
        systolic: 137,
        diastolic: 80,
        pulse: 63,
      },
      {
        date: new Date('2024-04-03T04:45:18.000Z'),
        time: '9:45 PM',
        systolic: 103,
        diastolic: 67,
        pulse: 62,
      },
      {
        date: new Date('2024-04-03T04:41:40.000Z'),
        time: '9:41 PM',
        systolic: 102,
        diastolic: 70,
        pulse: 65,
      },
      {
        date: new Date('2024-03-27T02:45:42.000Z'),
        time: '7:45 PM',
        systolic: 131,
        diastolic: 80,
        pulse: 71,
      },
      {
        date: new Date('2024-03-21T15:27:58.000Z'),
        time: '8:27 AM',
        systolic: 119,
        diastolic: 79,
        pulse: 60,
      },
      {
        date: new Date('2024-03-20T18:29:46.000Z'),
        time: '11:29 AM',
        systolic: 129,
        diastolic: 91,
        pulse: 65,
      },
      {
        date: new Date('2024-03-20T18:23:04.000Z'),
        time: '11:23 AM',
        systolic: 117,
        diastolic: 78,
        pulse: 55,
      },
      {
        date: new Date('2024-01-23T07:54:34.000Z'),
        time: '11:54 PM',
        systolic: 123,
        diastolic: 83,
        pulse: 59,
      },
      {
        date: new Date('2023-12-21T01:20:02.000Z'),
        time: '5:20 PM',
        systolic: 136,
        diastolic: 81,
        pulse: 81,
      },
      {
        date: new Date('2023-12-21T01:12:24.000Z'),
        time: '5:12 PM',
        systolic: 146,
        diastolic: 88,
        pulse: 67,
      },
      {
        date: new Date('2023-12-20T03:39:28.000Z'),
        time: '7:39 PM',
        systolic: 121,
        diastolic: 84,
        pulse: 70,
      },
      {
        date: new Date('2023-12-20T03:33:16.000Z'),
        time: '7:33 PM',
        systolic: 125,
        diastolic: 87,
        pulse: 67,
      },
      {
        date: new Date('2023-12-19T20:54:04.000Z'),
        time: '12:54 PM',
        systolic: 137,
        diastolic: 90,
        pulse: 51,
      },
      {
        date: new Date('2023-12-19T20:42:16.000Z'),
        time: '12:42 PM',
        systolic: 143,
        diastolic: 93,
        pulse: 52,
      },
      {
        date: new Date('2023-12-19T15:09:44.000Z'),
        time: '7:09 AM',
        systolic: 106,
        diastolic: 78,
        pulse: 68,
      },
      {
        date: new Date('2023-12-19T03:11:46.000Z'),
        time: '7:11 PM',
        systolic: 143,
        diastolic: 92,
        pulse: 68,
      },
    ]

    const result = calculateOverallForBpm(testData)

    // First week average (last entries in the array)
    // Last week entries (2023-12-19 to 2023-12-21):
    // - 143, 92
    // - 106, 78
    // - 143, 93
    // - 137, 90
    // - 125, 87
    // - 121, 84
    // - 146, 88
    // - 136, 81
    // Average: 137.6, 86.6

    // Last week average (first entries in the array)
    // First week entries (2025-01-10):
    // - 122, 85
    // Average: 122, 85

    // Expected differences:
    // Systolic: 137.6 - 122 = 15.6
    // Diastolic: 86.6 - 85 = 1.6

    expect(result).toEqual({
      systolic: -10,
      diastolic: -2,
    })
  })

  it('should return null for empty array', () => {
    expect(calculateOverallForBpm([])).toBeNull()
  })

  it('should return null for null input', () => {
    expect(calculateOverallForBpm(null)).toBeNull()
  })
})
