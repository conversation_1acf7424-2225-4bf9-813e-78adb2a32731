import { useMemo } from 'react'
import moment from 'moment-timezone'
import {
  gramsToKgsString,
  gramsToLbsString,
} from '../../../common/conversions.js'
import { MultiLineChart } from '../../common/MultiLineChart.jsx'
import { ThresholdDot } from '../../common/ThresholdDot.jsx'
import { green } from '../../../common/colors.js'

export const WeightScaleChart = ({
  threshold,
  chartData = [],
  weightUnit,
  lowWeight,
  highWeight,
  timeZone,
  durationDays,
  isAllTimeFilter = false,
}) => {
  const data = useMemo(() => {
    let processedData = chartData

    if (!isAllTimeFilter && chartData.length > 19) {
      const step = Math.floor(chartData.length / 19)
      processedData = chartData
        .filter((_, index) => index % step === 0)
        .slice(0, 19)
    }

    return processedData.map((data) => {
      let taken = ''
      if (moment(data.ts).tz(timeZone)) {
        taken = moment(data.ts).tz(timeZone).format('MM/DD/YY')
      }
      return {
        date: taken,
        Weight:
          weightUnit === 'Lbs'
            ? gramsToLbsString(data.weight)
            : gramsToKgsString(data.weight),
      }
    })
  }, [chartData, timeZone, weightUnit, isAllTimeFilter])
  const maxDataWeight = Math.ceil(
    Math.max(...data.map((d) => parseFloat(d.Weight) + 25))
  )
  const minDataWeight = Math.floor(
    Math.min(...data.map((d) => parseFloat(d.Weight) - 25))
  )
  return (
    <MultiLineChart
      title={'Weight Trend'}
      label={weightUnit}
      lines={[
        {
          key: 'Weight',
          color: green,
          dot: () => <ThresholdDot threshold={threshold} />,
        },
      ]}
      data={data}
      domain={[minDataWeight, maxDataWeight]}
    />
  )
}
