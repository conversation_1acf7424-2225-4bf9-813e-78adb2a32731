import { parseWeightScaleMeasures } from './weightScale'
import { TRANSTEK, CARDIOWELL } from '../../../common/manufacters'
import { ttWsMockData } from '../../../measurements/ws/tests/ttWsMockData'
import { btWsMockData } from '../../../measurements/ws/tests/btWsMockData'
import { btMessageWsMockData } from '../../../measurements/ws/tests/btMessageWsMockData'

const testData = ({
  ws,
  btMessagesWs,
  ttWs,
  weightDevice,
  weightUnit,
  timeframe,
  timeZone = 'America/Los_Angeles',
  expectedArrayWs,
  expectedWsTableArray,
  expectedLowWeight,
  expectedHighWeight,
}) => {
  const { arrayWS, wsTableArray, lowWeight, highWeight } =
    parseWeightScaleMeasures({
      ws,
      btMessagesWs,
      ttWs,
      weightDevice,
      weightUnit,
      timeframe,
      timeZone,
    })
  expect(arrayWS).toEqual(expectedArrayWs)
  expect(wsTableArray).toEqual(expectedWsTableArray)
  expect(lowWeight).toEqual(expectedLowWeight)
  expect(highWeight).toEqual(expectedHighWeight)
}

test('Parses Transtek WS Measurements', () => {
  testData({
    ttWs: ttWsMockData,
    weightDevice: TRANSTEK,
    weightUnit: 'kg',
    expectedArrayWs: [
      {
        ts: 1546272360000,
        weight: 67000,
      },
      {
        ts: 1546272360000,
        weight: 75000,
      },
      {
        ts: 1546272360000,
        weight: 65000,
      },
    ],
    expectedWsTableArray: [
      {
        date: new Date('2018-12-31T16:06:00.000Z'),
        time: '8:06 AM',
        weight: '67.00',
      },
      {
        date: new Date('2018-12-31T16:06:00.000Z'),
        time: '8:06 AM',
        weight: '75.00',
      },
      {
        date: new Date('2018-12-31T16:06:00.000Z'),
        time: '8:06 AM',
        weight: '65.00',
      },
    ],
    expectedLowWeight: 10,
    expectedHighWeight: 250,
  })

  testData({
    ttWs: ttWsMockData,
    weightDevice: TRANSTEK,
    weightUnit: 'Lbs',
    expectedArrayWs: [
      {
        ts: 1546272360000,
        weight: 67000,
      },
      {
        ts: 1546272360000,
        weight: 75000,
      },
      {
        ts: 1546272360000,
        weight: 65000,
      },
    ],
    expectedWsTableArray: [
      {
        date: new Date('2018-12-31T16:06:00.000Z'),
        time: '8:06 AM',
        weight: '147.71',
      },
      {
        date: new Date('2018-12-31T16:06:00.000Z'),
        time: '8:06 AM',
        weight: '165.34',
      },
      {
        date: new Date('2018-12-31T16:06:00.000Z'),
        time: '8:06 AM',
        weight: '143.30',
      },
    ],
    expectedLowWeight: 25,
    expectedHighWeight: 500,
  })
})

test('Parses Bodytrace WS Measurements', () => {
  testData({
    ws: btWsMockData,
    btMessagesWs: btMessageWsMockData,
    weightDevice: CARDIOWELL,
    weightUnit: 'kg',
    expectedArrayWs: [
      {
        ts: 1734021977209,
        weight: 76200,
      },
      {
        ts: 1733894183565,
        weight: 77600,
      },
      {
        ts: 1733721596753,
        weight: 79200,
      },
      {
        ts: 1718908739477,
        weight: 77800,
      },
      {
        ts: 1718842649386,
        weight: 78300,
      },
      {
        ts: 1718834171236,
        weight: 78000,
      },
    ],
    expectedWsTableArray: [
      {
        date: new Date('2024-12-12T16:46:17.209Z'),
        time: '8:46 AM',
        weight: '76.20',
      },
      {
        date: new Date('2024-12-11T05:16:23.565Z'),
        time: '9:16 PM',
        weight: '77.60',
      },
      {
        date: new Date('2024-12-09T05:19:56.753Z'),
        time: '9:19 PM',
        weight: '79.20',
      },
      {
        date: new Date('2024-06-20T18:38:59.477Z'),
        time: '11:38 AM',
        weight: '77.80',
      },
      {
        date: new Date('2024-06-20T00:17:29.386Z'),
        time: '5:17 PM',
        weight: '78.30',
      },
      {
        date: new Date('2024-06-19T21:56:11.236Z'),
        time: '2:56 PM',
        weight: '78.00',
      },
    ],
    expectedLowWeight: 10,
    expectedHighWeight: 250,
  })

  testData({
    ws: btWsMockData,
    btMessagesWs: btMessageWsMockData,
    weightDevice: CARDIOWELL,
    weightUnit: 'Lbs',
    expectedArrayWs: [
      {
        ts: 1734021977209,
        weight: 76200,
      },
      {
        ts: 1733894183565,
        weight: 77600,
      },
      {
        ts: 1733721596753,
        weight: 79200,
      },
      {
        ts: 1718908739477,
        weight: 77800,
      },
      {
        ts: 1718842649386,
        weight: 78300,
      },
      {
        ts: 1718834171236,
        weight: 78000,
      },
    ],
    expectedWsTableArray: [
      {
        date: new Date('2024-12-12T16:46:17.209Z'),
        time: '8:46 AM',
        weight: '167.99',
      },
      {
        date: new Date('2024-12-11T05:16:23.565Z'),
        time: '9:16 PM',
        weight: '171.08',
      },
      {
        date: new Date('2024-12-09T05:19:56.753Z'),
        time: '9:19 PM',
        weight: '174.60',
      },
      {
        date: new Date('2024-06-20T18:38:59.477Z'),
        time: '11:38 AM',
        weight: '171.52',
      },
      {
        date: new Date('2024-06-20T00:17:29.386Z'),
        time: '5:17 PM',
        weight: '172.62',
      },
      {
        date: new Date('2024-06-19T21:56:11.236Z'),
        time: '2:56 PM',
        weight: '171.96',
      },
    ],
    expectedLowWeight: 25,
    expectedHighWeight: 500,
  })
})
