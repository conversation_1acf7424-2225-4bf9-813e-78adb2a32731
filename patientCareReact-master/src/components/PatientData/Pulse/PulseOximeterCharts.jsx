import { useMemo } from 'react'
import moment from 'moment-timezone'
import { MultiLineChart } from '../../common/MultiLineChart.jsx'
import { ThresholdDot } from '../../common/ThresholdDot.jsx'
import { green } from '../../../common/colors.js'

export const BloodOxygenChart = ({
  threshold,
  chartData = [],
  durationDays,
  isAllTimeFilter = false,
}) => {
  const data = useMemo(() => {
    let processedData = chartData

    if (!isAllTimeFilter && chartData.length > 19) {
      const step = Math.floor(chartData.length / 19)
      processedData = chartData
        .filter((_, index) => index % step === 0)
        .slice(0, 19)
    }

    return processedData.map((data) => {
      let taken = ''
      const momentDate = moment(data.time, 'YY/MM/DD,hh:mm:ss±zz')
      if (
        durationDays === 93 ||
        durationDays === 186 ||
        durationDays === 365 ||
        durationDays === 730
      ) {
        taken =
          momentDate.format('MM/DD/YY') + '\n' + momentDate.format('hh:mmA')
      } else {
        taken = momentDate.format('MM/DD/YY')
      }
      return {
        date: taken,
        SPO2: data.spo2,
      }
    })
  }, [chartData, durationDays, isAllTimeFilter])
  return (
    <MultiLineChart
      title={'SPO2 Trend'}
      label={'%'}
      lines={[
        {
          key: 'SPO2',
          color: green,
          dot: () => <ThresholdDot threshold={threshold} />,
        },
      ]}
      data={data}
      domain={[60, 100]}
    />
  )
}

export const HeartRateOximeterChart = ({
  threshold,
  chartData = [],
  durationDays,
  isAllTimeFilter = false,
}) => {
  const data = useMemo(() => {
    let processedData = chartData

    if (!isAllTimeFilter && chartData.length > 19) {
      const step = Math.floor(chartData.length / 19)
      processedData = chartData
        .filter((_, index) => index % step === 0)
        .slice(0, 19)
    }

    return processedData.map((data) => {
      let taken = ''
      const momentDate = moment(data.time, 'YY/MM/DD,hh:mm:ss±zz')
      if (
        durationDays === 93 ||
        durationDays === 186 ||
        durationDays === 365 ||
        durationDays === 730
      ) {
        taken =
          momentDate.format('MM/DD/YY') + '\n' + momentDate.format('hh:mmA')
      } else {
        taken = momentDate.format('MM/DD/YY')
      }
      return {
        date: taken,
        BPM: data.pr,
      }
    })
  }, [chartData, durationDays, isAllTimeFilter])
  return (
    <MultiLineChart
      title={'Heart Rate Trend - From Oximeter'}
      label={'BPM'}
      lines={[
        {
          key: 'BPM',
          color: green,
          dot: () => <ThresholdDot threshold={threshold} />,
        },
      ]}
      data={data}
      domain={[40, 220]}
    />
  )
}
