import moment from 'moment-timezone'
import { parseBpMeasures } from './bloodPressure'
import { AD, TRANSTEK, WITHINGS, CARDIOWELL } from '../../../common/manufacters'
import {
  adBpmMockData,
  standardizedAdBpm,
} from '../../../measurements/bpm/tests/adMockData'
import { btBpmMockData } from '../../../measurements/bpm/tests/btBpmMockData'
import { btMessageBpmMockData } from '../../../measurements/bpm/tests/btMessageBpmMockData'
import { transtekBpmMockData } from '../../../measurements/bpm/tests/transtekBpmMockData'
import { withingsBpmMockData } from '../../../measurements/bpm/tests/withingsBpmMockData'

const timeZone = 'America/Los_Angeles'

const testData = ({
  bpm,
  btMessagesBpm,
  ttBpm,
  adBpm,
  withingsBpm,
  bpDevice,
  expectArrayBp,
  expectedHighDia,
  expectedHighSys,
  expectedLowDia,
  expectedLowSys,
  expectedAvgDia,
  expectedAvgSys,
  expectedBpmTableArrary,
  expectedLastReading,
  timeframe,
}) => {
  const {
    arrayBP,
    bpmTableArray,
    highDia,
    highSys,
    lowDia,
    lowSys,
    avgDia,
    avgSys,
    lastReading,
  } = parseBpMeasures({
    bpm,
    btMessagesBpm,
    ttBpm,
    adBpm,
    withingsBpm,
    bpDevice,
    timeframe,
    timeZone: 'America/Los_Angeles',
  })
  expect(arrayBP).toEqual(expectArrayBp)
  expect(highDia).toEqual(expectedHighDia)
  expect(highSys).toEqual(expectedHighSys)
  expect(lowDia).toEqual(expectedLowDia)
  expect(lowSys).toEqual(expectedLowSys)
  expect(avgDia).toEqual(expectedAvgDia)
  expect(avgSys).toEqual(expectedAvgSys)
  expect(bpmTableArray).toEqual(expectedBpmTableArrary)
  expect(lastReading).toEqual(expectedLastReading)
}

const testEmptyData = (parsedBpData) => {
  expect(parsedBpData.arrayBP.length).toEqual(0)
  expect(parsedBpData.bpmTableArray.length).toEqual(0)
  expect(parsedBpData.highDia).toEqual('')
  expect(parsedBpData.highSys).toEqual('')
  expect(parsedBpData.lowDia).toEqual('')
  expect(parsedBpData.lowSys).toEqual('')
  expect(parsedBpData.avgDia).toEqual('')
  expect(parsedBpData.avgSys).toEqual('')
  expect(parsedBpData.lastReading).toEqual({})
}

test('Parses Bodytrace Measurements', () => {
  testData({
    bpm: btBpmMockData,
    btMessagesBpm: btMessageBpmMockData,
    bpDevice: CARDIOWELL,
    expectArrayBp: [
      {
        diastolic: 77,
        pulse: 87,
        systolic: 107,
        ts: '2024-06-19T14:01:54.927Z',
      },
      {
        diastolic: 77,
        pulse: 87,
        systolic: 107,
        ts: '2024-06-19T13:24:18.990Z',
      },
      {
        diastolic: 77,
        pulse: 87,
        systolic: 107,
        ts: '2024-06-13T17:07:21.677Z',
      },
      {
        diastolic: 84,
        pulse: 76,
        systolic: 129,
        ts: '2016-06-03T05:07:46.329Z',
      },
      {
        diastolic: 85,
        pulse: 82,
        systolic: 129,
        ts: '2016-06-03T04:48:35.824Z',
      },
      {
        diastolic: 74,
        pulse: 53,
        systolic: 118,
        ts: '2016-05-19T16:26:22.275Z',
      },
    ],
    expectedHighDia: 84,
    expectedHighSys: 129,
    expectedLowDia: 77,
    expectedLowSys: 107,
    expectedAvgDia: 79,
    expectedAvgSys: 116,
    expectedBpmTableArrary: [
      {
        date: new Date('2024-06-19T14:01:54.927Z'),
        time: '7:01 AM',
        systolic: 107,
        diastolic: 77,
        pulse: 87,
      },
      {
        date: new Date('2024-06-19T13:24:18.990Z'),
        time: '6:24 AM',
        systolic: 107,
        diastolic: 77,
        pulse: 87,
      },
      {
        date: new Date('2024-06-13T17:07:21.677Z'),
        time: '10:07 AM',
        systolic: 107,
        diastolic: 77,
        pulse: 87,
      },
      {
        date: new Date('2016-06-03T05:07:46.329Z'),
        time: '10:07 PM',
        systolic: 129,
        diastolic: 84,
        pulse: 76,
      },
      {
        date: new Date('2016-06-03T04:48:35.824Z'),
        time: '9:48 PM',
        systolic: 129,
        diastolic: 85,
        pulse: 82,
      },
      {
        date: new Date('2016-05-19T16:26:22.275Z'),
        time: '9:26 AM',
        systolic: 118,
        diastolic: 74,
        pulse: 53,
      },
    ],
    expectedLastReading: {
      diastolic: 77,
      pulse: 87,
      systolic: 107,
      ts: '2024-06-19T14:01:54.927Z',
    },
  })
})

test('Parses Transtek Measurements', () => {
  testData({
    ttBpm: transtekBpmMockData,
    bpDevice: TRANSTEK,
    expectArrayBp: [
      {
        diastolic: 85,
        pulse: 65,
        systolic: 120,
        ts: 1721261958000,
      },
      {
        diastolic: 85,
        pulse: 65,
        systolic: 120,
        ts: 1720469847000,
      },
      {
        diastolic: 81,
        pulse: 65,
        systolic: 130,
        ts: 1720469839000,
      },
    ],
    expectedHighDia: 81,
    expectedHighSys: 130,
    expectedLowDia: 85,
    expectedLowSys: 120,
    expectedAvgDia: 84,
    expectedAvgSys: 123,
    expectedBpmTableArrary: [
      {
        date: new Date('2024-07-18T00:19:18.000Z'),
        diastolic: 85,
        pulse: 65,
        systolic: 120,
        time: '5:19 PM',
      },
      {
        date: new Date('2024-07-08T20:17:27.000Z'),
        diastolic: 85,
        pulse: 65,
        systolic: 120,
        time: '1:17 PM',
      },
      {
        date: new Date('2024-07-08T20:17:19.000Z'),
        diastolic: 81,
        pulse: 65,
        systolic: 130,
        time: '1:17 PM',
      },
    ],
    expectedLastReading: {
      diastolic: 85,
      pulse: 65,
      systolic: 120,
      ts: 1721261958000,
    },
  })
})

test('Parses All A&D Measurements', () => {
  testData({
    adBpm: adBpmMockData,
    bpDevice: AD,
    expectArrayBp: [
      standardizedAdBpm,
      {
        diastolic: 90,
        pulse: 60,
        systolic: 150,
        ts: 1730475267000,
      },
      {
        diastolic: 75,
        pulse: 45,
        systolic: 120,
        ts: 1728488067000,
      },
    ],
    expectedHighDia: 90,
    expectedHighSys: 150,
    expectedLowDia: 75,
    expectedLowSys: 120,
    expectedAvgDia: 82,
    expectedAvgSys: 134,
    expectedBpmTableArrary: [
      {
        date: moment(1731166467000).tz(timeZone).toDate(),
        time: '7:34 AM',
        systolic: 133,
        diastolic: 80,
        pulse: 54,
      },
      {
        date: moment(1730475267000).tz(timeZone).toDate(),
        diastolic: 90,
        pulse: 60,
        systolic: 150,
        time: '8:34 AM',
      },
      {
        date: moment(1728488067000).tz(timeZone).toDate(),
        diastolic: 75,
        pulse: 45,
        systolic: 120,
        time: '8:34 AM',
      },
    ],
    expectedLastReading: standardizedAdBpm,
  })
})

test('Parses Some A&D Measurements', () => {
  const {
    arrayBP,
    bpmTableArray,
    highDia,
    highSys,
    lowDia,
    lowSys,
    avgDia,
    avgSys,
  } = parseBpMeasures({
    adBpm: adBpmMockData,
    bpDevice: AD,
    timeframe: 1730444400000,
    timeZone,
  })
  expect(arrayBP.length).toEqual(2)
  expect(bpmTableArray.length).toEqual(2)
  expect(highDia).toEqual(90)
  expect(highSys).toEqual(150)
  expect(lowDia).toEqual(80)
  expect(lowSys).toEqual(133)
  expect(avgDia).toEqual(85)
  expect(avgSys).toEqual(142)

  const noData = parseBpMeasures({
    adBpm: [],
    bpDevice: AD,
    timeframe: 0,
    timeZone,
  })
  testEmptyData(noData)

  const outOfTimeframe = parseBpMeasures({
    adBpm: adBpmMockData,
    bpDevice: AD,
    timeframe: 1731470055000,
    timeZone,
  })
  expect(outOfTimeframe.arrayBP.length).toEqual(0)
  expect(outOfTimeframe.bpmTableArray.length).toEqual(0)
  expect(outOfTimeframe.highDia).toEqual('')
  expect(outOfTimeframe.highSys).toEqual('')
  expect(outOfTimeframe.lowDia).toEqual('')
  expect(outOfTimeframe.lowSys).toEqual('')
  expect(outOfTimeframe.avgDia).toEqual('')
  expect(outOfTimeframe.avgSys).toEqual('')
  expect(outOfTimeframe.lastReading).toEqual(standardizedAdBpm)
})

test('Parses Withings Measurements', () => {
  testData({
    withingsBpm: withingsBpmMockData,
    bpDevice: WITHINGS,
    expectArrayBp: [
      {
        diastolic: 69,
        pulse: 77,
        systolic: 114,
        ts: 1719534896000,
      },
      {
        diastolic: 70,
        pulse: 76,
        systolic: 114,
        ts: 1719534893000,
      },
      {
        diastolic: 73,
        pulse: 82,
        systolic: 120,
        ts: 1719534890000,
      },
    ],
    expectedHighDia: 73,
    expectedHighSys: 120,
    expectedLowDia: 69,
    expectedLowSys: 114,
    expectedAvgDia: 71,
    expectedAvgSys: 116,
    expectedBpmTableArrary: [
      {
        date: new Date('2024-06-28T00:34:56.000Z'),
        diastolic: 69,
        pulse: 77,
        systolic: 114,
        time: '5:34 PM',
      },
      {
        date: new Date('2024-06-28T00:34:53.000Z'),
        diastolic: 70,
        pulse: 76,
        systolic: 114,
        time: '5:34 PM',
      },
      {
        date: new Date('2024-06-28T00:34:50.000Z'),
        diastolic: 73,
        pulse: 82,
        systolic: 120,
        time: '5:34 PM',
      },
    ],
    expectedLastReading: {
      diastolic: 69,
      pulse: 77,
      systolic: 114,
      ts: 1719534896000,
    },
  })
})
