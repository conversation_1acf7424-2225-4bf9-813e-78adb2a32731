const getPeriodSum = (period) => {
  return period.reduce(
    (acc, curr) => {
      acc.diastolic += curr.diastolic
      acc.systolic += curr.systolic
      return acc
    },
    { diastolic: 0, systolic: 0 }
  )
}

export const calculateOverallForBpm = ({
  readings,
  commonAvgDia,
  commonAvgSys,
}) => {
  if (!readings || readings.length === 0 || !commonAvgDia || !commonAvgSys) {
    return null
  }

  const currentPeriodSum = getPeriodSum(readings)

  const currentPeriodDiastolicAvg = currentPeriodSum.diastolic / readings.length
  const currentPeriodSystolicAvg = currentPeriodSum.systolic / readings.length

  return {
    diastolic: Math.round(currentPeriodDiastolicAvg - commonAvgDia),
    systolic: Math.round(currentPeriodSystolicAvg - commonAvgSys),
  }
}

export const calculateOverallForBpm2 = ({ readings }) => {
  if (!readings || readings.length === 0) {
    return null
  }

  const firstReading = readings[0]
  const lastReading = readings[readings.length - 1]

  return {
    diastolic: Math.round(firstReading.diastolic - lastReading.diastolic),
    systolic: Math.round(firstReading.systolic - lastReading.systolic),
  }
}
