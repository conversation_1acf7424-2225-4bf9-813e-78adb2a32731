import { Typography, Paper } from '@mui/material'
import { styled } from '@mui/material/styles'
import moment from 'moment-timezone'

const PaperOutline = styled(Paper)(({ theme }) => ({
  width: '100%',
  padding: theme.spacing(2),
  ...theme.typography.body2,
  textAlign: 'left',
}))

// Function to decode HTML entities like &#x2F; back to /
const decodeHtmlEntities = (text) => {
  if (!text) return text

  // Use a more reliable approach that handles all common HTML entities
  return text
    .replace(/&#x2F;/g, '/')
    .replace(/&#x27;/g, "'")
    .replace(/&quot;/g, '"')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
}

export const ClinicalNote = ({ createdAt, providerName, note }) => {
  const momentObj = moment(createdAt)
  const name = providerName?.firstName + ' ' + providerName?.lastName
  const decodedNote = decodeHtmlEntities(note)

  return (
    <PaperOutline variant="elevation" sx={{ mt: 2 }}>
      <Typography variant="subtitle2">
        {momentObj.format('MMM D, YYYY; h:mma')}
      </Typography>
      <Typography variant="subtitle2">{name}</Typography>
      <Typography
        component="div"
        sx={{
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          mt: 1,
        }}
      >
        <strong>Note:</strong> {decodedNote}
      </Typography>
    </PaperOutline>
  )
}
