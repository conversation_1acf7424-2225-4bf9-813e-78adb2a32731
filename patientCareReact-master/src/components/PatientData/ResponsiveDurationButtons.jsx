import { Typography, ToggleButton, Box, Select, MenuItem } from '@mui/material'
import { useTheme, styled } from '@mui/material/styles'
import { StyledToggleButtonGroup } from './ToggleButtonGroup'
import { useState } from 'react'
import { ReactComponent as ArrowIcon } from '../../images/arrow-down-s-line.svg'

const Month = 1
const ThreeMonths = 2
const SixMonths = 6
const Year = 12
const TwoYears = 24
const All = 20

export const durationMap = {
  [Month]: '1 Month',
  [ThreeMonths]: '3 Months',
  [SixMonths]: '6 Months',
  [Year]: '1 Year',
  [TwoYears]: '2 Years',
  [All]: 'All Time',
}

const StyledSelect = styled(Select)(({ theme }) => ({
  display: 'flex',
  height: '40px',
  padding: '0px 8px',
  alignItems: 'center',
  gap: '8px',
  alignSelf: 'stretch',
  borderRadius: '4px',
  border: '1px solid #3E50B5',
  background: '#FFF',
  marginTop: '25px',
  '& .MuiSelect-select': {
    color: '#3E50B5',
    fontSize: '14px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '100%',
    minHeight: 'unset !important',
    padding: '0 !important',
  },
  '& .MuiOutlinedInput-notchedOutline': {
    border: 'none',
  },
  '& .MuiSelect-icon': {
    color: '#101113',
    right: '8px',
  },
}))

export const useDurationDays = () => {
  const [durationText, setDurationText] = useState('Last 1 month')
  const [months, setMonths] = useState(1)
  return {
    durationText,
    months,
    setDurationText,
    setMonths,
  }
}

export const ResponsiveDurationButtons = ({
  days,
  setDurationText,
  setDays,
  isMobile,
}) => {
  const theme = useTheme()

  const handleChange = (_e, value) => {
    if (value) {
      const num = parseInt(value)
      setDays(num)
      setDurationText(durationMap[num])
    }
  }

  const handleSelect = (e) => {
    const num = parseInt(e.target.value)
    setDays(num)
    setDurationText(durationMap[num])
  }

  return (
    <Box>
      {isMobile ? (
        <StyledSelect
          value={days}
          onChange={handleSelect}
          fullWidth
          IconComponent={ArrowIcon}
        >
          <MenuItem value={Month}>1 MONTH</MenuItem>
          <MenuItem value={ThreeMonths}>3 MONTHS</MenuItem>
          <MenuItem value={SixMonths}>6 MONTHS</MenuItem>
          <MenuItem value={Year}>1 YEAR</MenuItem>
          <MenuItem value={TwoYears}>2 YEARS</MenuItem>
          <MenuItem value={All}>ALL</MenuItem>
        </StyledSelect>
      ) : (
        <StyledToggleButtonGroup
          color="primary"
          value={days}
          exclusive
          onChange={handleChange}
        >
          <ToggleButton value={Month}>1 Month</ToggleButton>
          <ToggleButton value={ThreeMonths}>3 Months</ToggleButton>
          <ToggleButton value={SixMonths}>6 Months</ToggleButton>
          <ToggleButton value={Year}>1 Year</ToggleButton>
          <ToggleButton value={TwoYears}>2 Years</ToggleButton>
          <ToggleButton value={All}>All</ToggleButton>
        </StyledToggleButtonGroup>
      )}
    </Box>
  )
}
