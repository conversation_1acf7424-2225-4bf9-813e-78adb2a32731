import { ToggleButtonGroup, toggleButtonGroupClasses } from '@mui/material'
import { styled } from '@mui/material/styles'

export const StyledToggleButtonGroup = styled(ToggleButtonGroup)(
  ({ theme }) => ({
    [theme.breakpoints.down('md')]: {
      width: '100%',
      display: 'flex',
      gap: '10px',
      '& .MuiToggleButton-root': {
        flex: 1,
      },
    },
    [`& .${toggleButtonGroupClasses.grouped}`]: {
      margin: theme.spacing(0.5),
      border: 0,
      borderRadius: theme.shape.borderRadius,
      [`&.${toggleButtonGroupClasses.disabled}`]: {
        border: 0,
      },
      [theme.breakpoints.down('md')]: {
        color: '#3F51B5',
        backgroundColor: '#FFFFFF',
        border: '1px solid #3F51B5',
        margin: 0,
        '&.Mui-selected': {
          backgroundColor: '#3F51B5',
          color: '#FFFFFF',
          border: '1px solid #3F51B5',
          '&:hover': {
            backgroundColor: '#303F9F',
          },
        },
        '&:hover': {
          backgroundColor: '#EEF0FB',
        },
      },
    },
    [`& .${toggleButtonGroupClasses.middleButton},& .${toggleButtonGroupClasses.lastButton}`]:
      {
        marginLeft: -1,
        borderLeft: '1px solid transparent',
        [theme.breakpoints.down('md')]: {
          marginLeft: 0,
          borderLeft: '1px solid #3F51B5',
        },
      },
  })
)
