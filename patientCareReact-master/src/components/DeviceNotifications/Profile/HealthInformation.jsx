import React, { useEffect, useState } from 'react'
import {
  Box,
  Typography,
  TextField,
  Switch,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
} from '@material-ui/core'
import MenuItem from '@material-ui/core/MenuItem'
import { AddMedicationIcon } from './Icons'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DesktopTimePicker } from '@mui/x-date-pickers/DesktopTimePicker'
import EditIcon from '@mui/icons-material/Edit'
import IosShareIcon from '@mui/icons-material/IosShare'
import DeleteIcon from '@mui/icons-material/Delete'
import Drawer from '@mui/material/Drawer'
import useMediaQuery from '@mui/material/useMediaQuery'
import { generatePdf } from '../../../reports/generatePdf'
import { fetchBloodPressureData } from '../../../services/bloodPressure'
import dayjs from 'dayjs'
import moment from 'moment-timezone'
import { getPatientByImei } from '../../../services/patient'
import { parseBpMeasures } from '../../PatientData/BloodPressure/bloodPressure'
import { CARDIOWELL } from '../../../common/manufacters'
import Picker from 'react-mobile-picker-scroll'

const HealthInformation = ({
  formData,
  errors,
  isSubmitting,
  handleInputChange,
  handleSaveChanges,
  classes,
  handleAddMedicationTime,
  handleRemoveMedicationTime,
}) => {
  const [showMedicationForm, setShowMedicationForm] = useState(false)
  const [selectedMedication, setSelectedMedication] = useState('')
  const [editingIndex, setEditingIndex] = useState(null)
  const [openDrawer, setOpenDrawer] = useState(false)
  const [selectedTime, setSelectedTime] = useState(null)
  const isMobile = useMediaQuery('(max-width:768px)')
  const drawerAnchor = isMobile ? 'bottom' : 'right'
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false)
  const [bpmData, setBpmData] = useState([])
  const [avgSys, setAvgSys] = useState(null)
  const [avgDia, setAvgDia] = useState(null)
  const [isPickerOpen, setIsPickerOpen] = useState(false)
  const [tempValue, setTempValue] = useState({ feet: '5 ft', inches: '8 ft' })
  const displayValue = `${tempValue.feet.replace(/\D/g, '')}' ${tempValue.inches.replace(/\D/g, '')}''`
  useEffect(() => {
    if (formData.height) {
      const totalInches = parseInt(formData.height) || 0
      const feet = Math.floor(totalInches / 12)
      const inches = totalInches % 12
      setTempValue({ feet: `${feet} ft`, inches: `${inches} in` })
    }
  }, [formData.height])

  useEffect(() => {
    const fetchData = async () => {
      if (!formData.imei) {
        return
      }

      try {
        const data = await fetchBloodPressureData(formData.imei)

        // Transform data
        const transformedData = data.map((reading) => {
          // Convert timestamp to milliseconds if needed
          const timestampMs =
            typeof reading.ts === 'number' && reading.ts < 10000000000
              ? reading.ts * 1000
              : reading.ts

          const momentDate = moment(timestampMs)

          return {
            id: reading._id,
            date: momentDate.format('MM/DD/YYYY'),
            time: momentDate.format('hh:mm A'),
            systolic: parseInt(reading.systolic) || 0,
            diastolic: parseInt(reading.diastolic) || 0,
            pulse: parseInt(reading.pulse) || 0,
            ts: timestampMs,
            deviceId: reading.deviceId,
            dataType: reading.dataType,
          }
        })

        // Sort by timestamp (newest first)
        const sortedData = transformedData.sort((a, b) => b.ts - a.ts)
        setBpmData(sortedData)

        // Calculate averages
        if (sortedData.length > 0) {
          const totalSys = sortedData.reduce(
            (sum, reading) => sum + (reading.systolic || 0),
            0
          )
          const totalDia = sortedData.reduce(
            (sum, reading) => sum + (reading.diastolic || 0),
            0
          )
          const avgSysValue = Math.round(totalSys / sortedData.length)
          const avgDiaValue = Math.round(totalDia / sortedData.length)

          setAvgSys(avgSysValue)
          setAvgDia(avgDiaValue)
        }
      } catch (error) {
        console.error('Error fetching blood pressure data:', error)
      }
    }

    fetchData()
  }, [formData.imei])

  const handleTimeChange = (newTime) => {
    setSelectedTime(newTime)
  }

  const handleMedicationSelect = (event) => {
    setSelectedMedication(event.target.value)
  }

  const handleAddMedication = () => {
    if (selectedTime && selectedMedication) {
      if (editingIndex !== null) {
        handleUpdateMedicationTime(
          editingIndex,
          selectedMedication,
          selectedTime
        )
        setEditingIndex(null)
      } else {
        handleAddMedicationTime(selectedMedication, selectedTime)
      }
      setOpenDrawer(false)
      setShowMedicationForm(false)
      setSelectedMedication('')
      setSelectedTime(null)
    }
  }

  const handleChange = (name, value) => {
    const numericValue = value.replace(/\D/g, '')

    setTempValue((prev) => {
      if (prev[name].replace(/\D/g, '') === numericValue) return prev

      return {
        ...prev,
        [name]: `${numericValue} ${name === 'feet' ? 'ft' : 'in'}`,
      }
    })
  }

  const handleCancel = () => {
    setIsPickerOpen(false)
  }

  const handleSave = () => {
    setIsPickerOpen(false)

    const feet = parseInt(tempValue.feet) || 0
    const inches = parseInt(tempValue.inches) || 0
    const totalInches = feet * 12 + inches

    handleInputChange({
      target: {
        name: 'height',
        value: totalInches.toString(),
      },
    })
  }

  const handleEditMedicationTime = (index, medication, time) => {
    setEditingIndex(index)
    setSelectedMedication(medication)
    setSelectedTime(dayjs(time, 'hh:mm A'))
    setOpenDrawer(true)
  }

  const handleUpdateMedicationTime = (index, medication, time) => {
    const timeString = time.format('hh:mm A')
    const updatedTimes = [...formData.medicationTime]
    updatedTimes[index] = { medication, time: timeString }

    const event = {
      target: {
        name: 'medicationTime',
        value: updatedTimes,
      },
    }
    handleInputChange(event)
    setSelectedTime(null)
  }

  const handleDrawerDelete = () => {
    if (editingIndex !== null) {
      handleRemoveMedicationTime(editingIndex)
      setEditingIndex(null)
      setOpenDrawer(false)
      setSelectedMedication('')
      setSelectedTime(null)
    }
  }

  const renderMedicationTimes = () => {
    if (!formData.medicationTime || formData.medicationTime.length === 0) {
      return null
    }

    return (
      <Box mt={2} style={{ width: '100%' }}>
        {formData.medicationTime.map((item, index) => {
          const medication =
            typeof item === 'object'
              ? item.medication
              : index < formData.hypertensionMedications.length
                ? formData.hypertensionMedications[index]
                : 'Medication'
          const time = typeof item === 'object' ? item.time : item

          return (
            <Box
              key={index}
              sx={{
                mb: 2,
                width: '100%',
              }}
            >
              <Box className={classes.medicationTimeContainer}>
                <Box className={classes.medicationBox}>
                  <TextField
                    fullWidth
                    name="hypertensionMedications"
                    onChange={handleInputChange}
                    variant="outlined"
                    label="Medications"
                    value={medication}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    InputProps={{
                      readOnly: true,
                      inputComponent: ({ ...props }) => (
                        <Box
                          component="div"
                          sx={{
                            backgroundColor: '#F1F1F1',
                            borderRadius: '16px',
                            padding: '4px 10px',
                            margin: '17px 12px',
                            display: 'inline-block',
                            fontSize: '14px',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            maxWidth: '150px',
                            width: '100%',
                            border: 'none',
                          }}
                          {...props}
                        >
                          {medication}
                        </Box>
                      ),
                    }}
                  />
                </Box>
                <Box className={classes.SelectTimeBox}>
                  <Box
                    sx={{
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'space-between',
                    }}
                  >
                    <TextField
                      fullWidth
                      name="time"
                      onChange={handleInputChange}
                      variant="outlined"
                      label="Select time"
                      value={time}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      InputProps={{
                        readOnly: true,
                        sx: {
                          height: '70px',
                          padding: '16px 14px',
                        },
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          width: '100%',
                          height: '70px',
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            fontSize: '14px',
                            fontWeight: 400,
                            color: 'rgba(0, 0, 0, 0.87)',
                            width: '100%',
                            margin: '17px 12px',
                            height: '100px',
                            maxHeight: '100%',
                          }}
                        >
                          {time ? dayjs(time).format('hh:mm A') : '08:00 AM'}
                        </Typography>
                      </Box>
                    </TextField>
                  </Box>
                  <Box
                    sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}
                  >
                    <IconButton
                      size="small"
                      onClick={() =>
                        handleEditMedicationTime(index, medication, time)
                      }
                      sx={{
                        padding: 0.3,
                        minWidth: 'auto',
                        margin: '24px 12px',
                        marginRight: '4px',
                      }}
                    >
                      <EditIcon
                        sx={{ fontSize: 26, color: 'rgba(0, 0, 0, 0.54)' }}
                      />
                    </IconButton>
                    <Typography
                      style={{
                        color: 'rgba(67, 67, 67, 0.52)',
                        fontSize: '20px',
                        fontWeight: 400,
                      }}
                    >
                      |
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={() => handleRemoveMedicationTime(index)}
                      sx={{
                        padding: 0.3,
                        minWidth: 'auto',
                      }}
                    >
                      <DeleteIcon
                        sx={{ fontSize: 26, color: 'rgba(0, 0, 0, 0.54)' }}
                      />
                    </IconButton>
                  </Box>
                </Box>
              </Box>
            </Box>
          )
        })}
      </Box>
    )
  }

  const drawerContent = (
    <Box
      sx={{
        p: 3,
        width: isMobile ? '100%' : 400,
        maxWidth: '100vw',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: '#fff',
      }}
    >
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        mb={3}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: 500,
            fontSize: '20px',
            lineHeight: '32px',
            color: 'rgba(0, 0, 0, 0.87)',
          }}
        >
          Taken medication time
        </Typography>
        <IconButton
          onClick={() => {
            setOpenDrawer(false)
            setEditingIndex(null)
            setSelectedMedication('')
            setSelectedTime(null)
          }}
          style={{ padding: '0px !important' }}
        >
          <span
            style={{
              fontSize: 24,
              fontWeight: 700,
              padding: '0px !important',
              color: 'rgba(0, 0, 0, 0.54)',
            }}
          >
            &times;
          </span>
        </IconButton>
      </Box>
      <Box sx={{ marginBottom: '30px' }}>
        <TextField
          select
          fullWidth
          variant="outlined"
          label="Medications"
          value={selectedMedication}
          onChange={handleMedicationSelect}
          className={
            selectedMedication
              ? `${classes.input} ${classes.inputSelected}`
              : classes.input
          }
          InputLabelProps={{ shrink: true }}
        >
          <MenuItem value="" disabled>
            Select Medication
          </MenuItem>
          {Array.isArray(formData.hypertensionMedications) &&
            formData.hypertensionMedications.map((medication) => (
              <MenuItem key={medication} value={medication}>
                {medication}
              </MenuItem>
            ))}
        </TextField>
      </Box>

      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DesktopTimePicker
          label="Select time"
          value={selectedTime}
          onChange={handleTimeChange}
          ampm={true}
          slotProps={{
            textField: {
              variant: 'outlined',
              fullWidth: true,
              sx: { mb: 3 },
            },
          }}
        />
      </LocalizationProvider>
      <Box flexGrow={1} />
      <Box
        style={{
          display: 'flex',
          flexDirection: isMobile ? 'column-reverse' : 'row',
          gap: '16px',
          padding: '0px 10px',
          justifyContent: isMobile ? 'stretch' : 'center',
        }}
      >
        <Button
          variant="outlined"
          color="secondary"
          onClick={handleDrawerDelete}
          disabled={editingIndex === null}
          style={{
            Width: 'fit-content',
            padding: '15px 35px',
            borderColor: '#9C27B0',
            color: '#9C27B0',
          }}
        >
          DELETE
        </Button>
        <Button
          variant="contained"
          color="primary"
          disabled={!selectedTime || !selectedMedication}
          onClick={handleAddMedication}
          style={{
            Width: 'fit-content',
            padding: '15px 35px',
            bgcolor: '#3F51B5',
          }}
        >
          SAVE TIME
        </Button>
      </Box>
    </Box>
  )

  const handleGeneratePdf = async () => {
    const providerId = sessionStorage.getItem('providerID')
    try {
      setIsGeneratingPdf(true)

      if (!formData.imei) {
        console.error('No IMEI provided')
        return
      }

      // Get patient data
      const patientResponse = await getPatientByImei(formData.imei)

      if (!patientResponse?.patient?.id) {
        console.error('No patient ID found')
        return
      }

      // Get detailed patient data
      const response = await fetch('/routes/users/getPatientData', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: patientResponse.patient.id, providerId }),
      })

      if (!response.ok) {
        throw new Error('Failed to fetch patient data')
      }

      const data = await response.json()

      if (!data?.data) {
        console.error('Invalid data format received:', data)
        return
      }

      const now = moment()
      const startDate = now.clone().subtract(365, 'days').startOf('day')
      const endDate = now.clone().endOf('day')

      const {
        arrayBP,
        bpmTableArray,
        avgSys,
        avgDia,
        highSys,
        highDia,
        lowSys,
        lowDia,
      } = parseBpMeasures({
        bpm: data.data.bpm || [],
        btMessagesBpm: data.data.btMessagesBpm || [],
        ttBpm: data.data.ttBpm || [],
        adBpm: data.data.adBpm || [],
        withingsBpm: data.data.withingsBpm || [],
        bpDevice: data.data.selectedBpDevice || CARDIOWELL,
        timeframe: startDate.toDate(),
        endDate: endDate.toDate(),
        timeZone: data.data.timeZone || moment.tz.guess(),
      })

      const pdfData = {
        firstName: data.data.firstName || formData.firstName,
        lastName: data.data.lastName || formData.lastName,
        imei: formData.imei,
        manufacturer:
          data.data.selectedBpDevice || formData.manufacturer || 'Transtek',
        days: 365,
        bpmData: bpmTableArray,
        avgSys: avgSys,
        avgDia: avgDia,
      }

      await generatePdf(pdfData)
    } catch (error) {
      console.error('Error generating health information PDF:', error)
    } finally {
      setIsGeneratingPdf(false)
    }
  }

  return (
    <Box className={classes.formContainer}>
      <Typography className={classes.sectionHeader}>
        Health Information
      </Typography>

      <Box className={classes.formInnerContainer}>
        <div className={classes.fieldGroup}>
          <TextField
            fullWidth
            variant="outlined"
            label="Weight, Lbs"
            placeholder="Enter your weight"
            value={formData.weight}
            name="weight"
            onChange={handleInputChange}
            className={classes.input}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors.weight}
            helperText={errors.weight || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.weight ? 'auto' : '0',
                opacity: errors.weight ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          />
        </div>

        <div className={classes.fieldGroup}>
          <TextField
            fullWidth
            variant="outlined"
            label="Height"
            placeholder="Select your height"
            value={displayValue}
            name="height"
            onClick={() => setIsPickerOpen(true)}
            className={classes.input}
            onChange={handleInputChange}
            InputLabelProps={{ shrink: true }}
            error={!!errors.height}
            helperText={errors.height || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.height ? 'auto' : '0',
                opacity: errors.height ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
            InputProps={{
              readOnly: true,
            }}
          />

          <Dialog open={isPickerOpen} onClose={handleCancel}>
            <DialogTitle>Select Height</DialogTitle>
            <DialogContent>
              <DialogContent>
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height={200}
                  padding="0 40px"
                  style={{
                    width: 240,
                    overflowY: 'auto',
                    cursor: 'drag',
                    userSelect: 'none',
                    scrollbarWidth: 'thin',
                    msOverflowStyle: 'auto',
                  }}
                  onMouseDown={(e) =>
                    (e.currentTarget.style.cursor = 'grabbing')
                  }
                  onMouseUp={(e) => (e.currentTarget.style.cursor = 'grab')}
                >
                  <Picker
                    valueGroups={{
                      feet: tempValue.feet,
                      inches: tempValue.inches,
                    }}
                    optionGroups={{
                      feet: Array.from({ length: 8 }, (_, i) => `${i + 3} ft`),
                      inches: Array.from({ length: 12 }, (_, i) => `${i} in`),
                    }}
                    onChange={handleChange}
                    height={200}
                    itemHeight={36}
                    wheelMode="natural"
                    style={{ width: 120 }}
                  />
                </Box>
              </DialogContent>

              <Box display="flex" justifyContent="flex-end" marginTop={2}>
                <Button onClick={handleCancel}>Cancel</Button>
                <Button
                  onClick={handleSave}
                  color="primary"
                  variant="contained"
                  style={{ marginLeft: 8 }}
                >
                  Save
                </Button>
              </Box>
            </DialogContent>
          </Dialog>
        </div>

        <div className={classes.fieldGroup}>
          <TextField
            select
            fullWidth
            variant="outlined"
            label="Chronic Conditions"
            placeholder="Choose your Chronic Conditions"
            value={formData.chronicConditions}
            name="chronicConditions"
            onChange={handleInputChange}
            className={classes.multiSelectInput}
            InputLabelProps={{
              shrink: true,
            }}
            SelectProps={{
              multiple: true,
              displayEmpty: true,
              MenuProps: {
                getContentAnchorEl: null,
                anchorOrigin: {
                  vertical: 'bottom',
                  horizontal: 'left',
                },
                transformOrigin: {
                  vertical: 'top',
                  horizontal: 'left',
                },
                PaperProps: {
                  style: {
                    maxHeight: 300,
                    width: 'auto',
                  },
                },
                MenuListProps: {
                  disablePadding: true,
                },
                anchorReference: 'anchorEl',
                marginThreshold: 0,
              },
              renderValue: (selected) => {
                if (!selected?.length) {
                  return 'Choose your Chronic Conditions'
                }
                return (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Box
                        key={value}
                        sx={{
                          backgroundColor: '#F1F1F1',
                          borderRadius: '16px',
                          padding: '4px 10px',
                          margin: '2px',
                          display: 'inline-block',
                          fontSize: '14px',
                        }}
                      >
                        {value}
                      </Box>
                    ))}
                  </Box>
                )
              },
            }}
            error={!!errors.chronicConditions}
            helperText={errors.chronicConditions || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.chronicConditions ? 'auto' : '0',
                opacity: errors.chronicConditions ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          >
            <MenuItem value="" disabled>
              Choose your Chronic Conditions
            </MenuItem>
            {Array.isArray(formData.chronicConditionsOptions) &&
              formData.chronicConditionsOptions.map((condition) => (
                <MenuItem key={condition} value={condition}>
                  {condition}
                </MenuItem>
              ))}
          </TextField>
        </div>

        <div className={classes.fieldGroup}>
          <TextField
            select
            fullWidth
            variant="outlined"
            label="Allergies"
            placeholder="Choose your Allergies"
            value={formData.allergies}
            name="allergies"
            onChange={handleInputChange}
            className={classes.multiSelectInput}
            InputLabelProps={{
              shrink: true,
            }}
            SelectProps={{
              multiple: true,
              displayEmpty: true,
              MenuProps: {
                getContentAnchorEl: null,
                anchorOrigin: {
                  vertical: 'bottom',
                  horizontal: 'left',
                },
                transformOrigin: {
                  vertical: 'top',
                  horizontal: 'left',
                },
                PaperProps: {
                  style: {
                    maxHeight: 300,
                    width: 'auto',
                  },
                },
                MenuListProps: {
                  disablePadding: true,
                },
                anchorReference: 'anchorEl',
                marginThreshold: 0,
              },
              renderValue: (selected) => {
                if (!selected?.length) {
                  return 'Choose your Allergies'
                }
                return (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Box
                        key={value}
                        sx={{
                          backgroundColor: '#F1F1F1',
                          borderRadius: '16px',
                          padding: '4px 10px',
                          margin: '2px',
                          display: 'inline-block',
                          fontSize: '14px',
                        }}
                      >
                        {value}
                      </Box>
                    ))}
                  </Box>
                )
              },
            }}
            error={!!errors.allergies}
            helperText={errors.allergies || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.allergies ? 'auto' : '0',
                opacity: errors.allergies ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          >
            <MenuItem value="" disabled>
              Choose your Allergies
            </MenuItem>
            {Array.isArray(formData.allergiesOptions) &&
              formData.allergiesOptions.map((allergen) => (
                <MenuItem key={allergen} value={allergen}>
                  {allergen}
                </MenuItem>
              ))}
          </TextField>
        </div>

        <div className={classes.fieldGroup}>
          <Box style={{ display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="body2"
              style={{ marginRight: '8px', color: 'rgba(0, 0, 0, 0.6)' }}
            >
              Pregnant
            </Typography>
            <Switch
              checked={formData.gender === 'Male' ? false : formData.isPregnant}
              onChange={(e) => {
                if (formData.gender === 'Male') {
                  handleInputChange({
                    target: {
                      name: 'isPregnant',
                      checked: false,
                    },
                  })
                } else {
                  handleInputChange({
                    target: {
                      name: 'isPregnant',
                      checked: e.target.checked,
                    },
                  })
                }
              }}
              color="primary"
              disabled={formData.gender === 'Male'}
              sx={{
                '&.Mui-disabled': {
                  color: 'rgba(0, 0, 0, 0.38)',
                },
              }}
            />
          </Box>
        </div>

        <div className={classes.fieldGroup}></div>

        <div className={classes.fieldGroup}>
          <TextField
            select
            fullWidth
            variant="outlined"
            label="Medications"
            placeholder="Choose your Blood Pressure Medications"
            value={formData.hypertensionMedications}
            name="hypertensionMedications"
            onChange={handleInputChange}
            className={classes.multiSelectInput}
            InputLabelProps={{
              shrink: true,
            }}
            SelectProps={{
              multiple: true,
              displayEmpty: true,
              MenuProps: {
                getContentAnchorEl: null,
                anchorOrigin: {
                  vertical: 'bottom',
                  horizontal: 'left',
                },
                transformOrigin: {
                  vertical: 'top',
                  horizontal: 'left',
                },
                PaperProps: {
                  style: {
                    maxHeight: 300,
                    width: 'auto',
                    maxWidth: '100%',
                  },
                },
                MenuListProps: {
                  disablePadding: true,
                },
                anchorReference: 'anchorEl',
                marginThreshold: 0,
              },
              renderValue: (selected) => {
                if (!selected?.length) {
                  return 'Choose your Blood Pressure Medications'
                }
                return (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Box
                        key={value}
                        sx={{
                          backgroundColor: '#F1F1F1',
                          borderRadius: '16px',
                          padding: '4px 10px',
                          margin: '2px',
                          display: 'inline-block',
                          fontSize: '14px',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          maxWidth: '150px',
                        }}
                      >
                        {value}
                      </Box>
                    ))}
                  </Box>
                )
              },
            }}
            error={!!errors.hypertensionMedications}
            helperText={errors.hypertensionMedications || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.hypertensionMedications ? 'auto' : '0',
                opacity: errors.hypertensionMedications ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                height: '70px',
              },
            }}
          >
            <MenuItem value="" disabled>
              Choose your Blood Pressure Medications
            </MenuItem>
            {Array.isArray(formData.medicationsOptions) &&
              formData.medicationsOptions.map((medication) => (
                <MenuItem
                  key={medication}
                  value={medication}
                  style={{
                    whiteSpace: 'normal',
                    wordBreak: 'break-word',
                  }}
                >
                  {medication}
                </MenuItem>
              ))}
          </TextField>
        </div>

        <div className={classes.fieldGroup}>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            style={{
              marginTop: '16px',
              marginBottom: '12px',
            }}
          >
            <Typography
              variant="body1"
              style={{
                color: 'rgba(0, 0, 0, 0.87)',
                fontSize: '16px',
                fontWeight: 400,
                lineHeight: '24px',
              }}
            >
              Add time for taking medication
            </Typography>
            <IconButton
              color="primary"
              aria-label="add medication time"
              onClick={() => {
                setOpenDrawer(true)
                setEditingIndex(null)
                setSelectedMedication('')
                setSelectedTime(null)
              }}
              style={{
                backgroundColor: '#EFF3FC',
                width: '40px',
                height: '40px',
                padding: '8px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: '50%',
              }}
            >
              <AddMedicationIcon style={{ color: '#3F51B5' }} />
            </IconButton>
          </Box>
        </div>
      </Box>

      {renderMedicationTimes()}

      <Drawer
        anchor={drawerAnchor}
        open={openDrawer}
        onClose={() => {
          setOpenDrawer(false)
          setEditingIndex(null)
          setSelectedMedication('')
          setSelectedTime(null)
        }}
        transitionDuration={400}
        PaperProps={{
          sx: isMobile
            ? {
                width: '100vw',
                height: '80vh',
                maxHeight: '80vh',
                borderRadius: '16px 16px 0 0',
                margin: '0 auto',
                overflowY: 'auto',
              }
            : {
                width: 400,
                maxWidth: '100vw',
                height: '100vh',
                overflowY: 'auto',
              },
        }}
      >
        {drawerContent}
      </Drawer>

      <Box className={classes.ButtonContent}>
        <Button
          fullWidth
          variant="contained"
          className={classes.shareButton}
          onClick={handleGeneratePdf}
          disableRipple
          disableFocusRipple
          disabled={isSubmitting || isGeneratingPdf}
          endIcon={<IosShareIcon style={{ fontSize: 24 }} />}
        >
          {isGeneratingPdf ? 'GENERATING REPORT...' : 'SHARE REPORT'}
        </Button>
        <Button
          fullWidth
          variant="contained"
          className={classes.saveButton}
          onClick={handleSaveChanges}
          disableRipple
          disableFocusRipple
          disabled={isSubmitting}
        >
          {isSubmitting ? 'SAVING...' : 'SAVE CHANGES'}
        </Button>
      </Box>
    </Box>
  )
}

export default HealthInformation
