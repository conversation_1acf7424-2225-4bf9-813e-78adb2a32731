import { useEffect, useState, useRef } from 'react'
import {
  Box,
  CssBaseline,
  AppBar,
  Typography,
  Toolbar,
  IconButton,
  Button,
} from '@material-ui/core'
import { useTheme } from '@mui/material/styles'
import { getDeviceMeasures } from '../getDeviceMeasures'
import { useResponseState } from '../../../common/useResponsiveState'
import { Avatar, Tab, Tabs, Modal } from '@mui/material'
import { history } from '../../../App'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import { Loader } from '../../common/Loader/Loader'
import profileIcon from '../../../images/icons/profile-icon.svg'
import CloseIcon from '@mui/icons-material/Close'
import { chronicConditions } from '../../../patient/formOptions/chronicConditions'
import { bloodPressureMedications } from '../../../patient/formOptions/bloodPressureMedications'
import { allergens } from '../../../patient/formOptions/allergens'
import { useMediaQuery } from '@mui/material'
import { useProfileStyles } from './profileStyles'
import { SuccessCheckIcon } from './Icons'
import PersonalInformation from './PersonalInformation'
import HealthInformation from './HealthInformation'
import MyDevices from './MyDevices'
import Settings from './Settings'
import { Copyright } from '../../common/Copyright'
import { jsPDF } from 'jspdf'
import { getPatientByImei } from '../../../services/patient'

const ErrorDisplay = ({ message }) => {
  const classes = useProfileStyles()
  return (
    <Box className={classes.centered}>
      <Typography>{message}</Typography>
    </Box>
  )
}

const LoadingDisplay = () => {
  const classes = useProfileStyles()
  return (
    <Box className={classes.centered}>
      <Loader size={80} />
    </Box>
  )
}

export const Profile = (props) => {
  const imei = props.match.params.imei
  const urlSection = props.match.params.section || props.section
  const classes = useProfileStyles()
  const theme = useTheme()
  const { smallMobile } = useResponseState()
  const isDesktop = useMediaQuery('(min-width:768px)')
  const isMobile = useMediaQuery('(max-width:767px)')

  const validateField = (name, value) => {
    let error = ''

    switch (name) {
      case 'firstName':
        if (!value) {
          error = 'First name is required'
        } else if (/\d/.test(value)) {
          error = 'First name cannot contain numbers'
        }
        break

      case 'lastName':
        if (!value) {
          error = 'Last name is required'
        } else if (/\d/.test(value)) {
          error = 'Last name cannot contain numbers'
        }
        break

      case 'cellNumber':
        if (!value) {
          error = 'Phone number is required'
        } else if (!value.startsWith('+1')) {
          error = 'Phone number must start with +1'
        } else if (!/^\+1[0-9\s-()]{10,}$/.test(value)) {
          error = 'Please enter a valid phone number'
        }
        break

      case 'dateOfBirth':
        if (!value) {
          error = 'Date of birth cannot contain numbers'
        }
        break

      case 'gender':
        if (value && /\d/.test(value)) {
          error = 'Gender cannot contain numbers'
        }
        break

      case 'email':
        if (value && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value)) {
          error = 'Please enter a valid email address'
        }
        break

      case 'city':
        if (value && /\d/.test(value)) {
          error = 'City cannot contain numbers'
        }
        break

      case 'address':
        if (value && /^\d+$/.test(value)) {
          error = 'Address cannot consist of only numbers'
        }
        break

      case 'zip':
        if (!value) {
          error = 'Zip code is required'
        } else {
          error = ''
        }
        break

      case 'weight':
        if (value && !/^\d+$/.test(value)) {
          error = 'Please enter numbers only'
        }
        break

      case 'height':
        if (value && !/^\d+$/.test(value)) {
          error = 'Please enter numbers only'
        }
        break

      case 'chronicConditions':
        break

      case 'hypertensionMedications':
        break

      case 'allergies':
        break

      default:
        break
    }

    return error
  }

  const [display, setDisplay] = useState('loading')
  const [deviceData, setDeviceData] = useState({})
  const [serverResponse, setServerResponse] = useState('')
  const getSectionTabValue = (section) => {
    switch (section) {
      case 'health':
        return 1
      case 'devices':
        return 2
      case 'settings':
        return 3
      case 'personal':
      default:
        return 0
    }
  }

  const [tabValue, setTabValue] = useState(getSectionTabValue(urlSection))
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    gender: '',
    email: '',
    cellNumber: '',
    city: '',
    address: '',
    zip: '',
    weight: '',
    height: '',
    chronicConditionsOptions: chronicConditions,
    chronicConditions: [],
    medicationsOptions: bloodPressureMedications,
    hypertensionMedications: [],
    allergiesOptions: allergens,
    allergies: [],
    isPregnant: false,
    medicationTime: [],
    state: '',
    imei: imei,
    manufacturer: '',
    id: '',
  })

  const [errors, setErrors] = useState({})
  const [selectedTime, setSelectedTime] = useState(null)
  const [openSuccessModal, setOpenSuccessModal] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [open, setOpen] = useState(false)
  const dateInputRef = useRef(null)
  const [uploadedImageUrl, setUploadedImageUrl] = useState(null)
  const fileInputRef = useRef(null)
  const [openUploadModal, setOpenUploadModal] = useState(false)
  const [hasAvatar, setHasAvatar] = useState(false)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const savedAvatar = localStorage.getItem(`avatar_${imei}`)
        if (savedAvatar) {
          setUploadedImageUrl(savedAvatar)
          setHasAvatar(true)
        } else {
          setHasAvatar(false)
        }

        const response = await getPatientByImei(imei)

        if (response && response.patient) {
          const patientData = response.patient

          let medicationTime = []
          if (
            patientData.medicationTime &&
            Array.isArray(patientData.medicationTime)
          ) {
            medicationTime = patientData.medicationTime.map((item) => {
              let timeString =
                typeof item === 'object' && item.time ? item.time : item

              if (timeString && timeString.match(/^\d{2}:\d{2}$/)) {
                const [hours, minutes] = timeString.split(':')
                const date = new Date()
                date.setHours(parseInt(hours, 10))
                date.setMinutes(parseInt(minutes, 10))
                timeString = date.toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: true,
                })
              }

              return {
                medication: item.medication || 'Medication',
                time: timeString,
              }
            })
          }

          setFormData((prevData) => ({
            ...prevData,
            firstName: patientData.firstName || '',
            lastName: patientData.lastName || '',
            dateOfBirth: patientData.birthdate || '',
            gender: patientData.gender || '',
            email: patientData.email || '',
            cellNumber: patientData.cellNumber || '',
            city: patientData.city || '',
            address: patientData.address
              ? patientData.address.split(',')[0].trim()
              : '',
            zip: patientData.zip || '',
            state: patientData.state || '',
            imei: patientData.bpIMEI || imei,
            id: patientData._id || '',
            weight: patientData.weight || '',
            height: patientData.height || '',
            chronicConditionsOptions: chronicConditions,
            chronicConditions: patientData.chronicConditions || [],
            medicationsOptions: bloodPressureMedications,
            hypertensionMedications: patientData.hypertensionMedications || [],
            allergiesOptions: allergens,
            allergies: patientData.allergies || [],
            isPregnant: patientData.pregnant || false,
            medicationTime: medicationTime,
          }))
          setDisplay('data')
        } else {
          setDisplay('error')
          setServerResponse('No patient data found')
        }
      } catch (error) {
        console.error('Error fetching patient data:', error)
        setDisplay('error')
        setServerResponse(error.message || 'Error loading profile data')
      }
    }

    fetchData()
  }, [imei])

  useEffect(() => {
    setTabValue(getSectionTabValue(urlSection))
  }, [urlSection])

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue)

    let section = 'personal'
    switch (newValue) {
      case 1:
        section = 'health'
        break
      case 2:
        section = 'devices'
        break
      case 3:
        section = 'settings'
        break
      default:
        section = 'personal'
    }

    history.push(`/device-updates/dashboard/${imei}/profile/${section}`)
  }

  const handleInputChange = (e) => {
    const { name, value, checked } = e.target

    let updatedFormData
    if (
      name === 'chronicConditions' ||
      name === 'hypertensionMedications' ||
      name === 'allergies'
    ) {
      updatedFormData = {
        ...formData,
        [name]: value,
      }
    } else if (name === 'isPregnant') {
      updatedFormData = {
        ...formData,
        isPregnant: checked,
      }
    } else if (name === 'medicationTime') {
      updatedFormData = {
        ...formData,
        medicationTime: value,
      }
    } else if (name === 'address' && value && value.includes(',')) {
      const parts = value.split(',')
      const streetAddress = parts[0]?.trim() || ''
      const city = parts[1]?.trim() || ''
      const stateZip = parts[2]?.trim() || ''
      const zip = stateZip.match(/\d{5}/)?.[0] || ''
      const state = stateZip.replace(/\d{5}/, '').trim()

      updatedFormData = {
        ...formData,
        address: streetAddress,
        city: city,
        state: state,
        zip: zip,
      }
    } else {
      updatedFormData = {
        ...formData,
        [name]: value,
      }
    }

    setFormData(updatedFormData)

    if (name !== 'medicationTime') {
      const error = validateField(name, value)
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: error,
      }))
    }
  }

  const handleDateChange = (newValue) => {
    if (newValue) {
      const formattedDate = newValue.format('YYYY-MM-DD')
      const updatedFormData = {
        ...formData,
        dateOfBirth: formattedDate,
      }
      setFormData(updatedFormData)

      const error = validateField('dateOfBirth', formattedDate)
      setErrors((prevErrors) => ({
        ...prevErrors,
        dateOfBirth: error,
      }))
    }
  }

  const handleAddMedicationTime = (medication, time) => {
    if (time) {
      const timeString =
        typeof time === 'object' && time.$d
          ? time.$d.toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true,
            })
          : time
      const alreadyExists = formData.medicationTime.some((item) => {
        if (typeof item === 'object') {
          return item.medication === medication && item.time === timeString
        } else {
          return item === timeString
        }
      })

      if (!alreadyExists) {
        const newMedicationTime = {
          medication: medication,
          time: timeString,
        }

        const updatedMedicationTime = [
          ...formData.medicationTime,
          newMedicationTime,
        ]

        setFormData({
          ...formData,
          medicationTime: updatedMedicationTime,
        })
      }
    }
  }

  const handleRemoveMedicationTime = (index) => {
    const updatedTimes = [...formData.medicationTime]
    updatedTimes.splice(index, 1)
    setFormData({
      ...formData,
      medicationTime: updatedTimes,
    })
  }

  const handleCloseSuccessModal = () => {
    setOpenSuccessModal(false)
  }

  const handleSaveAvatar = async () => {
    if (!uploadedImageUrl) {
      return
    }

    setIsSubmitting(true)
    try {
      localStorage.setItem(`avatar_${imei}`, uploadedImageUrl)

      setOpenUploadModal(false)
      setOpenSuccessModal(true)
      setTimeout(() => {
        setOpenSuccessModal(false)
      }, 3000)
    } catch (error) {
      console.error('Error saving avatar:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const genereteReportPdf = () => {
    const {
      firstName,
      lastName,
      dateOfBirth,
      gender,
      weight,
      height,
      chronicConditions,
      hypertensionMedications,
      allergies,
      medicationTime,
    } = formData

    const pdf = new jsPDF()

    pdf.setFontSize(18)
    pdf.text('Health Information Report', 10, 10)

    pdf.setFontSize(12)
    pdf.text(`Name: ${firstName} ${lastName}`, 10, 20)
    pdf.text(`Date of Birth: ${dateOfBirth}`, 10, 30)
    pdf.text(`Gender: ${gender}`, 10, 40)
    pdf.text(`Weight: ${weight} lbs`, 10, 50)
    pdf.text(`Height: ${height} inches`, 10, 60)

    pdf.text('Chronic Conditions:', 10, 70)
    if (chronicConditions.length > 0) {
      chronicConditions.forEach((condition, index) => {
        pdf.text(`- ${condition}`, 15, 80 + index * 10)
      })
    } else {
      pdf.text('None', 15, 80)
    }

    const medicationsStartY = 80 + chronicConditions.length * 10 + 10
    pdf.text('Medications:', 10, medicationsStartY)
    if (hypertensionMedications.length > 0) {
      hypertensionMedications.forEach((medication, index) => {
        pdf.text(`- ${medication}`, 15, medicationsStartY + 10 + index * 10)
      })
    } else {
      pdf.text('None', 15, medicationsStartY + 10)
    }

    const allergiesStartY =
      medicationsStartY + hypertensionMedications.length * 10 + 20
    pdf.text('Allergies:', 10, allergiesStartY)
    if (allergies.length > 0) {
      allergies.forEach((allergy, index) => {
        pdf.text(`- ${allergy}`, 15, allergiesStartY + 10 + index * 10)
      })
    } else {
      pdf.text('None', 15, allergiesStartY + 10)
    }

    const medicationTimesStartY = allergiesStartY + allergies.length * 10 + 20
    pdf.text('Medication Times:', 10, medicationTimesStartY)
    if (medicationTime && medicationTime.length > 0) {
      medicationTime.forEach((item, index) => {
        const medication = item.medication || 'Medication'
        const time = item.time || 'Time not set'
        pdf.text(
          `- ${medication}: ${time}`,
          15,
          medicationTimesStartY + 10 + index * 10
        )
      })
    } else {
      pdf.text('None', 15, medicationTimesStartY + 10)
    }

    pdf.save('Health_Information_Report.pdf')
  }

  const handleSaveChanges = async () => {
    const newErrors = {}

    if (tabValue === 0) {
      if (!formData.firstName) {
        newErrors.firstName = 'First name is required'
      } else if (/\d/.test(formData.firstName)) {
        newErrors.firstName = 'First name cannot contain numbers'
      }

      if (!formData.lastName) {
        newErrors.lastName = 'Last name is required'
      } else if (/\d/.test(formData.lastName)) {
        newErrors.lastName = 'Last name cannot contain numbers'
      }

      if (!formData.cellNumber) {
        newErrors.cellNumber = 'Phone number is required'
      } else if (!/^\+?[0-9\s-()]{10,}$/.test(formData.cellNumber)) {
        newErrors.cellNumber = 'Please enter a valid phone number'
      }

      if (
        formData.email &&
        !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)
      ) {
        newErrors.email = 'Please enter a valid email address'
      }

      if (formData.city && /\d/.test(formData.city)) {
        newErrors.city = 'City cannot contain numbers'
      }

      if (formData.address && /^\d+$/.test(formData.address)) {
        newErrors.address = 'Address cannot consist of only numbers'
      }

      if (formData.zip && !/^\d{5}(-\d{4})?$/.test(formData.zip)) {
        newErrors.zip =
          'Please enter a valid zip code (e.g., 12345 or 12345-6789)'
      }
    } else if (tabValue === 1) {
      // Health Information tab validation - all fields are optional
      if (formData.weight && !/^\d+$/.test(formData.weight)) {
        newErrors.weight = 'Please enter numbers only'
      }

      if (formData.height && !/^\d+$/.test(formData.height)) {
        newErrors.height = 'Please enter numbers only'
      }
    }

    setErrors(newErrors)

    if (Object.keys(newErrors).length === 0) {
      setIsSubmitting(true)

      try {
        // Create a new object with all the data we want to send
        const dataToSubmit = {
          ...formData,
          id: formData.id,
          deviceImei: imei,
          firstName: formData.firstName,
          lastName: formData.lastName,
          birthdate: formData.dateOfBirth,
          gender: formData.gender,
          email: formData.email,
          address: formData.address.split(',')[0].trim(),
          city: formData.city,
          state: formData.state,
          zip: formData.zip,
        }
        if (formData.cellNumber && formData.cellNumber !== '+1') {
          dataToSubmit.cellNumber = formData.cellNumber
        } else {
          delete dataToSubmit.cellNumber
        }

        const response = await fetch(
          '/routes/users/providerSavePatientChanges',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(dataToSubmit),
          }
        )

        if (!response.ok) {
          throw new Error('Error')
        }

        const responseData = await response.json()

        // Update form data with the response data
        const updatedFormData = {
          ...formData,
          address: responseData.address || formData.address,
          city: responseData.city || formData.city,
          state: responseData.state || formData.state,
          zip: responseData.zip || formData.zip,
          firstName: responseData.firstName || formData.firstName,
          lastName: responseData.lastName || formData.lastName,
          birthdate: responseData.birthdate || formData.dateOfBirth,
          gender: responseData.gender || formData.gender,
          email: responseData.email || formData.email,
          cellNumber: responseData.cellNumber || formData.cellNumber,
        }

        setFormData(updatedFormData)

        setOpenSuccessModal(true)

        if (tabValue === 0) {
          setTimeout(() => {
            setOpenSuccessModal(false)
            setTabValue(1)
          }, 2000)
        } else {
          setTimeout(() => {
            setOpenSuccessModal(false)
          }, 3000)
        }
      } catch (error) {
        console.error('Error saving changes:', error)
        console.log('Error, try again')
      } finally {
        setIsSubmitting(false)
      }
    }
  }

  const handleDatePickerOpen = () => {
    setOpen(true)

    setTimeout(() => {
      const datePicker = document.querySelector('.MuiPickersPopper-root')
      const dateInput =
        dateInputRef.current.querySelector('input').parentElement.parentElement

      if (datePicker && dateInput) {
        const inputWidth = dateInput.offsetWidth

        datePicker.style.width = `${inputWidth}px`
        datePicker.style.maxWidth = `${inputWidth}px`

        const paperElement = datePicker.querySelector('.MuiPaper-root')
        if (paperElement) {
          paperElement.style.width = '100%'
          paperElement.style.maxWidth = '100%'
        }

        const selectors = [
          '.MuiPickersLayout-root',
          '.MuiDateCalendar-root',
          '.MuiYearCalendar-root',
          '.MuiMonthCalendar-root',
          '.MuiDayCalendar-header',
          '.MuiDayCalendar-weekContainer',
        ]

        selectors.forEach((selector) => {
          const elements = datePicker.querySelectorAll(selector)
          elements.forEach((element) => {
            element.style.width = '100%'
            element.style.maxWidth = '100%'
          })
        })
      }
    }, 50)
  }

  const handleImageClick = () => {
    setOpenUploadModal(true)
  }

  const handleImageChange = (event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setUploadedImageUrl(reader.result)
        console.log(
          'Image selected, uploadedImageUrl set:',
          reader.result ? 'Data URL present' : 'No Data URL'
        )
        console.log(
          'Value set to uploadedImageUrl:',
          uploadedImageUrl ? 'Data URL string' : 'null/undefined'
        )
      }
      reader.readAsDataURL(file)
    }
  }

  const handleCloseUploadModal = () => {
    setOpenUploadModal(false)
  }

  if (display === 'loading') {
    return <LoadingDisplay />
  }

  if (display === 'error') {
    return <ErrorDisplay message={serverResponse} />
  }

  return (
    <Box className={classes.root}>
      <CssBaseline />
      <AppBar position="static" className={classes.appBar}>
        <Toolbar>
          <Box className={classes.backButton} onClick={() => history.goBack()}>
            <ArrowBackIcon style={{ fontSize: '18px' }} />
            <Typography
              style={{
                fontFamily: 'Roboto',
                fontSize: '13px',
                fontWeight: 500,
                lineHeight: '22px',
                letterSpacing: '0.46px',
                textTransform: 'uppercase',
              }}
            >
              BACK
            </Typography>
          </Box>
        </Toolbar>
      </AppBar>

      <Box className={classes.profileHeader}>
        <Box
          className={classes.avatarContainer}
          onClick={handleImageClick}
          style={{ cursor: 'pointer' }}
        >
          {uploadedImageUrl ? (
            <img
              src={uploadedImageUrl}
              alt="Profile"
              style={{
                width: isDesktop ? '138px' : '48px',
                height: isDesktop ? '138px' : '48px',
                borderRadius: '50%',
                objectFit: 'cover',
              }}
            />
          ) : (
            <>
              <Avatar
                style={{
                  width: isDesktop ? '138px' : '48px',
                  height: isDesktop ? '138px' : '48px',
                  backgroundColor: '#BDBDBD',
                }}
              />
              <Box className={classes.profileIcon}>
                <img
                  src={profileIcon}
                  alt="Profile"
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                />
              </Box>
            </>
          )}
        </Box>
        <Box className={classes.profileInfo}>
          <Typography className={classes.profileName}>
            {formData.firstName} {formData.lastName}
          </Typography>
          <Typography className={classes.profileDevice}>
            BP device IMEI {imei}
          </Typography>
        </Box>
      </Box>

      <Box className={classes.tabsContainer}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          className={classes.tabs}
          variant="scrollable"
          scrollButtons="auto"
          indicatorColor="primary"
          TabIndicatorProps={{ style: { height: 2, background: '#1976D2' } }}
          sx={{
            width: '100%',
            border: 'none',
            borderBottom: 'none',
            height: '40px',
            minHeight: '40px',
            '& .MuiTabs-indicator': {
              backgroundColor: '#1976D2',
              height: '2px',
            },
            '& .MuiTabs-flexContainer': {
              borderBottom: 'none',
              paddingLeft: isDesktop ? '0' : '16px',
              height: '40px',
              minHeight: '40px',
              alignItems: 'center',
            },
            '& .MuiTabs-scrollButtons': {
              display: 'none',
            },
            overflowX: 'hidden',
            position: 'relative',
            '&::before': {
              content: 'none',
            },
          }}
        >
          <Tab
            label="Personal Information"
            className={classes.tab}
            style={{
              color: tabValue === 0 ? '#1976D2' : 'rgba(0, 0, 0, 0.6)',
              fontFamily: 'Roboto',
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: tabValue === 0 ? 500 : 400,
              lineHeight: '157%',
              letterSpacing: '0.1px',
              textTransform: 'none',
              minHeight: '40px',
              height: '40px',
              minWidth: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            disableRipple
            disableFocusRipple
          />
          <Tab
            label="My Health Information"
            className={classes.tab}
            style={{
              color: tabValue === 1 ? '#1976D2' : 'rgba(0, 0, 0, 0.6)',
              fontFamily: 'Roboto',
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: tabValue === 1 ? 500 : 400,
              lineHeight: '157%',
              letterSpacing: '0.1px',
              textTransform: 'none',
              minHeight: '40px',
              height: '40px',
              minWidth: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            disableRipple
            disableFocusRipple
          />
          <Tab
            label="My Devices"
            className={classes.tab}
            style={{
              color: tabValue === 2 ? '#1976D2' : 'rgba(0, 0, 0, 0.6)',
              fontFamily: 'Roboto',
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: tabValue === 2 ? 500 : 400,
              lineHeight: '157%',
              letterSpacing: '0.1px',
              textTransform: 'none',
              minHeight: '40px',
              height: '40px',
              minWidth: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            disableRipple
            disableFocusRipple
          />
          <Tab
            label="Settings"
            className={classes.tab}
            style={{
              color: tabValue === 3 ? '#1976D2' : 'rgba(0, 0, 0, 0.6)',
              fontFamily: 'Roboto',
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: tabValue === 3 ? 500 : 400,
              lineHeight: '157%',
              letterSpacing: '0.1px',
              textTransform: 'none',
              minHeight: '40px',
              height: '40px',
              minWidth: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            disableRipple
            disableFocusRipple
          />
        </Tabs>
      </Box>

      {tabValue === 0 && (
        <PersonalInformation
          formData={formData}
          errors={errors}
          isSubmitting={isSubmitting}
          handleInputChange={handleInputChange}
          handleDateChange={handleDateChange}
          handleSaveChanges={handleSaveChanges}
          classes={classes}
          dateInputRef={dateInputRef}
          open={open}
          handleDatePickerOpen={handleDatePickerOpen}
          setOpen={setOpen}
        />
      )}

      {tabValue === 1 && (
        <HealthInformation
          formData={formData}
          errors={errors}
          isSubmitting={isSubmitting}
          handleInputChange={handleInputChange}
          genereteReportPdf={genereteReportPdf}
          handleSaveChanges={handleSaveChanges}
          classes={classes}
          selectedTime={selectedTime}
          handleAddMedicationTime={handleAddMedicationTime}
          handleRemoveMedicationTime={handleRemoveMedicationTime}
        />
      )}

      {tabValue === 2 && (
        <MyDevices deviceData={deviceData} imei={imei} classes={classes} />
      )}

      {tabValue === 3 && (
        <Settings classes={classes} isSubmitting={isSubmitting} imei={imei} />
      )}

      <Copyright />

      <Modal
        open={openSuccessModal}
        onClose={handleCloseSuccessModal}
        aria-labelledby="success-modal"
        disableAutoFocus={true}
        disableEnforceFocus={true}
        disablePortal={false}
        disableScrollLock={true}
        keepMounted
        hideBackdrop
        sx={{
          position: 'absolute',
          backgroundColor: 'transparent',
          boxShadow: 'none',
          pointerEvents: 'none',
        }}
        slotProps={{
          backdrop: {
            sx: {
              backgroundColor: 'transparent',
            },
          },
        }}
      >
        <Box
          sx={{
            position: 'fixed',
            bottom: '16px',
            left: '50%',
            transform: 'translateX(-50%)',
            width: '328px',
            bgcolor: '#e8f5e9',
            borderRadius: '4px',
            padding: '8px 12px',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            boxShadow: '0px 3px 5px rgba(0, 0, 0, 0.2)',
            pointerEvents: 'auto',
            zIndex: 1300,
          }}
        >
          <SuccessCheckIcon />
          <Typography
            sx={{
              color: '#1e4620',
              fontSize: '14px',
              fontWeight: 400,
              lineHeight: 1.5,
              fontFamily: 'Roboto',
              flexGrow: 1,
            }}
          >
            Your data has been saved.
          </Typography>
          <IconButton
            onClick={handleCloseSuccessModal}
            sx={{
              color: '#1e4620',
              padding: '2px',
              marginLeft: 'auto',
              pointerEvents: 'auto',
            }}
          >
            <CloseIcon style={{ width: '16px', height: '16px' }} />
          </IconButton>
        </Box>
      </Modal>

      <Modal
        open={openUploadModal}
        onClose={handleCloseUploadModal}
        aria-labelledby="upload-photo-modal"
      >
        <Box
          sx={{
            position: 'absolute',
            top: isMobile ? 'auto' : '0%',
            bottom: isMobile ? '0' : 'auto',
            right: '0',
            height: isMobile ? '70%' : '100%',
            width: isMobile ? '100%' : 350,
            bgcolor: 'background.paper',
            border: 'none',
            boxShadow: 24,
            p: isMobile ? 2 : 4,
            borderTopLeftRadius: isMobile ? '16px' : '0',
            borderTopRightRadius: isMobile ? '16px' : '0',
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Typography id="upload-photo-modal" variant="h6" component="h2">
              Upload photo
            </Typography>
            <IconButton
              aria-label="close"
              onClick={handleCloseUploadModal}
              sx={{ p: 0 }}
            >
              <CloseIcon />
            </IconButton>
          </Box>

          <Box
            sx={{
              width: isMobile ? 150 : 100,
              height: isMobile ? 150 : 100,
              bgcolor: '#e0e0e0',
              borderRadius: '4px',
              margin: '20px auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              textAlign: 'center',
              flexShrink: 0,
            }}
            onClick={() => fileInputRef.current.click()}
          >
            {uploadedImageUrl ? (
              <img
                src={uploadedImageUrl}
                alt="Preview"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  borderRadius: '4px',
                }}
              />
            ) : (
              <Avatar
                style={{
                  width: '100%',
                  height: '100%',
                  backgroundColor: 'transparent',
                }}
              >
                <img
                  src={profileIcon}
                  alt="Profile"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                />
              </Avatar>
            )}
          </Box>

          <Typography
            variant="body1"
            style={{
              textAlign: 'center',
              fontWeight: '600',
              mt: 2,
              width: '100%',
              margin: '0 auto !important',
            }}
          >
            EDIT IMAGE
          </Typography>

          <Typography
            variant="body2"
            style={{
              textAlign: 'center',
              px: 2,
              mb: 8,
              color: 'text.secondary',
              width: '100%',
              margin: '0 auto',
              maxWidth: '380px',
              maxHeight: '50px',
            }}
          >
            Upload a profile photo in JPEG, PNG or GIF format, with a size no
            more than 1000×1000 pixels, no more than 1 MB
          </Typography>

          <Box
            sx={{
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              p: 2,
            }}
          >
            <Button
              variant="contained"
              color="primary"
              onClick={handleSaveAvatar}
              disabled={isSubmitting || !uploadedImageUrl}
              style={{
                width: isMobile ? '100%' : '150px',
                margin: isMobile ? '0 auto' : '0',
                display: 'block',
                backgroundColor: '#3F51B5',
                position: isMobile ? 'static' : 'absolute',
                bottom: isMobile ? 'auto' : '16px',
                right: isMobile ? 'auto' : '16px',
              }}
            >
              SAVE IMAGE
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleImageChange}
              accept="image/*"
              style={{ display: 'none' }}
            />
          </Box>
        </Box>
      </Modal>
    </Box>
  )
}
