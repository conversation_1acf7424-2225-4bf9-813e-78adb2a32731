import React, { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  TextField,
  Button,
  InputAdornment,
  IconButton,
  FormControl,
  InputLabel,
} from '@material-ui/core'
import { LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import dayjs from 'dayjs'
import MenuItem from '@material-ui/core/MenuItem'
import CalendarTodayIcon from '@mui/icons-material/CalendarToday'
import Autocomplete from 'react-google-autocomplete'

const PersonalInformation = ({
  formData,
  errors,
  isSubmitting,
  handleInputChange,
  handleDateChange,
  handleSaveChanges,
  classes,
  dateInputRef,
  open,
  handleDatePickerOpen,
  setOpen,
}) => {
  const [selectedCountryCode, setSelectedCountryCode] = useState('')
  const [localAddress, setLocalAddress] = useState('')
  const [localCity, setLocalCity] = useState('')
  const [localState, setLocalState] = useState('')
  const [localZip, setLocalZip] = useState('')

  // Initialize local states only once when component mounts
  useEffect(() => {
    if (formData.address) setLocalAddress(formData.address)
    if (formData.city) setLocalCity(formData.city)
    if (formData.state) setLocalState(formData.state)
    if (formData.zip) setLocalZip(formData.zip)
  }, []) // Remove formData dependency to prevent re-initialization

  const handleAddressChange = (place) => {
    if (place.formatted_address) {
      let city = ''
      let state = ''
      let countryCode = ''
      let zipCode = ''

      // Extract all information first
      if (place.address_components) {
        for (const component of place.address_components) {
          if (component.types.includes('locality')) {
            city = component.long_name
          }
          if (component.types.includes('administrative_area_level_1')) {
            state = component.long_name
          }
          if (component.types.includes('country')) {
            countryCode = component.long_name
          }
          if (component.types.includes('postal_code')) {
            zipCode = component.long_name
          }
        }
      }

      // Update local states
      const streetAddress = place.formatted_address.split(',')[0].trim()

      setLocalAddress(streetAddress)
      if (city) setLocalCity(city)
      if (state) setLocalState(state)
      if (zipCode) setLocalZip(zipCode)
      if (countryCode) setSelectedCountryCode(countryCode)

      handleInputChange({
        target: {
          name: 'address',
          value: streetAddress,
        },
      })

      // Update state separately to ensure it uses the long name
      if (state) {
        handleInputChange({
          target: {
            name: 'state',
            value: state,
          },
        })
      }
    }
  }

  const handleFieldChange = (e) => {
    const { name, value } = e.target

    // Update local state
    switch (name) {
      case 'address':
        setLocalAddress(value)
        break
      case 'city':
        setLocalCity(value)
        break
      case 'state':
        setLocalState(value)
        break
      case 'zip':
        setLocalZip(value)
        break
      default:
        break
    }

    // Update form data immediately
    handleInputChange(e)
  }

  const handleSave = async () => {
    const updates = {
      address: localAddress,
      city: localCity,
      state: localState,
      zip: localZip,
    }

    Object.entries(updates).forEach(([field, value]) => {
      handleInputChange({
        target: {
          name: field,
          value: value,
        },
      })
    })

    await handleSaveChanges()
  }

  return (
    <Box className={classes.formContainer}>
      <Typography className={classes.sectionHeader}>
        Personal Information
      </Typography>

      <Box className={classes.formInnerContainer}>
        <div className={classes.fieldGroup}>
          <TextField
            fullWidth
            variant="outlined"
            label="First name"
            value={formData.firstName}
            name="firstName"
            onChange={handleInputChange}
            placeholder="Enter your first name..."
            className={classes.input}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors.firstName}
            helperText={errors.firstName || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.firstName ? 'auto' : '0',
                opacity: errors.firstName ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          />
        </div>

        <div className={classes.fieldGroup}>
          <TextField
            fullWidth
            variant="outlined"
            label="Last name"
            value={formData.lastName}
            name="lastName"
            onChange={handleInputChange}
            placeholder="Enter your last name..."
            className={classes.input}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors.lastName}
            helperText={errors.lastName || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.lastName ? 'auto' : '0',
                opacity: errors.lastName ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          />
        </div>

        <div className={classes.fieldGroup}>
          <div className={classes.datePickerWrapper} ref={dateInputRef}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                label="Date of birth"
                open={open}
                onOpen={handleDatePickerOpen}
                onClose={() => setOpen(false)}
                value={
                  formData.dateOfBirth ? dayjs(formData.dateOfBirth) : null
                }
                format="MM/DD/YYYY"
                disableFuture
                onChange={handleDateChange}
                slots={{ textField: TextField }}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    fullWidth: true,
                    placeholder: 'MM/DD/YYYY',
                    className: classes.dateInput,
                    InputLabelProps: { shrink: true },
                    error: !!errors.dateOfBirth,
                    helperText: errors.dateOfBirth || ' ',
                    FormHelperTextProps: {
                      style: {
                        margin: '8px 0 0 0',
                        height: errors.dateOfBirth ? 'auto' : '0',
                        opacity: errors.dateOfBirth ? 1 : 0,
                        overflow: 'hidden',
                        position: 'static',
                      },
                    },
                    InputProps: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            edge="end"
                            onClick={() => handleDatePickerOpen()}
                            sx={{
                              '&:focus': {
                                outline: 'none',
                              },
                            }}
                          >
                            <CalendarTodayIcon
                              className={classes.calendarIcon}
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                    },
                  },
                }}
                PopperProps={{
                  placement: 'bottom-start',
                  disablePortal: true,
                  modifiers: [
                    {
                      name: 'preventOverflow',
                      enabled: true,
                      options: {
                        altAxis: true,
                        altBoundary: true,
                        tether: false,
                        rootBoundary: 'document',
                        padding: 8,
                      },
                    },
                  ],
                  style: {
                    width: '100%',
                    maxWidth: '100%',
                  },
                }}
                sx={{
                  width: '100%',
                }}
                showDaysOutsideCurrentMonth
                desktopModeMediaQuery="@media (min-width: 0px)"
              />
            </LocalizationProvider>
          </div>
        </div>

        <div className={classes.fieldGroup}>
          <TextField
            select
            fullWidth
            variant="outlined"
            label="Gender"
            value={formData.gender}
            name="gender"
            onChange={handleInputChange}
            className={classes.input}
            InputLabelProps={{
              shrink: true,
            }}
            SelectProps={{
              displayEmpty: true,
              renderValue: (selected) => {
                if (!selected) {
                  return (
                    <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                      Your gender...
                    </span>
                  )
                }
                return selected
              },
            }}
            error={!!errors.gender}
            helperText={errors.gender || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.gender ? 'auto' : '0',
                opacity: errors.gender ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          >
            <MenuItem value="" disabled>
              Choose your gender...
            </MenuItem>
            <MenuItem value="Male">Male</MenuItem>
            <MenuItem value="Female">Female</MenuItem>
            <MenuItem value="Other">Other</MenuItem>
            <MenuItem value="Prefer not to say">Prefer not to say</MenuItem>
          </TextField>
        </div>

        <div className={classes.fieldGroup}>
          <TextField
            fullWidth
            variant="outlined"
            label="E-mail"
            placeholder="Enter your E-mail..."
            value={formData.email}
            name="email"
            onChange={handleInputChange}
            className={classes.input}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors.email}
            helperText={errors.email || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.email ? 'auto' : '0',
                opacity: errors.email ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          />
        </div>

        <div className={classes.fieldGroup}>
          <TextField
            fullWidth
            variant="outlined"
            label="Cell Phone Number"
            placeholder="Enter your phone number..."
            value={formData.cellNumber}
            name="cellNumber"
            onChange={(e) => {
              const value = e.target.value
              if (!value.startsWith('+1')) {
                handleInputChange({
                  target: {
                    name: 'cellNumber',
                    value: '+1' + value.replace('+', ''),
                  },
                })
              } else {
                handleInputChange(e)
              }
            }}
            onFocus={(e) => {
              if (!formData.cellNumber || formData.cellNumber === '') {
                handleInputChange({
                  target: {
                    name: 'cellNumber',
                    value: '+1',
                  },
                })
              }
            }}
            onBlur={() => {
              if (formData.cellNumber === '+1') {
                handleInputChange({ target: { name: 'cellNumber', value: '' } })
              }
            }}
            onKeyDown={(e) => {
              if (
                (e.key === 'Backspace' || e.key === 'Delete') &&
                formData.cellNumber === '+1'
              ) {
                e.preventDefault()
                handleInputChange({
                  target: { name: 'cellNumber', value: '' },
                })
              }
            }}
            className={classes.input}
            InputLabelProps={{
              shrink: true,
            }}
            error={!formData.cellNumber.startsWith('+1') || !!errors.cellNumber}
            helperText={
              (!formData.cellNumber.startsWith('+1')
                ? 'Phone number must start with +1'
                : errors.cellNumber) || ' '
            }
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.cellNumber ? 'auto' : '0',
                opacity: errors.cellNumber ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          />
        </div>

        <Typography className={classes.addressHeader}>
          Address information
        </Typography>

        <div className={classes.fieldGroup}>
          <TextField
            fullWidth
            variant="outlined"
            label="Address"
            placeholder="Enter your street, house and apartment..."
            value={localAddress}
            name="address"
            className={classes.input}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors.address}
            helperText={errors.address || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.address ? 'auto' : '0',
                opacity: errors.address ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
            InputProps={{
              inputComponent: () => (
                <Autocomplete
                  apiKey={process.env.REACT_APP_GOOGLE_MAPS_KEY}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                    outline: 'none',
                    fontSize: '16px',
                  }}
                  onPlaceSelected={handleAddressChange}
                  options={{
                    types: ['address'],
                    componentRestrictions: {
                      country: selectedCountryCode || [],
                    },
                    fields: ['formatted_address', 'address_components'],
                  }}
                  defaultValue={localAddress}
                />
              ),
            }}
          />
        </div>

        <div className={classes.fieldGroup}>
          <TextField
            fullWidth
            variant="outlined"
            label="City"
            placeholder="Enter your city..."
            value={localCity}
            name="city"
            onChange={handleFieldChange}
            className={classes.input}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors.city}
            helperText={errors.city || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.city ? 'auto' : '0',
                opacity: errors.city ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          />
        </div>

        <div className={classes.fieldGroup}>
          <TextField
            fullWidth
            variant="outlined"
            label="State"
            name="state"
            value={localState}
            placeholder="Enter your state..."
            onChange={handleFieldChange}
            className={classes.input}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors.state}
            helperText={errors.state || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.state ? 'auto' : '0',
                opacity: errors.state ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          />
        </div>

        <div className={classes.fieldGroup}>
          <TextField
            fullWidth
            variant="outlined"
            label="Zip code"
            placeholder="Enter your zip code..."
            value={localZip}
            name="zip"
            onChange={handleFieldChange}
            className={classes.input}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors.zip}
            helperText={errors.zip || ' '}
            FormHelperTextProps={{
              style: {
                margin: '8px 0 0 0',
                height: errors.zip ? 'auto' : '0',
                opacity: errors.zip ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          />
        </div>
      </Box>

      <Button
        fullWidth
        variant="contained"
        className={classes.saveButton}
        onClick={handleSave}
        disableRipple
        disableFocusRipple
        disabled={isSubmitting}
      >
        {isSubmitting ? 'SAVING...' : 'SAVE CHANGES'}
      </Button>
    </Box>
  )
}

export default PersonalInformation
