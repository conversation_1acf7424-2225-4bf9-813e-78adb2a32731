import { makeStyles } from '@material-ui/core/styles'

export const useProfileStyles = makeStyles((theme) => ({
  root: {
    backgroundColor: '#FFFFFF',
    minHeight: '100vh',
  },
  appBar: {
    backgroundColor: '#3F51B5',
    boxShadow: 'none',
    height: '72px',
    display: 'flex',
    justifyContent: 'center',
  },
  backButton: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    color: '#FFFFFF',
    cursor: 'pointer',
    textTransform: 'uppercase',
    fontFamily: 'Roboto',
    fontSize: '13px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '22px',
    letterSpacing: '0.46px',
  },
  profileHeader: {
    display: 'flex',
    alignItems: 'center',
    padding: '0 16px',
    margin: '24px 0',
    backgroundColor: 'white',
    '@media (min-width: 768px)': {
      maxWidth: '1128px',
      margin: '32px auto',
      padding: '0 24px',
    },
  },
  avatarContainer: {
    position: 'relative',
    marginRight: '16px',
    '@media (min-width: 768px)': {
      marginRight: '24px',
    },
  },
  profileInfo: {
    padding: '4px',
  },
  profileIcon: {
    position: 'absolute',
    right: -5,
    bottom: -5,
    width: '24px',
    height: '24px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    '@media (min-width: 768px)': {
      right: -8,
      bottom: -8,
      width: '48px',
      height: '48px',
    },
  },
  profileName: {
    fontSize: '20px',
    fontWeight: 500,
    color: 'rgba(0, 0, 0, 0.87)',
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    lineHeight: '160%',
    letterSpacing: '0.15px',
    '@media (min-width: 768px)': {
      fontSize: '24px',
      lineHeight: '133.4%',
      letterSpacing: '0',
    },
  },
  profileDevice: {
    fontSize: '14px',
    color: 'rgba(0, 0, 0, 0.6)',
    '@media (min-width: 768px)': {
      fontSize: '16px',
      lineHeight: '150%',
      letterSpacing: '0.15px',
      marginTop: '4px',
    },
  },
  tabsContainer: {
    borderBottom: '1px solid #E0E0E0',
    width: '100%',
    '@media (min-width: 768px)': {
      maxWidth: '1128px',
      margin: '0 auto',
      paddingLeft: '24px',
      paddingRight: '24px',
    },
  },
  tabs: {
    backgroundColor: 'white',
    borderBottom: 'none',
    zIndex: 1,
    position: 'relative',
    '& .MuiTabs-indicator': {
      backgroundColor: '#1976D2',
      height: '2px',
      zIndex: 2,
    },
    '& .MuiTab-root': {
      '&:focus': {
        outline: 'none',
      },
      '&.Mui-selected': {
        color: '#1976D2',
      },
    },
    '@media (min-width: 768px)': {
      paddingLeft: '0',
    },
  },
  tab: {
    textTransform: 'none',
    fontWeight: 400,
    fontSize: '14px',
    fontStyle: 'normal',
    lineHeight: '157%',
    letterSpacing: '0.1px',
    minWidth: 'auto',
    padding: '12px 16px',
    color: 'rgba(0, 0, 0, 0.6)',
    '&.Mui-selected': {
      color: '#1976D2',
      fontWeight: 500,
    },
    '&:focus': {
      outline: 'none',
    },
  },
  sectionTitle: {
    fontSize: '14px',
    fontWeight: 500,
    color: 'rgba(0, 0, 0, 0.87)',
    margin: '24px 0 16px 0',
  },
  formContainer: {
    display: 'flex',
    width: '100%',
    padding: '24px 16px 16px',
    flexDirection: 'column',
    alignItems: 'flex-start',
    [theme.breakpoints.up(768)]: {
      maxWidth: '1128px',
      margin: '0 auto',
      padding: '32px 24px 24px',
    },
  },
  formInnerContainer: {
    width: '100%',
    [theme.breakpoints.up(768)]: {
      display: 'grid',
      gridTemplateColumns: '1fr 1fr',
      columnGap: '24px',
    },
  },
  fieldGroup: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    minHeight: '56px',
    marginBottom: '24px',
    '& .MuiFormHelperText-root': {
      margin: '8px 0 0 0',
      fontSize: '12px',
      lineHeight: '1.66',
      letterSpacing: '0.03333em',
      textAlign: 'left',
      height: 'auto',
      transition: 'height 0.3s ease, opacity 0.3s ease',
    },
    '&:last-child': {
      marginBottom: 0,
    },
  },
  fieldLabel: {
    color: 'rgba(0, 0, 0, 0.6)',
    fontSize: '12px',
    fontFamily: 'Roboto',
    fontWeight: 400,
    lineHeight: '166%',
    letterSpacing: '0.4px',
  },
  inputSelected: {
    '& .MuiSelect-select': {
      backgroundColor: '#F1F1F1',
      borderRadius: '16px',
      padding: '4px 10px !important',
      margin: '2px',
      display: 'inline-block',
      fontSize: '14px',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      maxWidth: 'fit-content',
    },
  },
  input: {
    '& .MuiOutlinedInput-root': {
      borderRadius: '4px',
      backgroundColor: '#fff',
      height: '56px',
      minHeight: '56px',
      maxHeight: '56px',
      padding: '0 12px',
    },
    '& .MuiOutlinedInput-input': {
      padding: '16px 12px',
      fontSize: '16px',
      color: 'rgba(0, 0, 0, 0.87)',
      '&::placeholder': {
        color: 'rgba(0, 0, 0, 0.6)',
        opacity: 1,
      },
      height: '24px',
      minHeight: '24px',
    },
    '& .MuiSelect-select': {
      padding: '16px 14px',
      fontSize: '16px',
      color: 'rgba(0, 0, 0, 0.87)',
      height: '24px',
      minHeight: '24px',
    },
    '& .MuiInputLabel-outlined': {
      transform: 'translate(14px, -6px) scale(0.75)',
      color: 'rgba(0, 0, 0, 0.6)',
    },
    '& .MuiInputLabel-outlined.MuiInputLabel-shrink': {
      transform: 'translate(14px, -6px) scale(0.75)',
    },
    '& .MuiFormControl-root': {
      marginBottom: '20px !important',
      height: '56px',
    },
    '& .MuiFormLabel-root': {
      lineHeight: 'normal',
    },
    '& .Mui-error .MuiInputLabel-outlined': {
      color: '#f44336',
    },
    '& .MuiFormLabel-root.Mui-error': {
      color: '#f44336',
    },
    height: '56px',
  },
  medicationsInput: {
    width: '328px',
    '& .MuiOutlinedInput-root': {
      borderRadius: '4px',
      backgroundColor: '#fff',
      height: '80px',
      minHeight: '80px',
      maxHeight: '80px',
    },
    '& .MuiOutlinedInput-input': {
      padding: '16px 12px',
      fontSize: '16px',
      color: 'rgba(0, 0, 0, 0.87)',
      '&::placeholder': {
        color: 'rgba(0, 0, 0, 0.6)',
        opacity: 1,
      },
    },
    '& .MuiSelect-select': {
      padding: '16px 14px',
      fontSize: '16px',
      color: 'rgba(0, 0, 0, 0.87)',
      minHeight: '48px',
    },
    '& .MuiInputLabel-outlined': {
      transform: 'translate(14px, -6px) scale(0.75)',
      color: 'rgba(0, 0, 0, 0.6)',
    },
    '& .MuiInputLabel-outlined.MuiInputLabel-shrink': {
      transform: 'translate(14px, -6px) scale(0.75)',
    },
    '& .MuiFormControl-root': {
      margin: 0,
      height: '80px',
    },
    '& .MuiFormLabel-root': {
      lineHeight: 'normal',
    },
    '& .Mui-error .MuiInputLabel-outlined': {
      color: '#f44336',
    },
    '& .MuiFormLabel-root.Mui-error': {
      color: '#f44336',
    },
    height: '80px',
  },
  dateInput: {
    '& .MuiOutlinedInput-root': {
      '& input': {
        paddingRight: 0,
      },
    },
    '& .MuiOutlinedInput-input': {
      padding: '16px 0px 16px 12px',
    },
    '& .MuiInputAdornment-root': {
      marginLeft: 0,
    },
  },
  saveButton: {
    backgroundColor: '#3F51B5',
    color: '#FFF',
    textTransform: 'uppercase',
    padding: '0',
    height: '42px',
    borderRadius: '4px',
    fontFamily: 'Roboto',
    fontSize: '15px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '26px',
    letterSpacing: '0.46px',
    marginBottom: '4px',
    marginTop: '16px',
    fontFeatureSettings: "'liga' off, 'clig' off",
    '&:hover': {
      backgroundColor: '#303F9F',
    },
    boxShadow:
      '0px 1px 5px 0px rgba(0, 0, 0, 0.12), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 3px 1px -2px rgba(0, 0, 0, 0.20)',
    '&:focus': {
      outline: 'none',
    },
    '&.MuiButton-root': {
      '&:focus': {
        outline: 'none',
      },
    },
    [theme.breakpoints.up(768)]: {
      width: '200px',
      alignSelf: 'flex-end',
      margin: '0px',
    },
  },
  shareButton: {
    backgroundColor: '#ffffff',
    width: '100%',
    color: 'rgba(156, 39, 176, 1)',
    padding: '4px 30px',
    border: '1px solid rgba(156, 39, 176, 1)',
    height: '42px',
    borderRadius: '4px',
    fontFamily: 'Roboto',
    fontSize: '15px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '26px',
    letterSpacing: '0.46px',
    marginBottom: '4px',
    marginTop: '2px',
    [theme.breakpoints.up(768)]: {
      width: 'fit-content',
      margin: '0px',
    },
  },

  medicationTimeContainer: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    gap: '20px',
    alignItems: 'stretch',
    [theme.breakpoints.up(768)]: {
      flexDirection: 'row',
    },
  },

  medicationBox: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    borderRadius: '4px',
    height: '100%',
    [theme.breakpoints.up(768)]: {
      width: '50%',
    },
  },

  SelectTimeBox: {
    display: 'flex',
    width: '100%',
    gap: '14px',
    alignItems: 'center',
    justifyContent: 'space-between',
    [theme.breakpoints.up(768)]: {
      width: '50%',
    },
  },

  ButtonContent: {
    alignItems: 'center',
    marginTop: '24px',
    width: '100%',
    display: 'flex',
    flexDirection: 'column-reverse',
    gap: '10px',
    position: 'relative',
    justifyContent: 'right',
    [theme.breakpoints.up(768)]: {
      flexDirection: 'row',
      gap: '16px',
    },
  },
  flexRow: {
    display: 'flex',
    gap: '16px',
    [theme.breakpoints.up(768)]: {
      gap: '24px',
    },
  },
  flexItem: {
    flex: 1,
  },
  sectionHeader: {
    color: 'rgba(0, 0, 0, 0.87)',
    fontFamily: 'Roboto',
    fontSize: '20px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '160%',
    letterSpacing: '0.15px',
    margin: '0 0 24px 0',
    height: '48px',
    display: 'flex',
    alignItems: 'center',
    [theme.breakpoints.up(768)]: {
      fontSize: '24px',
      gridColumn: '1 / -1',
    },
  },
  addressHeader: {
    color: 'rgba(0, 0, 0, 0.87)',
    fontFamily: 'Roboto',
    fontSize: '20px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '160%',
    letterSpacing: '0.15px',
    margin: '0 0 24px 0',
    height: '48px',
    display: 'flex',
    alignItems: 'center',
    [theme.breakpoints.up(768)]: {
      fontSize: '24px',
      gridColumn: '1 / -1',
      marginTop: '24px',
    },
  },
  calendarIcon: {
    color: 'rgba(0, 0, 0, 0.54)',
    fontSize: '20px',
    cursor: 'pointer',
  },
  centered: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: 'calc(100vh - 72px)',
  },
  multiSelectInput: {
    '& .MuiOutlinedInput-root': {
      borderRadius: '4px',
      backgroundColor: '#fff',
      minHeight: '56px',
      height: 'auto',
    },
    '& .MuiOutlinedInput-input': {
      padding: '16px 12px',
      fontSize: '16px',
      color: 'rgba(0, 0, 0, 0.87)',
      '&::placeholder': {
        color: 'rgba(0, 0, 0, 0.6)',
        opacity: 1,
      },
      minHeight: '24px',
      height: 'auto',
    },
    '& .MuiSelect-select': {
      padding: '16px 14px',
      fontSize: '16px',
      color: 'rgba(0, 0, 0, 0.87)',
      minHeight: '24px',
      height: 'auto',
    },
    '& .MuiInputLabel-outlined': {
      transform: 'translate(14px, -6px) scale(0.75)',
      color: 'rgba(0, 0, 0, 0.6)',
    },
    '& .MuiInputLabel-outlined.MuiInputLabel-shrink': {
      transform: 'translate(14px, -6px) scale(0.75)',
    },
    '& .MuiFormControl-root': {
      margin: 0,
      minHeight: '56px',
      height: 'auto',
    },
    '& .MuiFormLabel-root': {
      lineHeight: 'normal',
    },
    '& .Mui-error .MuiInputLabel-outlined': {
      color: '#f44336',
    },
    '& .MuiFormLabel-root.Mui-error': {
      color: '#f44336',
    },
    minHeight: '56px',
    height: 'auto',
  },
  datePickerWrapper: {
    width: '100%',
    position: 'relative',
  },
  datePickerPopper: {
    '&.MuiPopper-root': {
      width: 'auto !important',
    },
    '& .MuiPaper-root': {
      width: '100% !important',
    },
  },
  table: {
    minWidth: 650,
    marginTop: '24px',
    '@media (max-width: 767px)': {
      minWidth: '100%',
    },
  },
  '& .MuiPaper-elevation1': {
    boxShadow: 'none !important',
  },
  tableHead: {
    backgroundColor: '#ffffff',
  },
  tableCell: {
    color: '#303F9F !important',
    fontWeight: 500,
    fontFamily: 'Roboto',
    fontSize: '13px',
    padding: '10px 16px',
    '@media (min-width: 768px)': {
      fontSize: '14px',
      padding: '12px 24px',
    },
  },
  tableBodyCell: {
    fontFamily: 'Roboto',
    fontSize: '14px',
    color: '#000000',
    padding: '10px 16px',
    '@media (min-width: 768px)': {
      fontSize: '16px',
      padding: '12px 24px',
    },
  },
  hoverText: {
    '&:hover': {
      transform: 'translateY(-20px)',
      cursor: 'pointer',
    },
  },
}))
