import React, { useState, useEffect } from 'react'
import { Box, Typography, Switch, Button } from '@material-ui/core'
import { getPatientByImei } from '../../../services/patient'

const Settings = ({ classes, isSubmitting, imei }) => {
  const [lastUpdated, setLastUpdated] = useState(null)

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        const response = await getPatientByImei(imei)
        setLastUpdated(response.patient?.lastUpdated || null)
      } catch (error) {
        console.error('Error fetching patient:', error)
      }
    }

    if (imei) {
      fetchPatientData()
    }
  }, [imei])

  const formattedDate = lastUpdated
    ? new Date(lastUpdated).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    : 'Not available'

  return (
    <Box
      className={classes.formContainer}
      style={{ padding: '24px 16px 32px' }}
    >
      <Typography
        style={{
          color: 'rgba(0, 0, 0, 0.87)',
          fontFamily: 'Roboto',
          fontSize: '16px',
          fontStyle: 'normal',
          fontWeight: 500,
          lineHeight: '24px',
          letterSpacing: '0.15px',
        }}
      >
        About Cardiowell
      </Typography>

      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          padding: '8px 0',
          gap: '8px',
          cursor: 'pointer',
        }}
        onClick={() =>
          window.open('https://www.cardiowell.com/term-of-use', '_blank')
        }
      >
        <Typography
          style={{
            color: 'rgba(0, 0, 0, 0.87)',
            fontSize: '14px',
            fontWeight: 400,
            lineHeight: '20px',
          }}
        >
          Terms of Use
        </Typography>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
        >
          <path
            d="M8.00033 2.66667L7.06033 3.60667L10.7803 7.33334H2.66699V8.66667H10.7803L7.06033 12.3933L8.00033 13.3333L13.3337 8L8.00033 2.66667Z"
            fill="black"
            fillOpacity="0.56"
          />
        </svg>
      </Box>

      <Box
        sx={{
          display: 'flex',
          gap: '8px',
          alignItems: 'center',
          width: '100%',
          padding: '8px 0',
          cursor: 'pointer',
        }}
        onClick={() =>
          window.open('https://www.cardiowell.com/privacy-policy/', '_blank')
        }
      >
        <Typography
          style={{
            color: 'rgba(0, 0, 0, 0.87)',
            fontSize: '14px',
            fontWeight: 400,
            lineHeight: '20px',
          }}
        >
          Privacy Policy
        </Typography>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
        >
          <path
            d="M8.00033 2.66667L7.06033 3.60667L10.7803 7.33334H2.66699V8.66667H10.7803L7.06033 12.3933L8.00033 13.3333L13.3337 8L8.00033 2.66667Z"
            fill="black"
            fillOpacity="0.56"
          />
        </svg>
      </Box>

      <Box
        sx={{
          display: 'flex',
          gap: '8px',
          alignItems: 'center',
          width: '100%',
          padding: '8px 0',
          marginBottom: '24px',
          cursor: 'pointer',
        }}
        onClick={() => window.open('mailto:<EMAIL>', '_blank')}
      >
        <Typography
          style={{
            color: 'rgba(0, 0, 0, 0.87)',
            fontSize: '14px',
            fontWeight: 400,
            lineHeight: '20px',
          }}
        >
          Contact Support
        </Typography>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
        >
          <path
            d="M8.00033 2.66667L7.06033 3.60667L10.7803 7.33334H2.66699V8.66667H10.7803L7.06033 12.3933L8.00033 13.3333L13.3337 8L8.00033 2.66667Z"
            fill="black"
            fillOpacity="0.56"
          />
        </svg>
      </Box>

      <Typography
        style={{
          color: 'rgba(0, 0, 0, 0.6)',
          fontSize: '12px',
          fontWeight: 400,
          lineHeight: '16px',
          marginBottom: '32px',
        }}
      >
        Last updated on {formattedDate}
      </Typography>
    </Box>
  )
}

export default Settings
