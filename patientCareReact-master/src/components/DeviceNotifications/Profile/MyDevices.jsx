import React, { useEffect, useState } from 'react'
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@material-ui/core'
import { getDevices } from '../../../device/getDevices'
import { getDeviceType } from '../../../device/utils/deviceTypes'
import { Loader } from '../../common/Loader/Loader'

const MyDevices = ({ classes }) => {
  const [devices, setDevices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [pageInfo, setPageInfo] = useState({
    page: 0,
    pageSize: 100,
    totalRowCount: 0,
  })

  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const devicesData = await getDevices({
          page: pageInfo.page,
          pageSize: pageInfo.pageSize,
        })
        if (!devicesData || !devicesData.devices) {
          throw new Error('Invalid devices data received')
        }
        setDevices(devicesData.devices)
        setPageInfo(devicesData.pageInfo)
      } catch (err) {
        setError(err.message || 'Failed to fetch devices')
      } finally {
        setLoading(false)
      }
    }
    fetchDevices()
  }, [pageInfo.page, pageInfo.pageSize])

  const getDeviceUnit = (device) => device.unit || 'N/A'

  if (loading) {
    return (
      <Box className={classes.centered}>
        <Loader size={80} />
      </Box>
    )
  }

  if (error) {
    return (
      <Box className={classes.centered}>
        <Typography color="error">{error}</Typography>
      </Box>
    )
  }

  if (!devices.length) {
    return (
      <Box className={classes.centered}>
        <Typography>No devices found for this patient</Typography>
      </Box>
    )
  }

  return (
    <Box className={classes.formContainer}>
      <TableContainer component={Paper} style={{ boxShadow: 'none' }}>
        <Table
          sx={{ minWidth: 650 }}
          aria-label="simple table"
          className={classes.table}
        >
          <TableHead className={classes.tableHead}>
            <TableRow>
              <TableCell className={classes.tableCell}>Device</TableCell>
              <TableCell align="left" className={classes.tableCell}>
                Start date
              </TableCell>
              <TableCell align="left" className={classes.tableCell}>
                IMEI
              </TableCell>
              <TableCell align="left" className={classes.tableCell}>
                Last measurement
              </TableCell>
              <TableCell align="left" className={classes.tableCell}>
                Detail
              </TableCell>
              <TableCell align="left" className={classes.tableCell}>
                Unit
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {devices.map((device) => (
              <TableRow
                key={device._id}
                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                className={classes.tableRow}
              >
                <TableCell
                  component="th"
                  scope="row"
                  className={classes.tableBodyCell}
                >
                  {getDeviceType(device)}
                </TableCell>
                <TableCell align="left" className={classes.tableBodyCell}>
                  {new Date(device.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                  })}
                </TableCell>
                <TableCell align="left" className={classes.tableBodyCell}>
                  {device.imei}
                </TableCell>
                <TableCell align="left" className={classes.tableBodyCell}>
                  {device.lastMeasurement
                    ? new Date(device.lastMeasurement).toLocaleString('en-US', {
                        year: '2-digit',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true,
                      })
                    : 'N/A'}
                </TableCell>
                <TableCell align="left" className={classes.tableBodyCell}>
                  {device.detail || 'N/A'}
                </TableCell>
                <TableCell align="left" className={classes.tableBodyCell}>
                  {getDeviceUnit(device)}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  )
}

export default MyDevices
