import moment from 'moment-timezone'
import { parseBpMeasures } from '../PatientData/BloodPressure/bloodPressure'
import { TRANSTEK, BODYTRACE } from '../../common/manufacters'

export const parseDeviceMeasures = (data, timeframe) => {
  const { bpm, bpmMessages, manufacturer, timeZone } = data
  const localTimeZone = moment.tz.guess()
  const timezone = timeZone && timeZone !== 'local' ? timeZone : localTimeZone

  const timeframeMoment = moment(timeframe)
  const now = moment()

  if (manufacturer === 'Transtek') {
    const { arrayBP, bpmTableArray, avgSys, avgDia, lastReading } =
      parseBpMeasures({
        ttBpm: bpm,
        bpDevice: TRANSTEK,
        timeframe,
        timeZone,
      })

    const filteredArrayBP = arrayBP.filter((reading) => {
      const readingDate = moment(reading.ts * 1000)
      const isWithinTimeframe = readingDate.isBetween(
        timeframeMoment,
        now,
        undefined,
        '[]'
      )
      return isWithinTimeframe
    })

    const filteredBpmTableArray = bpmTableArray.filter((reading) => {
      const readingDate = moment(reading.date)
      return readingDate.isBetween(timeframeMoment, now, undefined, '[]')
    })

    filteredArrayBP.sort((a, b) => b.ts - a.ts)
    filteredBpmTableArray.sort(
      (a, b) => moment(b.date).valueOf() - moment(a.date).valueOf()
    )

    let deviceInfo = {}
    if (bpm.length > 0) {
      const { imei, sn, iccid, dataType, modelNumber, deviceId } = bpm[0]
      deviceInfo = {
        imei,
        sn,
        iccid,
        dataType,
        modelNumber,
        deviceId,
      }
    }

    const stats = filteredArrayBP.reduce(
      (acc, curr) => {
        acc.totalSys += curr.systolic
        acc.totalDia += curr.diastolic
        acc.count += 1
        return acc
      },
      { totalSys: 0, totalDia: 0, count: 0 }
    )

    return {
      arrayBP: filteredArrayBP,
      bpmTableArray: filteredBpmTableArray,
      avgSys: stats.count > 0 ? Math.round(stats.totalSys / stats.count) : '',
      avgDia: stats.count > 0 ? Math.round(stats.totalDia / stats.count) : '',
      timeZone: timezone,
      deviceInfo,
      lastReading: filteredArrayBP[0] || {},
    }
  } else {
    const result = parseBpMeasures({
      bpm,
      btMessagesBpm: bpmMessages,
      bpDevice: BODYTRACE,
      timeframe,
      timeZone,
    })

    const filteredArrayBP = result.arrayBP
      .filter((reading) => {
        const readingDate = moment(reading.ts * 1000)
        const isWithinTimeframe = readingDate.isBetween(
          timeframeMoment,
          now,
          undefined,
          '[]'
        )
        return isWithinTimeframe
      })
      .sort((a, b) => b.ts - a.ts)

    const filteredBpmTableArray = result.bpmTableArray
      .filter((reading) => {
        const readingDate = moment(reading.date)
        return readingDate.isBetween(timeframeMoment, now, undefined, '[]')
      })
      .sort((a, b) => moment(b.date).valueOf() - moment(a.date).valueOf())

    const stats = filteredArrayBP.reduce(
      (acc, curr) => {
        acc.totalSys += curr.systolic
        acc.totalDia += curr.diastolic
        acc.count += 1
        return acc
      },
      { totalSys: 0, totalDia: 0, count: 0 }
    )

    return {
      ...result,
      arrayBP: filteredArrayBP,
      bpmTableArray: filteredBpmTableArray,
      avgSys: stats.count > 0 ? Math.round(stats.totalSys / stats.count) : '',
      avgDia: stats.count > 0 ? Math.round(stats.totalDia / stats.count) : '',
      timeZone: timezone,
      deviceInfo: {},
      lastReading: filteredArrayBP[0] || {},
    }
  }
}
