// External Libraries
import React from 'react'
import { Container, Box, Typography, Link } from '@mui/material'

// Assets
import cardiowelLogo from '../../../images/main-mobile-logo-cardiowell.svg'

// Internal Components
import { DeviceSignupForm } from './DeviceSignupForm'
import { Copyright } from '../../common/Copyright'

// Styles
import { useFormStyles } from '../../common/style'

export const DeviceRegisterForm = ({ imei, onSubmit, isPending }) => {
  const classes = useFormStyles()

  return (
    <Box>
      <img
        className={classes.image}
        src={cardiowelLogo}
        alt="cardiowell-logo"
        style={{
          display: 'block',
          margin: '0 auto',
          width: '160px',
        }}
      />
      <Typography
        variant="h5"
        align="center"
        sx={{
          marginTop: '31px',
          marginBottom: '8px',
          color: 'rgba(30, 30, 30, 1)',
          textAlign: 'center',
          fontSize: '20px',
          fontStyle: 'normal',
          fontWeight: 500,
          lineHeight: '160%',
          letterSpacing: '0.15px',
        }}
      >
        Device Registration
      </Typography>
      <Typography
        align="center"
        variant="body1"
        sx={{
          color: 'rgba(117, 117, 117, 1)',
          textAlign: 'center',
          fontSize: '14px',
          fontStyle: 'normal',
          fontWeight: 400,
          lineHeight: '143%',
          letterSpacing: '0.17px',
          marginBottom: '4px',
        }}
      >
        {`Receive updates from your blood pressure device.`}
      </Typography>
      <Typography
        align="center"
        variant="body1"
        sx={{
          color: 'rgba(117, 117, 117, 1)',
          textAlign: 'center',
          fontSize: '15px',
          fontStyle: 'normal',
          fontWeight: 400,
          lineHeight: '122%',
          letterSpacing: '-0.3px',
        }}
      >
        IMEI:{' '}
        <Box
          component="span"
          sx={{
            color: 'rgba(117, 117, 117, 1)',
            fontSize: '16px',
            fontStyle: 'normal',
            fontWeight: 700,
            lineHeight: '122%',
            letterSpacing: '0.2px',
          }}
        >
          {imei}
        </Box>
      </Typography>
      <DeviceSignupForm onSubmit={onSubmit} isPending={isPending} />
      <Typography
        align="center"
        variant="body1"
        sx={{
          color: 'rgba(0, 0, 0, 0.60)',
          textAlign: 'center',
          fontSize: '14px',
          fontStyle: 'normal',
          fontWeight: 400,
          lineHeight: '166%',
          letterSpacing: '0.4px',
          marginBottom: '4px',
        }}
      >
        {'If you have not received the SMS yet, contact\n'}
        {'us at '}
        <Link
          href="mailto:<EMAIL>"
          style={{ color: 'rgba(30, 136, 229, 1)' }}
        >
          <EMAIL>
        </Link>
        {' for assistance.'}
      </Typography>
      <Copyright />
    </Box>
  )
}
