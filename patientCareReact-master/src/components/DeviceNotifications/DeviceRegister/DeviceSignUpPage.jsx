// External Libraries
import { useState, useEffect } from 'react'
import { Container, CssBaseline } from '@mui/material'

// Styles
import { useFormStyles } from '../../common/style'

// Internal Components
import { ConfirmationPage } from './ConfirmationPage'
import { DeviceRegisterForm } from './DeviceRegisterForm'
import {
  NotificationModal,
  NOTIFICATION_TYPES,
} from '../../common/modals/NotificationModal'

// Services
import { getDeviceStatus } from '../getDeviceStatus'
import { registerDeviceUpdates } from '../registerDeviceUpdates'

// Utilities
import { history } from '../../../App'

const alreadyActivatedMessage =
  'Device has already been activated to receive notifications'

export const DeviceSignUp = (props) => {
  const imei = props.match.params.imei
  const classes = useFormStyles()
  const [showForm, setShowForm] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)

  //TODO: need to display somewhere serverMessage
  const [serverMessage, setServerMessage] = useState('')
  const [isPending, setIsPending] = useState(false)
  const [openErrorModal, setOpenErrorModal] = useState(false)

  const onSubmit = (formData) => {
    setServerMessage('')
    setIsPending(true)
    registerDeviceUpdates({ ...formData, imei })
      .then((data) => {
        if (data.error) {
          setServerMessage(data.message)
          setOpenErrorModal(true)
        } else {
          setShowForm(false)
          setServerMessage('')
          setShowConfirmation(true)
        }
      })
      .catch((error) => {
        setServerMessage(`An error occurred : ${error}`)
        setOpenErrorModal(true)
      })
      .finally(() => {
        setIsPending(false)
      })
  }

  const handleCloseErrorModal = () => {
    setOpenErrorModal(false)
  }

  useEffect(() => {
    getDeviceStatus(imei).then((data) => {
      if (data.activated) {
        setShowForm(false)
        setServerMessage(alreadyActivatedMessage)
        history.push(`/device-updates/dashboard/${imei}`)
      } else {
        setShowForm(true)
        setServerMessage('')
      }
    })
  }, [imei])

  return (
    <Container component="main" maxWidth="xs">
      <CssBaseline />
      <div
        className={classes.paper}
        style={{ marginTop: showConfirmation ? 0 : undefined }}
      >
        {showForm ? (
          <DeviceRegisterForm
            imei={imei}
            onSubmit={onSubmit}
            isPending={isPending}
          />
        ) : showConfirmation ? (
          <ConfirmationPage imei={imei} match={{ params: { imei: imei } }} />
        ) : null}

        <NotificationModal
          open={openErrorModal}
          onClose={handleCloseErrorModal}
          title="Your data was not saved."
          message="Please try again."
          type={NOTIFICATION_TYPES.ERROR}
          autoCloseTime={5000}
          position="bottom"
        />
      </div>
    </Container>
  )
}
