import {
  TextField,
  Button,
  Checkbox,
  FormHelperText,
  Link,
  Box,
  CircularProgress,
} from '@material-ui/core'
import { useFormStyles } from '../../common/style'
import { PhoneNumberRegex } from '../../../common/regex'
import { useForm, Controller } from 'react-hook-form'
import { TopAlignedFormControlLabel } from '../../common/TopAlignedFormControlLabel'

const consentVerbage =
  'I agree to receive blood pressure device related messages from Cardiowell at the phone number provided above. I understand I will receive one message every week, data rates may apply, reply STOP to opt out.'

export const DeviceSignupForm = ({ onSubmit, isPending }) => {
  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      cellNumber: '',
      firstName: '',
      lastName: '',
      smsConsent: false,
    },
  })
  const classes = useFormStyles()
  const values = watch()
  const digitsAfterPlusOne = (() => {
    if (!values.cellNumber) return ''
    const withoutPrefix = values.cellNumber.startsWith('+1')
      ? values.cellNumber.slice(2)
      : values.cellNumber
    return withoutPrefix.replace(/\D/g, '')
  })()
  const isFormValid =
    values.cellNumber &&
    values.firstName &&
    values.lastName &&
    values.smsConsent &&
    digitsAfterPlusOne.length === 10 &&
    !errors.cellNumber &&
    !errors.firstName &&
    !errors.lastName

  return (
    <form className={classes.form} noValidate onSubmit={handleSubmit(onSubmit)}>
      <Controller
        name="cellNumber"
        control={control}
        rules={{
          required: 'Phone number is required',
          validate: {
            startsWithPlusOne: (value) =>
              value === '' ||
              value.startsWith('+1') ||
              'Phone number must start with +1',
            digitsOnlyAfterPlus: (value) => {
              if (value === '' || value === '+1') return true
              const afterPlusOne = value.startsWith('+1')
                ? value.slice(2)
                : value
              return /^[0-9]*$/.test(afterPlusOne) || 'Only digits are allowed'
            },
            usLength: (value) => {
              if (value === '' || value === '+1') return true
              const afterPlusOne = value.startsWith('+1')
                ? value.slice(2)
                : value
              const len = afterPlusOne.replace(/\D/g, '').length
              return len <= 10 || `US number must be 10 digits (${len}/10)`
            },
            validFormat: (value) => {
              if (value === '' || value === '+1') return true
              const afterPlusOne = value.startsWith('+1')
                ? value.slice(2)
                : value
              const len = afterPlusOne.replace(/\D/g, '').length
              if (len < 10) return true
              return (
                PhoneNumberRegex.test(value) ||
                'US number must be +1 followed by 10 digits'
              )
            },
          },
        }}
        render={({ field: { name, value, onChange } }) => (
          <TextField
            className={classes.textField}
            variant="outlined"
            margin="normal"
            fullWidth
            id="phonenumber"
            label="Cell Phone Number"
            value={value}
            name={name}
            onChange={(e) => {
              const newValue = e.target.value
              if (!newValue.startsWith('+1')) {
                onChange({
                  target: { value: '+1' + newValue.replace('+', '') },
                })
              } else {
                onChange(e)
              }
            }}
            error={value !== '' && value !== '+1' && !!errors[name]}
            helperText={
              value !== '' && value !== '+1' ? errors[name]?.message : ''
            }
            InputLabelProps={{
              required: false,
            }}
            onFocus={(e) => {
              if (!value || value === '') {
                onChange({ target: { value: '+1' } })
              }
            }}
            onBlur={() => {
              if (value === '+1') {
                onChange({ target: { value: '' } })
              }
            }}
            FormHelperTextProps={{
              style: {
                margin:
                  errors[name] && value !== '' && value !== '+1'
                    ? '8px 0 0 0'
                    : 0,
                height:
                  errors[name] && value !== '' && value !== '+1' ? 'auto' : 0,
                opacity: errors[name] && value !== '' && value !== '+1' ? 1 : 0,
                overflow: 'hidden',
                position: 'static',
              },
            }}
          />
        )}
      />
      <Controller
        name="firstName"
        control={control}
        rules={{
          required: 'First name is required',
          pattern: {
            value: /^[A-Za-z]+$/,
            message: 'First name can only contain letters',
          },
          minLength: {
            value: 2,
            message: 'First name must be at least 2 characters',
          },
        }}
        render={({ field: { name, value, onChange } }) => (
          <TextField
            className={classes.textField}
            variant="outlined"
            margin="normal"
            fullWidth
            name={name}
            value={value}
            label="First Name"
            onChange={onChange}
            InputLabelProps={{
              required: false,
            }}
            error={!!errors.firstName}
            helperText={errors.firstName?.message}
          />
        )}
      />
      <Controller
        name="lastName"
        control={control}
        rules={{
          required: 'Last name is required',
          pattern: {
            value: /^[A-Za-z]+$/,
            message: 'Last name can only contain letters',
          },
          minLength: {
            value: 2,
            message: 'Last name must be at least 2 characters',
          },
        }}
        render={({ field: { name, value, onChange } }) => (
          <TextField
            className={classes.textField}
            variant="outlined"
            margin="normal"
            required
            fullWidth
            name={name}
            value={value}
            label="Last Name"
            onChange={onChange}
            InputLabelProps={{
              required: false,
            }}
            error={!!errors.lastName}
            helperText={errors.lastName?.message}
          />
        )}
      />
      <Controller
        name="smsConsent"
        control={control}
        rules={{
          required:
            'Please agree to receive SMS notifications as part of this service',
        }}
        render={({ field: { name, value, onChange } }) => (
          <TopAlignedFormControlLabel
            className={classes.formElement}
            name={name}
            value={value}
            control={
              <Checkbox
                color="primary"
                style={{ color: 'rgba(25, 118, 210, 1)' }}
              />
            }
            onChange={onChange}
            label={consentVerbage}
          />
        )}
      />
      <FormHelperText error={!!errors['smsConsent']}>
        {errors['smsConsent']?.message}
      </FormHelperText>
      <Box display="flex" justifyContent="center" alignItems="center">
        <Link
          target="_blank"
          href={'https://cardiowell.com/privacy-policy/'}
          underline="always"
          style={{ color: 'rgba(25, 118, 210, 1)' }}
        >
          Privacy Policy
        </Link>
        <Box
          component="span"
          sx={{
            width: '5px',
            height: '18px',
            color: 'rgba(0, 0, 0, 0.38)',
            display: 'flex',
            alignItems: 'center',
            margin: '0 8px',
          }}
        >
          |
        </Box>
        <Link
          target="_blank"
          href={'https://cardiowell.com/cardiowell-mobile-terms-of-service'}
          underline="always"
          style={{ color: 'rgba(25, 118, 210, 1)' }}
        >
          Mobile Terms of Service
        </Link>
      </Box>
      <Button
        fullWidth
        variant="contained"
        color="primary"
        type="submit"
        disabled={!isFormValid || isPending}
        className={classes.submit}
        style={{
          height: '42px',
          minHeight: '42px',
          fontSize: '15px',
          backgroundColor: isFormValid ? '#3F51B5' : 'rgba(0, 0, 0, 0.12)',
          color: isFormValid ? '#fff' : 'rgba(0, 0, 0, 0.38)',
          fontWeight: 500,
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            position: 'relative',
          }}
        >
          <div
            style={{
              position: 'absolute',
              left: 'calc(50% - 65px)',
              top: '50%',
              transform: 'translateY(-50%)',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {isPending && (
              <CircularProgress
                size={20}
                style={{
                  color: '#fff',
                }}
              />
            )}
          </div>
          Register
        </div>
      </Button>
    </form>
  )
}
