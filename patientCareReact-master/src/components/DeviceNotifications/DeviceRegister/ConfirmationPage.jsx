import { Container, CssBaseline, Box, Typography, Link } from '@mui/material'
import { Copyright } from '../../common/Copyright'
import successIcon from '../../../images/success-checkmark.svg'

export const ConfirmationPage = (props) => {
  const imei = props.match.params.imei

  return (
    <Container component="main" maxWidth="xs">
      <CssBaseline />
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          pb: 4,
        }}
      >
        <Box sx={{ flex: 1 }}>
          <Box sx={{ textAlign: 'center', mt: 7 }}>
            <Typography
              sx={{
                width: 328,
                color: 'rgba(0, 0, 0, 0.56)',
                textAlign: 'center',
                fontSize: '16px',
                fontStyle: 'normal',
                fontWeight: 500,
                lineHeight: '150%',
                letterSpacing: '0.15px',
              }}
            >
              IMEI your Cardiowell device:
            </Typography>
            <Typography
              sx={{
                width: 328,
                color: 'rgba(0, 0, 0, 0.56)',
                textAlign: 'center',
                fontSize: '16px',
                fontStyle: 'normal',
                fontWeight: 500,
                lineHeight: '150%',
                letterSpacing: '0.15px',
                mb: '112px',
              }}
            >
              {imei}
            </Typography>
          </Box>

          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <img
              src={successIcon}
              alt="Success"
              loading="lazy"
              width={40}
              height={40}
              style={{
                marginBottom: '24px',
              }}
            />
            <Box sx={{ textAlign: 'center', mb: 2 }}>
              <Typography
                variant="h5"
                sx={{
                  color: 'rgba(0, 0, 0, 0.87)',
                  fontSize: '24px',
                  fontStyle: 'normal',
                  fontWeight: 400,
                  lineHeight: '133.4%',
                }}
              >
                Device Registered.
              </Typography>
              <Typography
                variant="h5"
                sx={{
                  color: 'rgba(0, 0, 0, 0.87)',
                  fontSize: '24px',
                  fontStyle: 'normal',
                  fontWeight: 400,
                  lineHeight: '133.4%',
                }}
              >
                Congratulations on an
              </Typography>
              <Typography
                variant="h5"
                sx={{
                  color: 'rgba(0, 0, 0, 0.87)',
                  fontSize: '24px',
                  fontStyle: 'normal',
                  fontWeight: 400,
                  lineHeight: '133.4%',
                }}
              >
                important step towards
              </Typography>
              <Typography
                variant="h5"
                sx={{
                  color: 'rgba(0, 0, 0, 0.87)',
                  fontSize: '24px',
                  fontStyle: 'normal',
                  fontWeight: 400,
                  lineHeight: '133.4%',
                }}
              >
                better health!
              </Typography>
            </Box>
          </Box>

          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Typography
              sx={{
                color: 'rgba(0, 0, 0, 0.60)',
                textAlign: 'center',
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: 400,
                lineHeight: '175%',
                letterSpacing: '0.15px',
              }}
            >
              <span style={{ whiteSpace: 'nowrap' }}>
                We've sent an SMS with a secure link
              </span>
            </Typography>
            <Typography
              sx={{
                color: 'rgba(0, 0, 0, 0.60)',
                textAlign: 'center',
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: 400,
                lineHeight: '175%',
                letterSpacing: '0.15px',
              }}
            >
              to view your results at any time.
            </Typography>
          </Box>
        </Box>

        <Box sx={{ textAlign: 'center' }}>
          <Typography
            sx={{
              color: 'rgba(0, 0, 0, 0.60)',
              textAlign: 'center',
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: 400,
              lineHeight: '166%',
              letterSpacing: '0.4px',
            }}
          >
            If you haven't received the SMS,
          </Typography>
          <Typography
            sx={{
              color: 'rgba(0, 0, 0, 0.60)',
              textAlign: 'center',
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: 400,
              lineHeight: '166%',
              letterSpacing: '0.4px',
              mb: 4,
            }}
          >
            please contact us at{' '}
            <Link
              href="mailto:<EMAIL>"
              sx={{
                color: '#1E88E5',
                textDecoration: 'underline',
              }}
            >
              <EMAIL>
            </Link>
          </Typography>

          <Box mt={8}>
            <Copyright />
          </Box>
        </Box>
      </Box>
    </Container>
  )
}
