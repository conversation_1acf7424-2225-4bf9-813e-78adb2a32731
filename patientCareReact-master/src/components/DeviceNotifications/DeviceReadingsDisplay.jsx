import { Fragment, useState, useMemo, useEffect, useRef } from 'react'
import {
  Typo<PERSON>,
  Button,
  ToggleButton,
  Paper,
  Container,
  Box,
  Stack,
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import moment from 'moment-timezone'
import { useStyles } from '../common/style'
import {
  BloodPressureChart,
  HeartRateChart,
} from '../PatientData/BloodPressure/BloodPressureCharts'
import { BloodPressureGrid } from '../PatientData/BloodPressure/BloodPressureGrid'
import {
  ResponsiveDurationButtons,
  useDurationDays,
} from '../PatientData/ResponsiveDurationButtons'
import { parseDeviceMeasures } from './parseDeviceMeasures'
import { StyledToggleButtonGroup } from '../PatientData/ToggleButtonGroup'
import { ChartPaper } from '../PatientData/ChartPaper'
import { useResponseState } from '../../common/useResponsiveState'
import { fetchBloodPressureData } from '../../services/bloodPressure'
import { getDeviceMeasures } from './getDeviceMeasures'
import { InfoDropdown } from '../PatientData/Dropdown'
import { getPatientByImei } from '../../services/patient'
import { parseBpMeasures } from '../PatientData/BloodPressure/bloodPressure'
import { CARDIOWELL } from '../../common/manufacters'
import { getProgramThresholds } from '../../thresholds/program/getProgramThresholds'
import { getBpThresholdLevel } from '../../thresholds/threshold/getBpThresholdLevel'
import {
  getBloodPressureColorForLevel,
  getPulseColor,
} from '../../utils/thresholdColors'

const lastReadingDate = (ts, timeZone) => {
  const lastReading = moment(ts)
  if (!ts || !lastReading?.isValid()) {
    return ''
  }
  const timezone =
    timeZone && timeZone !== 'local' ? timeZone : moment.tz.guess()

  if (lastReading.isBefore(moment().subtract(3, 'd'))) {
    return lastReading.tz(timezone).format('MM/DD/YY')
  } else if (lastReading.isBefore(moment().subtract(2, 'd'))) {
    return 'Two Days Ago'
  } else if (lastReading.isBefore(moment().subtract(1, 'd'))) {
    return 'Yesterday'
  } else {
    return 'Today'
  }
}

export const DeviceReadingsDisplay = ({ imei, deviceData }) => {
  const theme = useTheme()
  const { isMobile } = useResponseState()
  const { months, durationText, setDurationText, setMonths } = useDurationDays()
  const [tableView, setTableView] = useState('Chart')
  const [fitView, setFitView] = useState(false)
  const [gridData, setGridData] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [patientData, setPatientData] = useState(null)
  const [thresholds, setThresholds] = useState({})
  const classes = useStyles()
  const bpChartRef = useRef(null)
  const hrChartRef = useRef(null)
  const providerId = sessionStorage.getItem('providerID')

  const timeframe = useMemo(() => {
    const now = moment()
    const tf = now.subtract(months, 'months').toDate()

    return tf
  }, [months])

  useEffect(() => {
    const fetchData = async () => {
      if (!imei) return

      setIsLoading(true)
      try {
        const patientResponse = await getPatientByImei(imei)

        if (!patientResponse?.patient?.id) {
          console.error('No patient ID found')
          return
        }

        const response = await fetch('/routes/users/getPatientData', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: patientResponse.patient.id, providerId }),
        })

        if (!response.ok) {
          throw new Error('Failed to fetch patient data')
        }

        const data = await response.json()

        if (data && data.data) {
          setPatientData(data.data)

          if (data.data.threshold) {
            setThresholds(data.data.threshold.bloodPressure)
          } else {
            console.log('No thresholds found in getPatientData response')
          }

          const now = moment()
          const startDate = now
            .clone()
            .subtract(months, 'months')
            .startOf('day')
          const endDate = now.clone().endOf('day')

          const formattedBpm = data.data.bpm.map((reading) => {
            return {
              systolic: reading.systolic,
              diastolic: reading.diastolic,
              pulse: reading.pulse || 0,
              ts: reading.ts,
              deviceId: reading.deviceId || data.data.bpIMEI,
              dataType: reading.dataType || 'bp_measure',
              imei: data.data.bpIMEI,
            }
          })

          const {
            arrayBP,
            bpmTableArray,
            avgSys,
            avgDia,
            highSys,
            highDia,
            lowSys,
            lowDia,
          } = parseBpMeasures({
            bpm: formattedBpm,
            btMessagesBpm: data.data.btMessagesBpm || [],
            ttBpm: data.data.ttBpm || [],
            adBpm: data.data.adBpm || [],
            withingsBpm: data.data.withingsBpm || [],
            bpDevice: data.data.selectedBpDevice || CARDIOWELL,
            timeframe: startDate.toDate(),
            endDate: endDate.toDate(),
            timeZone: data.data.timeZone || moment.tz.guess(),
          })

          setGridData(bpmTableArray)
        } else {
          console.error('Invalid data format received:', data)
        }
      } catch (err) {
        console.error('Error fetching data:', err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [imei, months, durationText])

  const thresholdsToUse = useMemo(() => {
    if (!thresholds) {
      return null
    }
    return thresholds
  }, [thresholds])

  const {
    arrayBP,
    bpmTableArray,
    avgSys,
    avgDia,
    timeZone,
    deviceInfo: { sn, iccid, dataType, modelNumber, deviceId },
    lastReading,
  } = useMemo(() => {
    // Calculate filtering period
    const startDate = moment().subtract(months, 'months')
    const endDate = moment()

    // Transform and filter data
    const filteredData = gridData
      .map((reading) => {
        // Ensure ts is in milliseconds
        const timestampMs =
          typeof reading.ts === 'number' && reading.ts < 10000000000
            ? reading.ts * 1000
            : reading.ts

        // Get threshold level and color for each reading
        let thresholdLevel = null
        let color = null

        if (thresholds) {
          thresholdLevel = getBpThresholdLevel(
            reading.systolic,
            reading.diastolic,
            thresholds,
            reading.pulse || 0
          )
          color = getBloodPressureColorForLevel(thresholdLevel)
        }

        return {
          ...reading,
          ts: timestampMs,
          systolic: reading.systolic,
          diastolic: reading.diastolic,
          pulse: parseInt(reading.pulse) || 0,
          thresholdLevel,
          color,
        }
      })
      .filter((reading) => {
        const readingDate = moment(reading.ts)
        const isInRange =
          readingDate.isSameOrAfter(startDate) &&
          readingDate.isSameOrBefore(endDate)

        return isInRange
      })
      .sort((a, b) => a.ts - b.ts)

    // Calculate average systolic and diastolic manually
    let totalSys = 0
    let totalDia = 0
    let validReadings = 0

    filteredData.forEach((reading) => {
      if (reading.systolic > 0 && reading.diastolic > 0) {
        totalSys += reading.systolic
        totalDia += reading.diastolic
        validReadings++
      }
    })

    const calculatedAvgSys =
      validReadings > 0 ? Math.round(totalSys / validReadings) : 0
    const calculatedAvgDia =
      validReadings > 0 ? Math.round(totalDia / validReadings) : 0

    return {
      arrayBP: filteredData,
      bpmTableArray: filteredData,
      avgSys: calculatedAvgSys,
      avgDia: calculatedAvgDia,
      timeZone: patientData?.timeZone || moment.tz.guess(),
      deviceInfo: {
        sn: deviceData?.sn || '',
        iccid: deviceData?.iccid || '',
        dataType: deviceData?.dataType || '',
        modelNumber: deviceData?.modelNumber || '',
        deviceId: deviceData?.deviceId || '',
      },
      lastReading: filteredData[filteredData.length - 1]?.ts || null,
    }
  }, [gridData, months, patientData, deviceData, thresholds])

  const lastMeasurementInfo = useMemo(() => {
    if (gridData && gridData.length > 0) {
      const sortedData = [...gridData].sort((a, b) => {
        const tsA =
          typeof a.ts === 'number' && a.ts < 10000000000 ? a.ts * 1000 : a.ts
        const tsB =
          typeof b.ts === 'number' && b.ts < 10000000000 ? b.ts * 1000 : b.ts
        return tsB - tsA
      })

      const latest = sortedData[0]

      if (latest) {
        const timestamp =
          typeof latest.ts === 'number' && latest.ts < 10000000000
            ? latest.ts * 1000
            : latest.ts

        return {
          info: `${latest.systolic}/${latest.diastolic}`,
          title: 'Last Measurement',
          subText: lastReadingDate(timestamp, timeZone),
        }
      }
    }

    return {
      info: 'No data',
      title: 'Last Measurement',
      subText: '—',
    }
  }, [gridData, timeZone])

  const handleTableView = (_e, newTableView) => {
    if (newTableView) {
      setTableView(newTableView)
    }
  }

  const transformedData = useMemo(() => {
    if (tableView === 'Chart') {
      return gridData.map((reading) => ({
        date: moment(reading.ts).format('MM/DD/YY'),
        time: moment(reading.ts).format('hh:mm A'),
        systolic: reading.systolic,
        diastolic: reading.diastolic,
        pulse: reading.pulse || 0,
        ts: reading.ts,
        deviceId: reading.deviceId || deviceData?.deviceId,
        dataType: reading.dataType || deviceData?.dataType,
        imei: imei,
      }))
    }
    return gridData
  }, [tableView, gridData, deviceData, imei])

  return (
    <Fragment>
      <Container maxWidth="lg" className={classes.container}>
        <Box sx={{ mt: 2, mb: 2 }}>
          {isMobile ? (
            <Box>
              {/* Header */}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 3,
                  mt: 2,
                }}
              >
                <Typography
                  variant="h1"
                  sx={{
                    color: 'rgba(0, 0, 0, 0.87)',
                    fontSize: '28px',
                    fontWeight: 400,
                    lineHeight: 1.2,
                  }}
                >
                  Blood Pressure Measurement
                </Typography>
                <InfoDropdown
                  items={[]}
                  fitView={fitView}
                  setFitView={setFitView}
                  imei={imei}
                  firstName={deviceData?.firstName}
                  lastName={deviceData?.lastName}
                  manufacturer={deviceData?.manufacturer}
                  months={months}
                  bpmData={transformedData}
                  avgSys={avgSys}
                  avgDia={avgDia}
                  onGeneratePdf={(data) => {}}
                />
              </Box>

              {/* Last Measurement Card */}
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  mb: 2,
                  borderRadius: '12px',
                  border: '1px solid #E0E0E0',
                  backgroundColor: '#FFF',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                  }}
                >
                  <Box>
                    <Typography
                      variant="h6"
                      sx={{
                        color: 'rgba(0, 0, 0, 0.87)',
                        fontSize: '20px',
                        fontWeight: 500,
                        lineHeight: '32px',
                        letterSpacing: '0.15px',
                      }}
                    >
                      Last Measurement
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        color: 'rgba(0, 0, 0, 0.60)',
                        fontSize: '12px',
                        fontWeight: 400,
                        lineHeight: '20px',
                        letterSpacing: '0.4px',
                      }}
                    >
                      {lastMeasurementInfo.subText}
                    </Typography>
                  </Box>
                  <Typography
                    variant="h5"
                    sx={{
                      color: 'rgba(0, 0, 0, 0.87)',
                      fontSize: '24px',
                      fontWeight: 400,
                      lineHeight: 1.3,
                    }}
                  >
                    {lastMeasurementInfo.info}
                  </Typography>
                </Box>
              </Paper>

              {/* Average BP Card */}
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  borderRadius: '12px',
                  border: '1px solid #E0E0E0',
                  backgroundColor: '#FFF',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                  }}
                >
                  <Box>
                    <Typography
                      variant="h6"
                      sx={{
                        color: 'rgba(0, 0, 0, 0.87)',
                        fontSize: '20px',
                        fontWeight: 500,
                        lineHeight: '32px',
                        letterSpacing: '0.15px',
                      }}
                    >
                      Avg BP
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        color: 'rgba(0, 0, 0, 0.60)',
                        fontSize: '12px',
                        fontWeight: 400,
                        lineHeight: '20px',
                        letterSpacing: '0.4px',
                      }}
                    >
                      {durationText}
                    </Typography>
                  </Box>
                  <Typography
                    variant="h5"
                    sx={{
                      color: 'rgba(0, 0, 0, 0.87)',
                      fontSize: '24px',
                      fontWeight: 400,
                      lineHeight: 1.3,
                    }}
                  >
                    {avgSys && avgDia ? `${avgSys}/${avgDia}` : 'No data'}
                  </Typography>
                </Box>
              </Paper>
            </Box>
          ) : (
            <Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mb: 3,
                  mt: 2,
                  gap: 1,
                }}
              >
                <Typography
                  variant="h1"
                  sx={{
                    color: 'rgba(0, 0, 0, 0.87)',
                    fontSize: '28px',
                    fontWeight: 400,
                    lineHeight: 1.2,
                  }}
                >
                  Blood Pressure Measurement
                </Typography>
                <InfoDropdown
                  items={[]}
                  fitView={fitView}
                  setFitView={setFitView}
                  imei={imei}
                  firstName={deviceData?.firstName}
                  lastName={deviceData?.lastName}
                  manufacturer={deviceData?.manufacturer}
                  months={months}
                  bpmData={transformedData}
                  avgSys={avgSys}
                  avgDia={avgDia}
                  onGeneratePdf={(data) => {}}
                />
              </Box>
              <Box sx={{ mt: 5, mb: 3, display: 'flex', gap: '24px' }}>
                <Paper
                  elevation={0}
                  sx={{
                    width: '270px',
                    bgcolor: '#ffffff',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    textAlign: 'center',
                    padding: '32px 16px',
                  }}
                >
                  <Typography
                    variant="h4"
                    sx={{
                      fontSize: '32px',
                      fontWeight: 400,
                      color: '#3F51B5',
                      lineHeight: 1.2,
                      mb: 1,
                    }}
                  >
                    {lastMeasurementInfo.info}
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: '16px',
                      fontWeight: 500,
                      color: 'rgba(0, 0, 0, 0.87)',
                      mb: 0.5,
                    }}
                  >
                    {lastMeasurementInfo.title}
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: '14px',
                      fontWeight: 400,
                      color: 'rgba(0, 0, 0, 0.6)',
                    }}
                  >
                    {lastMeasurementInfo.subText}
                  </Typography>
                </Paper>

                <Paper
                  elevation={0}
                  sx={{
                    width: '270px',
                    bgcolor: '#ffffff',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    textAlign: 'center',
                    padding: '32px 16px',
                  }}
                >
                  <Typography
                    variant="h4"
                    sx={{
                      fontSize: '32px',
                      fontWeight: 400,
                      color: '#3F51B5',
                      lineHeight: 1.2,
                      mb: 1,
                    }}
                  >
                    {avgSys && avgDia ? `${avgSys}/${avgDia}` : 'No data'}
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: '16px',
                      fontWeight: 500,
                      color: 'rgba(0, 0, 0, 0.87)',
                      mb: 0.5,
                    }}
                  >
                    Avg BP
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: '14px',
                      fontWeight: 400,
                      color: 'rgba(0, 0, 0, 0.6)',
                    }}
                  >
                    {durationText}
                  </Typography>
                </Paper>
              </Box>
            </Box>
          )}
        </Box>
        <Box
          sx={{
            ...(!isMobile && {
              display: 'flex',
              alignItems: 'flex-end',
              justifyContent: 'space-between',
            }),
          }}
        >
          {!isMobile && (
            <StyledToggleButtonGroup
              color="primary"
              value={tableView}
              exclusive
              onChange={handleTableView}
              sx={{ marginTop: theme.spacing(1), maxHeight: '70px' }}
            >
              <ToggleButton value="Chart">Chart</ToggleButton>
              <ToggleButton value="Readings">Readings</ToggleButton>
            </StyledToggleButtonGroup>
          )}
          <ResponsiveDurationButtons
            months={months}
            setMonths={setMonths}
            setDurationText={setDurationText}
            isMobile={isMobile}
          />
        </Box>
        {tableView === 'Chart' ? (
          <Stack
            direction="column"
            spacing={4}
            sx={{ marginTop: theme.spacing(2) }}
          >
            <ChartPaper
              sx={{
                padding: 0,
                overflow: 'hidden',
                borderRadius: '8px',
                boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',
              }}
            >
              <Box
                sx={{
                  height: '350px',
                  padding: '10px 10px 20px 10px',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                }}
              >
                <BloodPressureChart
                  timeZone={timeZone}
                  chartData={arrayBP}
                  threshold={thresholdsToUse}
                  startDate={moment()
                    .subtract(months, 'months')
                    .startOf('day')
                    .toDate()}
                  endDate={moment().endOf('day').toDate()}
                  durationDays={months}
                  fitView={fitView}
                  scrollRef={bpChartRef}
                  syncScrollRef={hrChartRef}
                />
              </Box>
            </ChartPaper>

            <ChartPaper
              sx={{
                padding: 0,
                overflow: 'hidden',
                borderRadius: '8px',
                boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',
              }}
            >
              <Box
                sx={{
                  height: '350px',
                  padding: '10px 10px 20px 10px',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                }}
              >
                <HeartRateChart
                  timeZone={timeZone}
                  chartData={arrayBP}
                  threshold={thresholdsToUse?.pulse}
                  startDate={moment()
                    .subtract(months, 'months')
                    .startOf('day')
                    .toDate()}
                  endDate={moment().endOf('day').toDate()}
                  durationDays={months}
                  fitView={fitView}
                  scrollRef={hrChartRef}
                  syncScrollRef={bpChartRef}
                />
              </Box>
            </ChartPaper>

            {isMobile && (
              <StyledToggleButtonGroup
                color="primary"
                value={tableView}
                exclusive
                onChange={handleTableView}
                sx={{
                  marginTop: theme.spacing(1),
                  maxHeight: '70px',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <ToggleButton value="Chart">Chart</ToggleButton>
                <ToggleButton value="Readings">Readings</ToggleButton>
              </StyledToggleButtonGroup>
            )}
          </Stack>
        ) : (
          <Stack>
            <Paper sx={{ minHeight: 240, padding: theme.spacing(2) }}>
              {isLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <Typography>Loading data...</Typography>
                </Box>
              ) : error ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <Typography color="error">{error}</Typography>
                </Box>
              ) : (
                <BloodPressureGrid gridData={gridData} />
              )}
            </Paper>
            {isMobile && (
              <StyledToggleButtonGroup
                color="primary"
                value={tableView}
                exclusive
                onChange={handleTableView}
                sx={{
                  marginTop: theme.spacing(1),
                  maxHeight: '70px',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <ToggleButton value="Chart">Chart</ToggleButton>
                <ToggleButton value="Readings">Readings</ToggleButton>
              </StyledToggleButtonGroup>
            )}
          </Stack>
        )}
      </Container>
    </Fragment>
  )
}
