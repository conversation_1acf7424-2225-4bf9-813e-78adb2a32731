// External Libraries
import React from 'react'
import { Modal, Box, Typography, IconButton } from '@mui/material'

// Assets
import { WarningIcon } from '../../../images/icons/WarningIcon'
import { CustomCloseIcon } from '../../../images/icons/CloseIcon'
import { CheckCircleIcon } from '../../../images/icons/CheckCircleIcon'
import { InfoIcon } from '../../../images/icons/InfoIcon'

export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  INFO: 'info',
  WARNING: 'warning',
}

const notificationStyles = {
  [NOTIFICATION_TYPES.SUCCESS]: {
    background: 'rgb(237, 247, 237)',
    color: 'rgb(30, 70, 32)',
    icon: CheckCircleIcon,
  },
  [NOTIFICATION_TYPES.ERROR]: {
    background: 'rgb(255, 235, 238)',
    color: 'rgb(97, 26, 21)',
    icon: WarningIcon,
  },
  [NOTIFICATION_TYPES.INFO]: {
    background: 'rgb(232, 244, 253)',
    color: 'rgb(13, 60, 97)',
    icon: InfoIcon,
  },
  [NOTIFICATION_TYPES.WARNING]: {
    background: 'rgb(255, 244, 229)',
    color: 'rgb(102, 60, 0)',
    icon: WarningIcon,
  },
}

export const NotificationModal = ({
  open,
  onClose,
  title,
  message,
  type = NOTIFICATION_TYPES.ERROR,
  autoCloseTime = 5000,
  position = 'bottom',
}) => {
  const style =
    notificationStyles[type] || notificationStyles[NOTIFICATION_TYPES.ERROR]
  const Icon = style.icon

  React.useEffect(() => {
    let timeoutId
    if (open && autoCloseTime > 0) {
      timeoutId = setTimeout(() => {
        onClose()
      }, autoCloseTime)
    }
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [open, onClose, autoCloseTime])

  const getPositionStyle = () => {
    switch (position) {
      case 'top':
        return {
          top: '16px',
          left: '50%',
          transform: 'translateX(-50%)',
        }
      case 'center':
        return {
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
        }
      case 'bottom':
      default:
        return {
          bottom: '16px',
          left: '50%',
          transform: 'translateX(-50%)',
        }
    }
  }

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="notification-modal"
      sx={{
        backgroundColor: 'transparent',
        boxShadow: 'none',
      }}
      BackdropProps={{
        sx: {
          backgroundColor: 'transparent',
        },
      }}
    >
      <Box
        sx={{
          position: 'fixed',
          ...getPositionStyle(),
          width: '328px',
          bgcolor: style.background,
          borderRadius: '8px',
          padding: '16px',
          display: 'flex',
          alignItems: 'flex-start',
          gap: '12px',
          boxShadow: '0px 3px 5px rgba(0, 0, 0, 0.2)',
        }}
      >
        <Icon />
        <Box sx={{ flex: 1 }}>
          {title && (
            <Typography
              sx={{
                color: style.color,
                fontSize: '16px',
                fontWeight: 500,
                lineHeight: 1.5,
              }}
            >
              {title}
            </Typography>
          )}
          {message && (
            <Typography
              sx={{
                color: style.color,
                fontSize: '14px',
                lineHeight: 1.5,
              }}
            >
              {message}
            </Typography>
          )}
        </Box>
        <IconButton
          onClick={onClose}
          sx={{
            color: 'rgba(0, 0, 0, 0.54)',
            padding: '4px',
            marginLeft: 'auto',
            marginRight: '-8px',
            marginTop: '-4px',
          }}
        >
          <CustomCloseIcon />
        </IconButton>
      </Box>
    </Modal>
  )
}
