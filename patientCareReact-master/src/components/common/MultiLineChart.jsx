import { useTheme } from '@material-ui/core/styles'
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YA<PERSON><PERSON>,
  Label,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts'
import Title from '../Title'
import { CHART_COLORS } from '../../constants/chartConstants'

/**
 * Custom label component for chart data points
 * @param {Object} props - Component props
 * @param {number} props.x - X coordinate
 * @param {number} props.y - Y coordinate
 * @param {string|number} props.value - Value to display
 * @returns {JSX.Element} Text element with value
 */
const CustomizedLabel = ({ x, y, value }) => {
  // Add null checks
  if (x === undefined || y === undefined || value === undefined) {
    return null
  }

  return (
    <text
      x={x}
      y={y}
      dy={-4}
      fill={CHART_COLORS.SYSTOLIC}
      fontSize={10}
      textAnchor="middle"
    >
      {value}
    </text>
  )
}

/**
 * Custom tick component for X-axis labels
 * @param {Object} props - Component props
 * @param {number} props.x - X coordinate
 * @param {number} props.y - Y coordinate
 * @param {Object} props.payload - Payload with value
 * @returns {JSX.Element} Group element with text lines
 */
const CustomTick = ({ x, y, payload }) => {
  // Add null checks
  if (x === undefined || y === undefined || !payload?.value) {
    return null
  }

  const lines = payload.value.split('\n')

  return (
    <g transform={`translate(${x},${y})`}>
      {lines.map((line, index) => (
        <text
          key={index}
          x={0}
          y={index * 12}
          dy={6}
          textAnchor="middle"
          fill="#666"
          fontSize={10}
        >
          {line}
        </text>
      ))}
    </g>
  )
}

/**
 * Multi-line chart component with customizable lines and data
 * @param {Object} props - Component props
 * @param {string} props.title - Chart title
 * @param {string} props.label - Y-axis label
 * @param {Array} props.data - Chart data points
 * @param {Array} props.lines - Line configurations
 * @param {Array} props.domain - Y-axis domain
 * @returns {JSX.Element} Multi-line chart component
 */
export const MultiLineChart = ({
  title,
  label,
  data = [],
  lines = [],
  domain,
}) => {
  const theme = useTheme()

  // Add validation for required props
  if (!data || !Array.isArray(data)) {
    console.warn('MultiLineChart: data prop must be a valid array')
    return null
  }

  if (!lines || !Array.isArray(lines)) {
    console.warn('MultiLineChart: lines prop must be a valid array')
    return null
  }

  return (
    <>
      <Title>{title}</Title>
      <ResponsiveContainer>
        <LineChart
          data={data}
          margin={{
            top: 16,
            right: 16,
            bottom: 0,
            left: 24,
          }}
        >
          <XAxis
            dataKey="date"
            stroke={theme?.palette?.text?.secondary || CHART_COLORS.AXIS_LABEL}
            tick={<CustomTick />}
            height={60}
            interval={0}
            textAnchor="middle"
          />
          <YAxis
            stroke={theme?.palette?.text?.secondary || CHART_COLORS.AXIS_LABEL}
            domain={domain}
          >
            <Label
              angle={270}
              position="left"
              style={{
                textAnchor: 'middle',
                fill: theme?.palette?.text?.primary || CHART_COLORS.AXIS_LABEL,
              }}
            >
              {label}
            </Label>
          </YAxis>
          <Tooltip />
          <Legend iconType="rect" />
          {lines.map((line) => {
            if (!line || !line.key) {
              console.warn('MultiLineChart: Invalid line configuration', line)
              return null
            }

            return (
              <Line
                type="monotone"
                key={line.key}
                dataKey={line.key}
                stroke={line.color || CHART_COLORS.SYSTOLIC}
                dot={
                  line.dot || {
                    stroke: line.color || CHART_COLORS.SYSTOLIC,
                    strokeWidth: 2,
                    fill: line.color || CHART_COLORS.SYSTOLIC,
                  }
                }
                isAnimationActive={false}
                label={<CustomizedLabel />}
              />
            )
          })}
        </LineChart>
      </ResponsiveContainer>
    </>
  )
}
