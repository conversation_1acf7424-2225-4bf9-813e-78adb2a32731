import { makeStyles } from '@material-ui/core/styles'
import { max } from 'lodash'

const drawerWidth = 240

export const useStyles = makeStyles((theme) => ({
  root: {
    display: 'flex',
  },
  toolbar: {
    paddingRight: 24, // keep right padding when drawer closed
    display: 'flex',
    justifyContent: 'space-between',
  },
  toolbarIcon: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    padding: '0 8px',
    ...theme.mixins.toolbar,
  },
  appBar: {
    height: '72px',
    justifyContent: 'center',
    zIndex: theme.zIndex.drawer + 1,
    transition: theme.transitions.create(['width', 'margin'], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
  },
  appBarShift: {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(['width', 'margin'], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
  menuButton: {
    marginRight: 36,
  },
  menuButtonHidden: {
    display: 'none',
  },
  drawerPaper: {
    position: 'relative',
    whiteSpace: 'nowrap',
    width: drawerWidth,
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
  drawerPaperClose: {
    overflowX: 'hidden',
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    width: theme.spacing(7),
    [theme.breakpoints.up('sm')]: {
      width: theme.spacing(9),
    },
  },
  appBarSpacer: theme.mixins.toolbar,
  content: {
    flexGrow: 1,
    height: '100vh',
    overflow: 'auto',
  },
  container: {
    paddingTop: theme.spacing(4),
    paddingBottom: theme.spacing(4),
  },
  paper: {
    padding: theme.spacing(2),
    display: 'flex',
    overflow: 'auto',
    flexDirection: 'column',
  },
  fixedHeight: {
    height: 240,
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120,
  },
  column: {
    flexBasis: '33.33%',
  },
  heading: {
    fontSize: theme.typography.pxToRem(15),
    fontWeight: theme.typography.fontWeightRegular,
  },
  centered: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: `calc(100vh - ${theme.spacing(24)}px)`,
  },
  formWrapper: {
    margin: '32px 16px 36px 16px',
    backgroundColor: '#fff',
    borderRadius: '8px',
    padding: '16px',
    maxWidth: '400px',
    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.05)',
  },
  title: {
    fontSize: '20px',
    fontWeight: 500,
    marginBottom: '0px',
  },
  subtitle: {
    fontSize: '14px',
    color: 'rgba(0, 0, 0, 0.6)',
    marginBottom: '24px',
  },
  stepLabel: {
    color: '#5B6BF8',
    fontSize: '14px',
    marginBottom: '24px',
  },
  fieldGroup: {
    position: 'relative',
    marginBottom: '16px',
  },
  fieldLabel: {
    fontSize: '12px',
    color: 'rgba(0, 0, 0, 0.6)',
    marginBottom: '4px',
  },
  input: {
    width: '100%',
    '& .MuiOutlinedInput-root': {
      borderRadius: '8px',
      height: '70px !important',
      maxHeight: '100% !important',
      '& fieldset': {
        borderColor: '#E0E0E0',
      },
      '&:hover fieldset': {
        borderColor: '#5B6BF8',
      },
    },
    '& .MuiOutlinedInput-input': {
      padding: '12px 14px',
    },
  },
  calendarIcon: {
    position: 'absolute',
    right: '12px',
    top: '35px',
    color: 'rgba(0, 0, 0, 0.54)',
    pointerEvents: 'none',
  },
  button: {
    width: '100%',
    padding: '14px',
    borderRadius: '8px',
    textTransform: 'none',
    fontSize: '14px',
    marginBottom: '12px',
  },
  primaryButton: {
    backgroundColor: '#5B6BF8',
    color: '#fff',
    '&:hover': {
      backgroundColor: '#4555EA',
    },
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    color: '#5B6BF8',
    border: '1px solid #5B6BF8',
  },
}))

export const useFormStyles = makeStyles((theme) => ({
  textField: {
    '& .MuiFormHelperText-root': {
      marginLeft: 0,
      marginRight: 0,
    },
    '& .MuiOutlinedInput-root': {
      '&.Mui-error': {
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: '#d32f2f',
        },
      },
    },
  },
  paper: {
    marginTop: theme.spacing(5.25),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },
  avatar: {
    margin: theme.spacing(1),
    backgroundColor: theme.palette.secondary.main,
  },
  form: {
    width: '100%',
    marginTop: theme.spacing(1),
  },
  submit: {
    margin: theme.spacing(3, 0, 3),
  },
  image: {
    margin: theme.spacing(1),
    width: '50%',
    [theme.breakpoints.down('sm')]: {
      width: '104px',
      height: '18px',
      margin: 0,
    },
  },
  title: {
    margin: theme.spacing(1),
  },
  formElement: {
    margin: theme.spacing(2, 0, 1),
  },
  footer: {
    flex: 1,
    alignSelf: 'flex-end',
    position: 'sticky',
    bottom: 0,
  },
  element: {
    '& .MuiFormHelperText-root': {
      margin: '8px 0 0 0',
      position: 'static',
    },
  },
}))
