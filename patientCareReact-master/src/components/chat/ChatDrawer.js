import React, { useEffect, useRef, useState } from 'react'
import {
  Drawer,
  IconButton,
  Box,
  Typography,
  TextField,
  CircularProgress,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { BPBuddyIcon } from './icon/BPBuddyIcon'
import NavigationOutlinedIcon from '@mui/icons-material/NavigationOutlined'
import { format, isToday, isYesterday } from 'date-fns'

const formatMessageDate = (isoDate) => {
  const date = new Date(isoDate)

  if (isToday(date)) {
    return `today at ${format(date, 'p')}`
  } else if (isYesterday(date)) {
    return `yesterday at ${format(date, 'p')}`
  } else {
    return format(date, 'PPpp') // fallback: full readable date/time
  }
}

export const ChatDrawer = ({ open, onClose }) => {
  const [messages, setMessages] = useState([])
  const messagesEndRef = useRef(null)
  const [newMessage, setNewMessage] = useState('')
  const [isAssistantTyping, setIsAssistantTyping] = useState(false)
  const inputRef = useRef(null)

  useEffect(() => {
    async function fetchMessages() {
      try {
        const response = await fetch('/routes/chat/messages')

        if (!response.ok) {
          const errorText = await response
            .text()
            .catch(() => 'Unable to read error body')
          console.error(
            `Failed to fetch messages. Status: ${response.status}, Body: ${errorText}`
          )
          return
        }

        let responseObject
        try {
          responseObject = await response.json()
        } catch (parseError) {
          console.error('Failed to parse messages response JSON:', parseError)
          return
        }

        if (
          responseObject?.messages &&
          Array.isArray(responseObject.messages)
        ) {
          setMessages(responseObject.messages)
        } else {
          console.error('Invalid response format: no "messages" array found')
        }
      } catch (networkError) {
        console.error(
          'Network or fetch error while fetching messages:',
          networkError
        )
      }
    }
    fetchMessages()
  }, [])

  useEffect(() => {
    if (open && messages.length > 0) {
      const timeout = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
      }, 100) // allow drawer animation/render time
      return () => clearTimeout(timeout)
    }
  }, [open, messages])

  useEffect(() => {
    if (open) {
      // Timeout helps wait for drawer animation/rendering
      const timeout = setTimeout(() => {
        inputRef.current?.focus()
      }, 200)

      return () => clearTimeout(timeout)
    }
  }, [open])

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return
    setNewMessage('')

    setMessages((prev) => [
      ...prev,
      {
        messageText: newMessage,
        senderType: 'user',
        _id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        temporary: true,
      },
    ])

    setIsAssistantTyping(true)
    try {
      const response = await fetch('/routes/chat/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messageText: newMessage }),
      })

      if (response.ok) {
        const responseBody = await response.json()
        setMessages((prev) => [
          ...prev.filter((m) => !m.temporary),
          ...responseBody.messages,
        ])
      } else {
        console.error('Failed to send message')
      }
    } catch (error) {
      console.error('Error sending message:', error)
    }
    setIsAssistantTyping(false)
  }

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: { xs: '100%', sm: '400px' },
          boxShadow: '0 0 20px rgba(0,0,0,0.2)',
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      <Box sx={{ p: 4, pr: 2 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <BPBuddyIcon />
            <Typography variant="h6">BP Buddy</Typography>
          </Box>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </Box>
      <Box sx={{ flex: 1, overflowY: 'auto', p: 4 }}>
        {messages.length === 0 ? (
          <Typography variant="body2" color="text.secondary">
            No messages yet.
          </Typography>
        ) : (
          <>
            {messages.map((msg) => (
              <Box
                key={msg._id}
                sx={{
                  mb: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems:
                    msg.senderType === 'system' ? 'flex-start' : 'flex-end',
                }}
              >
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mb: 0.5, fontSize: '11px' }}
                >
                  {msg.senderType === 'system' ? 'BP Buddy' : 'You'}{' '}
                  {formatMessageDate(msg.createdAt)}
                </Typography>
                <Box
                  sx={{
                    maxWidth: '80%',
                    p: 1.5,
                    borderRadius: 2,
                    bgcolor:
                      msg.senderType === 'system' ? 'grey.200' : '#E8EAF6',
                    color: 'text.primary',
                  }}
                >
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                    {msg.messageText}
                  </Typography>
                </Box>
              </Box>
            ))}

            {/* Typing Indicator */}
            {isAssistantTyping && (
              <Box
                sx={{
                  mb: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                }}
              >
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mb: 0.5, fontSize: '11px' }}
                >
                  BP Buddy is typing...
                </Typography>
                <Box
                  sx={{
                    maxWidth: '80%',
                    py: 1,
                    px: 2,
                    borderRadius: 2,
                    bgcolor: 'grey.200',
                    display: 'flex',
                    alignItems: 'center',
                    height: 40,
                  }}
                >
                  <CircularProgress size={16} />
                </Box>
              </Box>
            )}

            <div ref={messagesEndRef} />
          </>
        )}
      </Box>
      <Box
        sx={{
          px: 2,
          py: 2,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            bgcolor: 'background.paper',
          }}
        >
          <TextField
            fullWidth
            multiline
            minRows={1}
            maxRows={10}
            placeholder="Your question here"
            variant="outlined"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey && !isAssistantTyping) {
                e.preventDefault()
                handleSendMessage()
              }
            }}
            InputProps={{
              sx: {
                borderRadius: '6px',
                bgcolor: 'white',
                paddingY: 1,
                '& .MuiInputBase-inputMultiline': {
                  padding: 0,
                  lineHeight: 1.5,
                },
              },
            }}
            inputRef={inputRef}
          />
          <IconButton
            onClick={() => {
              if (!isAssistantTyping) {
                handleSendMessage()
              }
            }}
            disabled={isAssistantTyping}
            sx={{
              bgcolor: '#2E3EB8',
              color: 'white',
              width: 32,
              height: 32,
              borderRadius: '50%',
              '&:hover': {
                bgcolor: '#1e2c96', // darker hover
              },
              opacity: isAssistantTyping ? 0.5 : 1, // optional visual cue
              cursor: isAssistantTyping ? 'not-allowed' : 'pointer',
            }}
          >
            <NavigationOutlinedIcon
              sx={{
                transform: 'rotate(45deg) translate(0px, -3px)', // moves right & up
                width: 18,
                height: 18,
              }}
            />
          </IconButton>
        </Box>
      </Box>
    </Drawer>
  )
}
