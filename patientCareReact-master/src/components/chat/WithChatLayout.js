import React, { useState } from 'react'
import { Box, IconButton } from '@mui/material'
import { ChatDrawer } from './ChatDrawer'
import OpenChatIcon from './icon/OpenChatIcon'

export const WithChatLayout = ({ children }) => {
  const [chatOpen, setChatOpen] = useState(false)

  return (
    <Box sx={{ position: 'relative', width: '100%' }}>
      {/* Chat Toggle Button (e.g., floating button) */}
      <IconButton
        onClick={() => setChatOpen(true)}
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          zIndex: 1300,
          bgcolor: '#3E3EB8',
          color: 'white',
          opacity: chatOpen ? 0 : 1,
          transform: chatOpen ? 'scale(0.8)' : 'scale(1)',
          pointerEvents: chatOpen ? 'none' : 'auto',
          transition: 'opacity 300ms ease, transform 300ms ease',
          '&:hover': {
            bgcolor: '#1e2c96', // darker hover
          },
        }}
      >
        <OpenChatIcon />
      </IconButton>

      {/* Main content with dynamic margin */}
      <Box
        sx={{
          transition: 'margin 0.3s',
          marginRight: chatOpen ? { xs: 0, sm: '400px' } : 0,
        }}
      >
        {children}
      </Box>

      <ChatDrawer open={chatOpen} onClose={() => setChatOpen(false)} />
    </Box>
  )
}
