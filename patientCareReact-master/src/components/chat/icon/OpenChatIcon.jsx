import React from 'react'
import SvgIcon from '@mui/material/SvgIcon'

const OpenChatIcon = (props) => {
  return (
    <SvgIcon {...props} viewBox="0 0 32 32" sx={{ fontSize: 32 }}>
      <path
        d="M8.60663 25.3333L2.66663 30V5.33333C2.66663 4.97971 2.8071 4.64057 3.05715 4.39052C3.3072 4.14048 3.64634 4 3.99996 4H28C28.3536 4 28.6927 4.14048 28.9428 4.39052C29.1928 4.64057 29.3333 4.97971 29.3333 5.33333V24C29.3333 24.3536 29.1928 24.6928 28.9428 24.9428C28.6927 25.1929 28.3536 25.3333 28 25.3333H8.60663ZM5.33329 24.5133L7.68396 22.6667H26.6666V6.66667H5.33329V24.5133Z"
        fill="white"
      />
      <rect x="12" y="12" width="2" height="3.2" rx="1" fill="white" />
      <rect x="18.6666" y="12" width="2" height="3.2" rx="1" fill="white" />
      <path
        d="M14 17.3334C14.5619 17.811 16.6667 19.3334 18.6667 17.3334"
        stroke="white"
        stroke-width="1.5"
      />
    </SvgIcon>
  )
}

export default OpenChatIcon
