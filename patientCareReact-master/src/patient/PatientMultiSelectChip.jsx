import { Controller, useFormContext } from 'react-hook-form'
import { useTheme } from '@mui/material/styles'
import Box from '@mui/material/Box'
import OutlinedInput from '@mui/material/OutlinedInput'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import FormControl from '@mui/material/FormControl'
import Select from '@mui/material/Select'
import Chip from '@mui/material/Chip'

const ITEM_HEIGHT = 48
const ITEM_PADDING_TOP = 8
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
}

function getStyles(item, value, theme) {
  return {
    fontWeight: value.includes(item)
      ? theme.typography.fontWeightMedium
      : theme.typography.fontWeightRegular,
  }
}

export const PatientMultipleSelectChip = ({
  name,
  label,
  id,
  items = [],
  rules = {},
  disabled = false,
  isMobile,
}) => {
  const theme = useTheme()

  const {
    control,
    formState: { errors },
  } = useFormContext()

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      render={({ field: { name, value, onChange } }) => (
        <FormControl
          sx={{
            margin: !isMobile ? '15px' : '15px 0 0 0',
            marginBottom: !isMobile ? '15px' : '8px',
            width: !isMobile ? '93%' : '100%',
          }}
        >
          <InputLabel id={`${id}-chip-label`}>{label}</InputLabel>
          <Select
            name={name}
            labelId={`${id}-chip-label`}
            id={`${id}-chip`}
            multiple
            value={value}
            onChange={onChange}
            input={
              <OutlinedInput id={`${id}-select-multiple-chip`} label={label} />
            }
            renderValue={(selected) => (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {selected.map((item) => (
                  <Chip
                    onMouseDown={(e) => e.stopPropagation()}
                    onDelete={() =>
                      onChange(value.filter((chip) => chip !== item))
                    }
                    key={item}
                    label={item}
                  />
                ))}
              </Box>
            )}
            MenuProps={MenuProps}
            disabled={disabled}
            error={errors[name]?.message}
          >
            {items.map((item) => (
              <MenuItem
                key={item}
                value={item}
                style={getStyles(item, value, theme)}
              >
                {item}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      )}
    />
  )
}
