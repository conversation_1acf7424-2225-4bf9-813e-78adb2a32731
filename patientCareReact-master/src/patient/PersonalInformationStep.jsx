import { useFormContext, Controller } from 'react-hook-form'
import { makeStyles } from '@material-ui/core/styles'
import TextField from '@material-ui/core/TextField'
import Typography from '@material-ui/core/Typography'
import MenuItem from '@material-ui/core/MenuItem'
import CalendarTodayIcon from '@mui/icons-material/CalendarToday'
import { genders, ethnicities } from './formOptions/genders'

const useStyles = makeStyles(() => ({
  stepLabel: {
    color: '#5B6BF8',
    fontSize: '14px',
    marginBottom: '24px',
  },
  fieldGroup: {
    marginBottom: '16px',
  },
  fieldLabel: {
    fontSize: '12px',
    color: 'rgba(0, 0, 0, 0.6)',
    marginBottom: '4px',
  },
  input: {
    '& .MuiOutlinedInput-root': {
      borderRadius: '8px',

      '& fieldset': {
        borderColor: '#E0E0E0',
      },
      '&:hover fieldset': {
        borderColor: '#5B6BF8',
      },
    },
  },
}))

export const PersonalInformationStep = () => {
  const classes = useStyles()
  const { control } = useFormContext()

  return (
    <>
      <Typography className={classes.stepLabel}>
        Step 1. Personal information
      </Typography>
    </>
  )
}
