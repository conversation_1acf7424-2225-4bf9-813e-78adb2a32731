export const providerSavePatientChanges = async (data) => {
  const normalizedCell =
    !data.cellNumber || data.cellNumber === '+1' ? '' : data.cellNumber
  const normalizedHome =
    !data.homeNumber || data.homeNumber === '+1' ? '' : data.homeNumber

  const patient = {
    id: data.id,
    firstName: data.firstName,
    lastName: data.lastName,
    homeNumber: normalizedHome,
    cellNumber: normalizedCell,
    MRN: data.mrn,
    email: data.email,
    address: data.address,
    city: data.city,
    state: data.state,
    zip: data.zip,
    timeZone: data.timezone,
    bpIMEI: data.bpImei,
    ttBpIMEI: data.transtekBpImei,
    adBpIMEI: data.adBpImei,
    weightIMEI: data.weightImei,
    ttWeightIMEI: data.transtekWeightImei,
    pulseIMEI: data.pulseImei,
    glucoseIMEI: data.glucoseImei,
    patientClinic: data.clinic,
    selectedBpDevice: data.selectedBpDevice,
    selectedWeightDevice: data.selectedWeightDevice,
    deviceNotificationsEnabled: data.deviceNotificationsEnabled,
    targetWeight: data.targetWeight,
    birthdate: data.birthdate,
    gender: data.gender,
    ethnicity: data.ethnicity,
    maritalStatus: data.maritalStatus,
    education: data.education,
    employment: data.employment,
    income: data.income,
    language: data.language,
    socialConnectedness: data.socialConnectedness,
    allergies: data.allergies,
    chronicConditions: data.chronicConditions,
    hypertensionMedications: data.hypertensionMedications,
    medicationTime: data.medicationTime,
    showTestData: data.showTestData,
    providerId: sessionStorage.getItem('providerID'),
  }

  return await fetch('/routes/users/providerSavePatientChanges', {
    method: 'POST',
    body: JSON.stringify(patient),
    headers: { 'Content-Type': 'application/json' },
  })
    .then((response) => {
      if (response.ok) {
        return response.json()
      }
      return {
        error: 'error',
        status: response.status,
        statusText: response.statusText,
      }
    })
    .catch((error) => {
      console.error(error)
      return { error }
    })
}
