import { useId, useState } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import Button from '@material-ui/core/Button'
import { useStyles } from '../components/common/style'
import Typography from '@material-ui/core/Typography'
import Grid from '@material-ui/core/Grid'
import { PatientForm } from './PatientForm'
import { defaultFormState } from './defaultFormState'
import { providerAddPatient } from './providerAddPatient'

export const AddPatientForm = ({ providerID, clinic, updatePatients }) => {
  const formId = useId()
  const methods = useForm({ defaultValues: defaultFormState })
  const classes = useStyles()
  const [serverMessage, setServerMessage] = useState('')

  const onSubmit = async (data) => {
    setServerMessage('Adding patient. Please wait...')
    try {
      const response = await providerAddPatient(data, providerID, clinic)
      if (response.status !== 201) {
        return setServerMessage('There has been an error. Please try again')
      }

      const responseData = await response.json()
      if (responseData.message === 'Success') {
        setServerMessage('')
        updatePatients(responseData.data)
      }
    } catch (e) {
      setServerMessage(e.message || 'Server Error')
    }
  }

  return (
    <Typography>
      <Grid container spacing={3}>
        <Grid item>
          <FormProvider {...methods}>
            <form
              className={classes.form}
              id={formId}
              noValidate
              onSubmit={methods.handleSubmit(onSubmit)}
            >
              <PatientForm
                addClinic={false}
                timezoneRequired={true}
                isLoading={false}
                showWithings={false}
              />
            </form>
          </FormProvider>
        </Grid>
        <Grid item>
          <p>{serverMessage}</p>
          <Button
            variant="contained"
            color="primary"
            form={formId}
            type="submit"
            className={classes.submit}
            disabled={methods.formState.isSubmitting}
          >
            Add Patient
          </Button>
        </Grid>
      </Grid>
    </Typography>
  )
}
