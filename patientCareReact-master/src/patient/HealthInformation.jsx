import { Controller, useFormContext } from 'react-hook-form'
import Typography from '@material-ui/core/Typography'
import TextField from '@material-ui/core/TextField'
import MenuItem from '@material-ui/core/MenuItem'
import { chronicConditions } from './formOptions/chronicConditions'
import { bloodPressureMedications } from './formOptions/bloodPressureMedications'
import Button from '@material-ui/core/Button'
import { useCustomStyles } from './EditPatientForm'

export const HealthInformation = ({ handleNextStep, methods }) => {
  const classes = useCustomStyles()
  const { control } = useFormContext()

  return (
    <>
      <Typography className={classes.stepLabel}>
        Step 2. Health information
      </Typography>

      <div className={classes.fieldGroup}>
        <Controller
          name="weight"
          control={control}
          rules={{ required: 'Weight is required' }}
          render={({ field, fieldState: { error } }) => (
            <TextField
              {...field}
              variant="outlined"
              fullWidth
              label="Weight, Lbs"
              placeholder="Enter your weight..."
              className={classes.element}
              error={!!error}
              helperText={error?.message}
              InputLabelProps={{
                shrink: true,
              }}
            />
          )}
        />
      </div>

      <div className={classes.fieldGroup}>
        <Controller
          name="height"
          control={control}
          rules={{ required: 'Height is required' }}
          render={({ field, fieldState: { error } }) => (
            <TextField
              {...field}
              variant="outlined"
              fullWidth
              label="Height, Inch"
              placeholder="Enter your height..."
              className={classes.element}
              error={!!error}
              helperText={error?.message}
              InputLabelProps={{
                shrink: true,
              }}
            />
          )}
        />
      </div>

      <div className={classes.fieldGroup}>
        <Controller
          name="chronicConditions"
          control={control}
          rules={{ required: 'Chronic conditions are required' }}
          render={({ field, fieldState: { error } }) => (
            <TextField
              {...field}
              select
              variant="outlined"
              fullWidth
              label="Chronic Conditions"
              placeholder="Choose your Chronic Conditions"
              className={classes.element}
              error={!!error}
              helperText={error?.message}
              InputLabelProps={{
                shrink: true,
              }}
              SelectProps={{
                displayEmpty: true,
                renderValue: (selected) => {
                  if (!selected) {
                    return (
                      <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                        Choose your Chronic Conditions
                      </span>
                    )
                  }
                  return selected
                },
              }}
            >
              <MenuItem value="" disabled>
                Choose your Chronic Conditions
              </MenuItem>
              {chronicConditions.map((condition) => (
                <MenuItem key={condition} value={condition}>
                  {condition}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </div>

      <div className={classes.fieldGroup}>
        <Controller
          name="hypertensionMedications"
          control={control}
          rules={{ required: 'Medications are required' }}
          render={({ field, fieldState: { error } }) => (
            <TextField
              {...field}
              select
              variant="outlined"
              fullWidth
              label="Medications"
              placeholder="Choose your Blood Pressure Medications"
              className={`${classes.element} ${classes.largeSelect}`}
              InputLabelProps={{
                shrink: true,
              }}
              SelectProps={{
                displayEmpty: true,
                renderValue: (selected) => {
                  if (!selected) {
                    return (
                      <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                        Choose your Blood Pressure
                        <br />
                        Medications
                      </span>
                    )
                  }
                  return selected
                },
              }}
            >
              <MenuItem value="" disabled>
                Choose your Blood Pressure Medications
              </MenuItem>
              {bloodPressureMedications.map((medication) => (
                <MenuItem key={medication} value={medication}>
                  {medication}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </div>

      <div className={classes.buttonContainer}>
        <Button
          className={`${classes.button} ${classes.primaryButton}`}
          onClick={handleNextStep}
          disabled={!methods.formState.isValid}
          style={{
            color: methods.formState.isValid ? '#FFF' : 'rgba(0, 0, 0, 0.26)',
          }}
        >
          Save My Information
        </Button>
        <Button className={`${classes.button} ${classes.secondaryButton}`}>
          Fill In Later
        </Button>
      </div>
    </>
  )
}
