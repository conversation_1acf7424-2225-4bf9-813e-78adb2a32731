import { useState, useId, useEffect } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import { Button } from '@material-ui/core'
import { Dialog } from '../components/common/Dialog'
import { defaultFormState } from './defaultFormState'
import { useStyles } from '../components/common/style'
import { PatientForm } from './PatientForm'

export const EditPatientModal = ({
  canDelete,
  open,
  patientId,
  patient,
  canEditClinic,
  timezoneRequired,
  clinics = [],
  handleClose = () => {},
  savePatientChanges = async () => {},
  deletePatient = async () => {},
}) => {
  const formId = useId()
  const methods = useForm({ defaultValues: defaultFormState })
  const classes = useStyles()
  const [serverMessage, setServerMessage] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  const closeHandler = () => {
    setServerMessage('')
    setIsLoading(true)
    handleClose()
    methods?.reset()
  }

  const deleteHandler = async () => {
    try {
      const response = await deletePatient()
      if (response.message === 'Success') {
        closeHandler()
      } else {
        setServerMessage(
          response.message || response.statusText || 'Service Error'
        )
      }
    } catch (e) {
      setServerMessage(e.message || 'Service Error')
    }
  }

  const onSubmit = async (data) => {
    try {
      setServerMessage('Saving patient changes. Please wait...')
      const medicationTime = Array.isArray(data.medicationTime)
        ? data.medicationTime
            .map(({ medication, time }) => ({
              medication: medication || '',
              time: time || '',
            }))
            .filter((item) => item.medication && item.time) // Filter out invalid entries
        : []
      const payload = {
        id: patientId,
        ...data,
        medicationTime,
      }
      const response = await savePatientChanges(payload)
      if (response.message === 'Success') {
        closeHandler()
      } else {
        setServerMessage(
          response.message || response.statusText || 'Service Error'
        )
      }
    } catch (e) {
      setServerMessage(e.message || 'Service Error')
    }
  }

  useEffect(() => {
    if (open && patientId && patient) {
      const setFieldValue = (field, value) => {
        if (value !== undefined && value !== null) {
          methods.setValue(field, value)
        }
      }

      if (patient.name) {
        const [firstName, lastName] = patient.name.split(' ')
        setFieldValue('firstName', firstName)
        setFieldValue('lastName', lastName)
      } else {
        setFieldValue('firstName', patient.firstName)
        setFieldValue('lastName', patient.lastName)
      }

      const fieldMappings = {
        mrn: patient.mrn || patient.MRN,
        email: patient.email,
        phoneNumber: patient.homeNumber,
        cellNumber: patient.cellNumber,
        address: patient.address,
        city: patient.city,
        state: patient.state,
        zip: patient.zip,
        timezone: patient.timeZone,
        bpImei: patient.bpIMEI,
        transtekBpImei: patient.ttBpIMEI,
        adBpImei: patient.adBpIMEI,
        withingsBpDevice: patient.withingsBpDevices?.deviceIds,
        weightImei: patient.weightIMEI,
        transtekWeightImei: patient.ttWeightIMEI,
        glucoseImei: patient.glucoseIMEI,
        pulseImei: patient.pulseIMEI,
        clinic: patient.clinic,
        selectedBpDevice: patient.selectedBpDevice,
        selectedWeightDevice: patient.selectedWeightDevice,
        deviceNotificationsEnabled: !!patient.deviceNotificationsEnabled,
        targetWeight: patient.targetWeight,
        birthdate: patient.birthdate,
        gender: patient.gender,
        ethnicity: patient.ethnicity,
        maritalStatus: patient.maritalStatus,
        education: patient.education,
        employment: patient.employment,
        income: patient.income,
        language: patient.language,
        socialConnectedness: patient.socialConnectedness,
        allergies: patient.allergies,
        chronicConditions: patient.chronicConditions,
        medicationTime: patient.medicationTime || [],
        showTestData: patient.showTestData,
        hypertensionMedications: patient.hypertensionMedications,
      }

      Object.entries(fieldMappings).forEach(([field, value]) => {
        setFieldValue(field, value)
      })
    }
    setIsLoading(false)
  }, [open, patientId])

  return (
    <Dialog
      open={open}
      title={'Edit Patient'}
      handleClose={closeHandler}
      actions={
        <>
          <Button variant="secondary" onClick={closeHandler}>
            Close
          </Button>
          {canDelete && (
            <Button variant="secondary" onClick={deleteHandler}>
              Delete
            </Button>
          )}
          <Button
            variant="primary"
            form={formId}
            type="submit"
            disabled={methods.formState.isSubmitting}
          >
            Submit
          </Button>
        </>
      }
    >
      <FormProvider {...methods}>
        <form
          className={classes.form}
          id={formId}
          noValidate
          onSubmit={methods.handleSubmit(onSubmit)}
        >
          <PatientForm
            addClinic={canEditClinic}
            clinics={clinics}
            timezoneRequired={timezoneRequired}
            isLoading={isLoading}
            showWithings={true}
          />
        </form>
      </FormProvider>
      <p>{serverMessage}</p>
    </Dialog>
  )
}
