import { useFormStyles } from '../components/common/style'
import { Controller, useFormContext } from 'react-hook-form'
import { useState } from 'react'
import { LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import Typography from '@material-ui/core/Typography'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import dayjs from 'dayjs'
import TextField from '@material-ui/core/TextField'
import { IconButton, InputAdornment } from '@mui/material'
import CalendarTodayIcon from '@mui/icons-material/CalendarToday'
import MenuItem from '@material-ui/core/MenuItem'
import { genders } from './formOptions/genders'
import { ethnicities } from './formOptions/ethnicities'
import { useCustomStyles } from './EditPatientForm'

export const PersonalInformation = () => {
  const classes = useCustomStyles()
  const formClasses = useFormStyles()
  const { control, setValue } = useFormContext()
  const [open, setOpen] = useState(false)
  return (
    <>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Typography className={classes.stepLabel}>
          Step 1. Personal information
        </Typography>

        <div className={classes.datePickerWrapper}>
          <Controller
            name="birthdate"
            control={control}
            rules={{ required: 'Date of birth is required' }}
            render={({ field: { value, onChange }, fieldState }) => (
              <DatePicker
                open={open}
                onOpen={() => setOpen(true)}
                onClose={() => setOpen(false)}
                value={value ? dayjs(value) : null}
                format="MM/DD/YYYY"
                disableFuture
                onChange={(date) => {
                  if (date) {
                    setValue('birthdate', date.format('YYYY-MM-DD'), {
                      shouldValidate: true,
                    })
                  }
                }}
                slots={{ textField: TextField }}
                slotProps={{
                  textField: {
                    label: 'Date of birth',
                    variant: 'outlined',
                    fullWidth: true,
                    placeholder: 'MM/DD/YYYY',
                    error: !!fieldState.error,
                    helperText: fieldState.error?.message,
                    className: classes.dateInput,
                    InputLabelProps: { shrink: true },
                    InputProps: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton edge="end" onClick={() => setOpen(true)}>
                            <CalendarTodayIcon />
                          </IconButton>
                        </InputAdornment>
                      ),
                    },
                    FormHelperTextProps: {
                      style: {
                        margin: '8px 0 0 0',
                        visibility: fieldState.error ? 'visible' : 'hidden',
                        position: 'static',
                      },
                    },
                  },
                }}
                PopperProps={{
                  className: classes.datePickerPopper,
                  placement: 'bottom-start',
                  disablePortal: true,
                }}
              />
            )}
          />
        </div>
      </LocalizationProvider>

      <div className={classes.fieldGroup}>
        <Controller
          name="gender"
          control={control}
          rules={{
            required: 'Gender is required',
          }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              select
              variant="outlined"
              fullWidth
              label="Gender"
              value={value || ''}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={formClasses.element}
              InputLabelProps={{
                shrink: true,
              }}
              SelectProps={{
                displayEmpty: true,
                renderValue: (selected) => {
                  if (!selected) {
                    return (
                      <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                        Choose your gender...
                      </span>
                    )
                  }
                  return selected
                },
              }}
            >
              <MenuItem value="" disabled>
                Choose your gender...
              </MenuItem>
              {genders.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </div>

      <div className={classes.fieldGroup}>
        <Controller
          name="ethnicity"
          control={control}
          rules={{
            required: 'Ethnicity is required',
          }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              select
              variant="outlined"
              fullWidth
              label="Ethnicity"
              value={value || ''}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={formClasses.element}
              InputLabelProps={{
                shrink: true,
              }}
              SelectProps={{
                displayEmpty: true,
                renderValue: (selected) => {
                  if (!selected) {
                    return (
                      <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                        Choose your ethnicity...
                      </span>
                    )
                  }
                  return selected
                },
              }}
            >
              <MenuItem value="" disabled>
                Choose your ethnicity...
              </MenuItem>
              {ethnicities.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </div>

      <div className={classes.fieldGroup}>
        <Controller
          name="zip"
          control={control}
          rules={{
            required: 'Zip code is required',
            pattern: {
              value: /^\d{5}(-\d{4})?$/,
              message: 'Please enter a valid zip code',
            },
          }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              variant="outlined"
              fullWidth
              label="Zip code"
              placeholder="Enter your zip code"
              value={value || ''}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={formClasses.element}
              InputLabelProps={{
                shrink: true,
              }}
            />
          )}
        />
      </div>

      <div className={classes.fieldGroup}>
        <Controller
          name="email"
          control={control}
          rules={{
            required: 'Email is required',
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'Please enter a valid email address',
            },
          }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              variant="outlined"
              fullWidth
              label="E-mail"
              placeholder="Enter your E-mail..."
              value={value || ''}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={formClasses.element}
              InputLabelProps={{
                shrink: true,
              }}
            />
          )}
        />
      </div>
    </>
  )
}
