import { Controller, useFormContext } from 'react-hook-form'
import { TextField } from '@material-ui/core'

export const PatientTextField = ({
  name,
  label,
  rules = {},
  disabled = false,
  isMobile,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext()
  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      render={({ field: { name, value, onChange } }) => (
        <TextField
          variant="outlined"
          margin="normal"
          required={!!rules.required}
          style={
            !isMobile ? { marginLeft: '15px', width: '45%' } : { width: '100%' }
          }
          label={label}
          name={name}
          value={value}
          autoFocus
          onChange={(event) => {
            if (name === 'cellNumber') {
              const inputValue = event.target.value
              if (!inputValue.startsWith('+1') && inputValue.length > 0) {
                onChange('+1' + inputValue.replace('+', ''))
              } else {
                onChange(inputValue)
              }
            } else {
              onChange(event)
            }
          }}
          onFocus={(event) => {
            if (name === 'cellNumber' && (!value || value === '')) {
              onChange('+1')
            }
          }}
          onKeyDown={(event) => {
            if (
              name === 'cellNumber' &&
              (event.key === 'Backspace' || event.key === 'Delete') &&
              value === '+1'
            ) {
              event.preventDefault()
              onChange('')
            }
          }}
          onBlur={(event) => {
            if (name === 'cellNumber' && value === '+1') {
              onChange('')
            }
          }}
          error={!!errors[name]}
          helperText={errors[name]?.message}
          disabled={disabled}
        />
      )}
    />
  )
}
