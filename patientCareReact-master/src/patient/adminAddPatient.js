export const adminAddPatient = async (data, adminID) => {
  const inputValue = {
    patientFirstName: data.firstName,
    patientLastName: data.lastName,
    patientMRN: data.mrn,
    patientEmail: data.email,
    patientHomeNumber: data.homeNumber,
    patientCellNumber: data.cellNumber,
    patientCity: data.city,
    patientState: data.state,
    patientAddress: data.address,
    patientZip: data.zip,
    patientTimeZone: data.timezone,
    patientBPIMEI: data.bpImei,
    patientTTBpIMEI: data.transtekBpImei,
    patientAdBpIMEI: data.adBpImei,
    patientWeightIMEI: data.weightImei,
    patientTTWeightIMEI: data.transtekWeightImei,
    patientGlucoseIMEI: data.glucoseImei,
    patientPulseIMEI: data.pulseImei,
    patientClinic: data.clinic,
    selectedBpDevice: data.selectedBpDevice,
    selectedWeightDevice: data.selectedWeightDevice,
    targetWeight: data.targetWeight,
    birthdate: data.birthdate,
    gender: data.gender,
    ethnicity: data.ethnicity,
    maritalStatus: data.maritalStatus,
    education: data.education,
    employment: data.employment,
    income: data.income,
    language: data.language,
    socialConnectedness: data.socialConnectedness,
    allergies: data.allergies,
    chronicConditions: data.chronicConditions,
    hypertensionMedications: data.hypertensionMedications,
    adminID,
  }

  return fetch('/routes/users/addPatient', {
    method: 'POST',
    body: JSON.stringify(inputValue),
    headers: { 'Content-Type': 'application/json' },
  })
    .then((response) => {
      if (response.ok) {
        return response.json()
      }
      return {
        error: 'error',
        status: response.status,
        statusText: response.statusText,
      }
    })
    .catch((error) => {
      console.error(error)
      return { error }
    })
}
