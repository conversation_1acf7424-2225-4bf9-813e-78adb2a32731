import { Controller, useFormContext } from 'react-hook-form'
import {
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  FormHelperText,
  OutlinedInput,
} from '@mui/material'
import { useStyles } from '../components/common/style'

export const PatientSelect = ({
  name,
  label,
  id,
  items = [],
  rules = {},
  isMobile,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext()

  const classes = useStyles()
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field: { name, value, onChange } }) => (
        <FormControl
          className={classes.formControl}
          required={!!rules.required}
          style={{
            marginLeft: !isMobile ? '15px' : '0',
            marginTop: '16px',
            marginBottom: !isMobile ? '0' : '8px',
            marginRight: '0px',
            width: !isMobile ? '45%' : '100%',
          }}
          error={errors[name]}
        >
          <InputLabel id={`${id}-select-label`}>{label}</InputLabel>
          <Select
            labelId={`${id}-select-label-id`}
            id={`${id}-select-id`}
            input={<OutlinedInput label={label} />}
            name={name}
            value={value}
            onChange={onChange}
          >
            {items.map((item, i) => (
              <MenuItem key={i} value={item.value}>
                {item.label}
              </MenuItem>
            ))}
          </Select>
          {errors[name] && (
            <FormHelperText>{errors[name]?.message}</FormHelperText>
          )}
        </FormControl>
      )}
    />
  )
}
