export const adminSavePatientChanges = async (data, adminID) => {
  const patient = {
    id: data.id,
    firstName: data.firstName,
    lastName: data.lastName,
    cellNumber: data.cellNumber,
    homeNumber: data.homeNumber,
    MRN: data.mrn,
    email: data.email,
    address: data.address,
    city: data.city,
    state: data.state,
    zip: data.zip,
    timeZone: data.timezone,
    bpIMEI: data.bpImei,
    ttBpIMEI: data.transtekBpImei,
    adBpIMEI: data.adBpImei,
    weightIMEI: data.weightImei,
    ttWeightIMEI: data.transtekWeightImei,
    pulseIMEI: data.pulseImei,
    glucoseIMEI: data.glucoseImei,
    clinic: data.clinic,
    selectedBpDevice: data.selectedBpDevice,
    selectedWeightDevice: data.selectedWeightDevice,
    deviceNotificationsEnabled: data.deviceNotificationsEnabled,
    targetWeight: data.targetWeight,
    birthdate: data.birthdate,
    gender: data.gender,
    ethnicity: data.ethnicity,
    maritalStatus: data.maritalStatus,
    education: data.education,
    employment: data.employment,
    income: data.income,
    language: data.language,
    socialConnectedness: data.socialConnectedness,
    allergies: data.allergies,
    chronicConditions: data.chronicConditions,
    hypertensionMedications: data.hypertensionMedications,
    adminID,
    showTestData: data.showTestData,
  }

  return await fetch('/routes/users/savePatientChanges', {
    method: 'POST',
    body: JSON.stringify(patient),
    headers: { 'Content-Type': 'application/json' },
  })
    .then((response) => {
      if (response.ok) {
        return response.json()
      }
      return {
        error: 'error',
        status: response.status,
        statusText: response.statusText,
      }
    })
    .catch((error) => {
      console.error(error)
      return { error }
    })
}
