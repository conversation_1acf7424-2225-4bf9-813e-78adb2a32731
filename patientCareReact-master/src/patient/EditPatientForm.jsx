import { useId, useState, useEffect, useRef } from 'react'
import { useForm, FormProvider, Controller } from 'react-hook-form'
import Button from '@material-ui/core/Button'
import Typography from '@material-ui/core/Typography'
import { useResponseState } from '../common/useResponsiveState'
import { useFormContext } from 'react-hook-form'
import { makeStyles } from '@material-ui/core/styles'
import TextField from '@material-ui/core/TextField'
import MenuItem from '@material-ui/core/MenuItem'
import CalendarTodayIcon from '@mui/icons-material/CalendarToday'
import { genders } from './formOptions/genders'
import { ethnicities } from './formOptions/ethnicities'
import { chronicConditions } from './formOptions/chronicConditions'
import { bloodPressureMedications } from './formOptions/bloodPressureMedications'
import { allergens } from './formOptions/allergens'
import { IconButton, InputAdornment } from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { LocalizationProvider } from '@mui/x-date-pickers'
import dayjs from 'dayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { getPatientByImei } from '../services/patient'
import { Dialog, DialogTitle, DialogContent, Box } from '@material-ui/core'
import Picker from 'react-mobile-picker-scroll'

const useCustomStyles = makeStyles((theme) => ({
  formWrapper: {
    display: 'flex',
    width: 'min(90%, 648px)',
    maxWidth: '648px',
    minWidth: '320px',
    padding: '16px 12px',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '24px',
    borderRadius: '8px',
    background: '#FFF',
    boxShadow: '0px 9px 44px 8px rgba(0, 0, 0, 0.12)',
    margin: '32px auto',
    '@media (min-width: 480px)': {
      padding: '20px 16px',
      gap: '28px',
    },
    '@media (min-width: 768px)': {
      width: '648px',
      padding: '32px',
      gap: '32px',
    },
  },
  form: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    '& .MuiInputBase-input::placeholder': {
      color: 'rgba(0, 0, 0, 0.60)',
      opacity: 1,
    },
  },
  headerContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '8px',
    alignSelf: 'stretch',
    width: '100%',
    marginBottom: '24px',
    padding: '0 8px',
    '@media (min-width: 768px)': {
      gap: '16px',
      marginBottom: '32px',
      padding: 0,
    },
  },
  title: {
    color: 'rgba(0, 0, 0, 0.87)',
    textAlign: 'center',
    fontSize: '22px',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: '133.4%',
    '@media (min-width: 768px)': {
      fontSize: '34px',
    },
  },
  subtitle: {
    color: 'rgba(0, 0, 0, 0.6)',
    textAlign: 'center',
    fontSize: '16px',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: '150%',
    letterSpacing: '0.15px',
    maxWidth: '584px',
    display: 'none',
    '@media (min-width: 768px)': {
      display: 'block',
    },
  },
  stepLabel: {
    color: '#5C6BC0',
    fontSize: '16px',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: '150%',
    letterSpacing: '0.15px',
    width: '100%',
    marginBottom: '20px',
    '@media (min-width: 480px)': {
      fontSize: '18px',
      marginBottom: '24px',
    },
    '@media (min-width: 768px)': {
      fontSize: '20px',
      marginBottom: '32px',
    },
  },
  fieldGroup: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    minHeight: '56px',
    marginBottom: '20px',
    '& .MuiFormHelperText-root': {
      margin: '8px 0 0 0',
      fontSize: '12px',
      lineHeight: '1.66',
      letterSpacing: '0.03333em',
      textAlign: 'left',
    },
    '&:last-child': {
      marginBottom: 0,
    },
    '@media (min-width: 768px)': {
      marginBottom: '24px',
    },
  },
  fieldLabel: {
    color: 'rgba(0, 0, 0, 0.6)',
    fontSize: '12px',
    fontWeight: 400,
    lineHeight: '166%',
    letterSpacing: '0.4px',
  },
  input: {
    '& .MuiOutlinedInput-root': {
      borderRadius: '8px',
      backgroundColor: '#fff',
      height: '56px',
    },
    '& .MuiOutlinedInput-input': {
      padding: '16px 12px',
      fontSize: '16px',
      color: 'rgba(0, 0, 0, 0.87)',
      '&::placeholder': {
        color: 'rgba(0, 0, 0, 0.6)',
        opacity: 1,
      },
    },
    '& .MuiSelect-select': {
      padding: '16px 14px',
      fontSize: '16px',
      color: 'rgba(0, 0, 0, 0.87)',
    },
    '& .MuiInputLabel-outlined': {
      transform: 'translate(14px, -6px) scale(0.75)',
      color: 'rgba(0, 0, 0, 0.6)',
    },
    '& .MuiInputLabel-outlined.MuiInputLabel-shrink': {
      transform: 'translate(14px, -6px) scale(0.75)',
    },
    '& .MuiFormControl-root': {
      margin: 0,
      height: '56px',
      padding: '0 20px !important',
    },
    '& .MuiFormLabel-root': {
      lineHeight: 'normal',
    },
  },
  buttonContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
    width: '100%',
    marginTop: '24px',
    '@media (min-width: 768px)': {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: '32px',
      gap: '16px',
      '& button': {
        width: 'auto',
      },
    },
  },
  button: {
    display: 'flex',
    padding: '8px 22px',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'stretch',
    borderRadius: '4px',
    textTransform: 'uppercase',
    fontSize: '14px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '26px',
    letterSpacing: '0.46px',
    boxShadow:
      '0px 1px 5px 0px rgba(0, 0, 0, 0.12), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 3px 1px -2px rgba(0, 0, 0, 0.20)',
    '&:focus': {
      outline: 'none',
    },
    '@media (min-width: 768px)': {
      fontSize: '15px',
    },
  },
  primaryButton: {
    backgroundColor: '#3F51B5',
    color: '#FFF',
    '&:hover': {
      backgroundColor: '#303F9F',
    },
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    color: '#5C6BC0',
    border: '1px solid rgba(156, 39, 176, 0.50)',
    '&:hover': {
      backgroundColor: 'rgba(92, 107, 192, 0.1)',
    },
  },
  largeSelect: {
    '& .MuiOutlinedInput-root': {
      minHeight: '56px',
      height: 'auto',
    },
    '& .MuiSelect-select': {
      minHeight: '24px',
      padding: '16px 14px',
      fontSize: '16px',
      fontStyle: 'normal',
      fontWeight: 400,
      lineHeight: '24px',
      letterSpacing: '0.15px',
      color: 'black',
    },
    '& .MuiOutlinedInput-input': {
      maxHeight: '120px',
    },
  },
  customIcon: {
    cursor: 'pointer',
    color: 'rgba(0, 0, 0, 0.56)',
  },
  datePickerWrapper: {
    width: '100%',
    position: 'relative',
    marginBottom: '20px',
    '@media (min-width: 768px)': {
      marginBottom: '24px',
    },
  },
  datePickerPopper: {
    '&.MuiPopper-root': {
      width: 'auto !important',
    },
    '& .MuiPaper-root': {
      width: '100% !important',
    },
  },
  dateInput: {
    width: '100% !important',
    '@media (min-width: 768px)': {
      width: '100% !important',
    },
  },
  datePickerError: {
    '& .MuiFormHelperText-root': {
      marginLeft: '0 !important',
      paddingLeft: '0 !important',
      '&.MuiFormHelperText-contained': {
        marginLeft: '0 !important',
        paddingLeft: '0 !important',
      },
    },
  },
  selectMenu: {
    '& .MuiPaper-root': {
      position: 'absolute',
      marginTop: '8px',
      width: 'var(--select-width) !important',
      boxSizing: 'border-box',
      '@media (min-width: 768px)': {
        maxHeight: '328px',
        overflowY: 'auto',
        borderRadius: '8px',
        boxShadow:
          '0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)',
      },
    },
  },
  chipsContainer: {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '8px',
    padding: '4px 0',
    maxWidth: '100%',
  },
  chip: {
    background: '#F5F5F5',
    padding: '4px 8px',
    borderRadius: '16px',
    fontSize: '14px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    maxWidth: '100%',
  },
}))

const PersonalInformationStep = () => {
  const classes = useCustomStyles()
  const { control, setValue } = useFormContext()
  const [open, setOpen] = useState(false)
  const dateInputRef = useRef(null)
  const genderFieldRef = useRef(null)
  const ethnicityFieldRef = useRef(null)

  useEffect(() => {
    const setSelectWidth = (ref, selector) => {
      if (ref.current) {
        const field = ref.current.querySelector(selector)
        if (field) {
          const width = field.offsetWidth
          document.documentElement.style.setProperty(
            '--select-width',
            `${width}px`
          )
        }
      }
    }

    const handleGenderOpen = () => {
      setSelectWidth(genderFieldRef, '.MuiOutlinedInput-root')
    }

    const handleEthnicityOpen = () => {
      setSelectWidth(ethnicityFieldRef, '.MuiOutlinedInput-root')
    }
    const genderSelect =
      genderFieldRef.current?.querySelector('div[role="button"]')
    const ethnicitySelect =
      ethnicityFieldRef.current?.querySelector('div[role="button"]')

    if (genderSelect) {
      genderSelect.addEventListener('mousedown', handleGenderOpen)
    }
    if (ethnicitySelect) {
      ethnicitySelect.addEventListener('mousedown', handleEthnicityOpen)
    }
    return () => {
      if (genderSelect) {
        genderSelect.removeEventListener('mousedown', handleGenderOpen)
      }
      if (ethnicitySelect) {
        ethnicitySelect.removeEventListener('mousedown', handleEthnicityOpen)
      }
    }
  }, [])

  const handleDatePickerOpen = () => {
    setOpen(true)

    setTimeout(() => {
      const datePicker = document.querySelector('.MuiPickersPopper-root')
      const dateInput =
        dateInputRef.current.querySelector('input').parentElement.parentElement

      if (datePicker && dateInput) {
        const inputWidth = dateInput.offsetWidth

        datePicker.style.width = `${inputWidth}px`
        datePicker.style.maxWidth = `${inputWidth}px`

        const paperElement = datePicker.querySelector('.MuiPaper-root')
        if (paperElement) {
          paperElement.style.width = '100%'
          paperElement.style.maxWidth = '100%'
        }

        const selectors = [
          '.MuiPickersLayout-root',
          '.MuiDateCalendar-root',
          '.MuiYearCalendar-root',
          '.MuiMonthCalendar-root',
          '.MuiDayCalendar-header',
          '.MuiDayCalendar-weekContainer',
        ]

        selectors.forEach((selector) => {
          const elements = datePicker.querySelectorAll(selector)
          elements.forEach((element) => {
            element.style.width = '100%'
            element.style.maxWidth = '100%'
          })
        })
      }
    }, 50)
  }

  return (
    <>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Typography className={classes.stepLabel}>
          Step 1. Personal information
        </Typography>

        <div className={classes.datePickerWrapper} ref={dateInputRef}>
          <Controller
            name="birthdate"
            control={control}
            rules={{ required: 'Date of birth is required' }}
            render={({ field: { value, onChange }, fieldState }) => (
              <DatePicker
                open={open}
                onOpen={handleDatePickerOpen}
                onClose={() => setOpen(false)}
                value={value ? dayjs(value) : null}
                format="MM/DD/YYYY"
                disableFuture
                onChange={(date) => {
                  if (date) {
                    const formattedDate = date.format('YYYY-MM-DD')
                    setValue('birthdate', formattedDate)
                    onChange(formattedDate)
                  } else {
                    setValue('birthdate', null, { shouldValidate: true })
                    onChange(null)
                  }
                }}
                slots={{ textField: TextField }}
                slotProps={{
                  textField: {
                    label: 'Date of birth',
                    variant: 'outlined',
                    fullWidth: true,
                    placeholder: 'MM/DD/YYYY',
                    error: !!fieldState.error,
                    helperText: fieldState.error?.message,
                    className: `${classes.dateInput} ${classes.datePickerError}`,
                    InputLabelProps: { shrink: true },
                    InputProps: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            edge="end"
                            onClick={() => handleDatePickerOpen()}
                            sx={{
                              '&:focus': {
                                outline: 'none',
                              },
                              '&:focus-visible': {
                                outline: 'none',
                              },
                              '&.Mui-focused': {
                                outline: 'none',
                              },
                            }}
                          >
                            <CalendarTodayIcon />
                          </IconButton>
                        </InputAdornment>
                      ),
                    },
                  },
                }}
                PopperProps={{
                  placement: 'bottom-start',
                  disablePortal: true,
                  modifiers: [
                    {
                      name: 'preventOverflow',
                      enabled: true,
                      options: {
                        altAxis: true,
                        altBoundary: true,
                        tether: false,
                        rootBoundary: 'document',
                        padding: 8,
                      },
                    },
                  ],
                  style: {
                    width: '100%',
                    maxWidth: '100%',
                  },
                }}
                sx={{
                  width: '100%',
                }}
                showDaysOutsideCurrentMonth
                desktopModeMediaQuery="@media (min-width: 0px)"
              />
            )}
          />
        </div>
      </LocalizationProvider>

      <div className={classes.fieldGroup} ref={genderFieldRef}>
        <Controller
          name="gender"
          control={control}
          rules={{
            required: false,
          }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              select
              variant="outlined"
              fullWidth
              label="Gender"
              value={value || ''}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={classes.element}
              InputLabelProps={{
                shrink: true,
              }}
              SelectProps={{
                MenuProps: {
                  className: classes.selectMenu,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left',
                  },
                  getContentAnchorEl: null,
                },
                displayEmpty: true,
                renderValue: (selected) => {
                  if (!selected) {
                    return (
                      <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                        Choose your gender...
                      </span>
                    )
                  }
                  return selected
                },
              }}
            >
              <MenuItem value="" disabled>
                Choose your gender...
              </MenuItem>
              {genders.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </div>

      <div className={classes.fieldGroup} ref={ethnicityFieldRef}>
        <Controller
          name="ethnicity"
          control={control}
          rules={{
            required: false,
          }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              select
              variant="outlined"
              fullWidth
              label="Ethnicity"
              value={value || ''}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={classes.element}
              InputLabelProps={{
                shrink: true,
              }}
              SelectProps={{
                MenuProps: {
                  className: classes.selectMenu,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left',
                  },
                  getContentAnchorEl: null,
                },
                displayEmpty: true,
                renderValue: (selected) => {
                  if (!selected) {
                    return (
                      <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                        Choose your ethnicity...
                      </span>
                    )
                  }
                  return selected
                },
              }}
            >
              <MenuItem value="" disabled>
                Choose your ethnicity...
              </MenuItem>
              {ethnicities.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </div>

      <div className={classes.fieldGroup}>
        <Controller
          name="zip"
          control={control}
          rules={{
            required: false,
            pattern: {
              value: /^\d{5}(-\d{4})?$/,
              message: 'Please enter a valid zip code',
            },
          }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              variant="outlined"
              fullWidth
              label="Zip code"
              placeholder="Enter your zip code"
              value={value || ''}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={classes.element}
              InputLabelProps={{
                shrink: true,
              }}
            />
          )}
        />
      </div>

      <div className={classes.fieldGroup}>
        <Controller
          name="email"
          control={control}
          rules={{
            required: false,
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'Please enter a valid email address',
            },
          }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              variant="outlined"
              fullWidth
              label="E-mail"
              placeholder="Enter your E-mail..."
              value={value || ''}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={classes.element}
              InputLabelProps={{
                shrink: true,
              }}
            />
          )}
        />
      </div>
    </>
  )
}

const HealthInformationStep = ({ handleSubmit, handleSkip }) => {
  const classes = useCustomStyles()
  const {
    control,
    formState: { isValid },
  } = useFormContext()
  const chronicConditionsRef = useRef(null)
  const medicationsRef = useRef(null)
  const allergiesRef = useRef(null)
  const [isHeightPickerOpen, setIsHeightPickerOpen] = useState(false)
  const [tempHeight, setTempHeight] = useState({ feet: '5 ft', inches: '8 in' })
  const { setValue } = useFormContext()

  const handleHeightChange = (name, value) => {
    const numericValue = value.replace(/\D/g, '')
    setTempHeight((prev) => {
      if (prev[name].replace(/\D/g, '') === numericValue) return prev
      return {
        ...prev,
        [name]: `${numericValue} ${name === 'feet' ? 'ft' : 'in'}`,
      }
    })
  }

  const handleHeightSave = () => {
    setIsHeightPickerOpen(false)
    const feet = parseInt(tempHeight.feet) || 0
    const inches = parseInt(tempHeight.inches) || 0
    const totalInches = feet * 12 + inches
    setValue('height', totalInches.toString())
  }

  const handleHeightCancel = () => {
    setIsHeightPickerOpen(false)
  }

  return (
    <>
      <Typography className={classes.stepLabel}>
        Step 2. Health information
      </Typography>

      <div className={classes.fieldGroup}>
        <Controller
          name="weight"
          control={control}
          rules={{
            required: false,
            pattern: {
              value: /^\d+$/,
              message: 'Please enter numbers only',
            },
          }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              variant="outlined"
              fullWidth
              label="Weight, Lbs"
              placeholder="Enter your weight..."
              value={value || ''}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={classes.element}
              InputLabelProps={{
                shrink: true,
              }}
            />
          )}
        />
      </div>

      <div className={classes.fieldGroup}>
        <Controller
          name="height"
          control={control}
          rules={{
            required: false,
            pattern: {
              value: /^\d+$/,
              message: 'Please enter numbers only',
            },
          }}
          render={({ field: { name, value }, fieldState: { error } }) => {
            const displayValue = value
              ? `${Math.floor(parseInt(value) / 12)}' ${parseInt(value) % 12}"`
              : ''
            return (
              <TextField
                variant="outlined"
                fullWidth
                label="Height"
                placeholder="Select your height"
                value={displayValue}
                name={name}
                onClick={() => setIsHeightPickerOpen(true)}
                error={!!error}
                helperText={error?.message}
                FormHelperTextProps={{
                  style: {
                    margin: '8px 0 0 0',
                    visibility: error ? 'visible' : 'hidden',
                    position: 'static',
                  },
                }}
                className={classes.element}
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  readOnly: true,
                }}
              />
            )
          }}
        />
      </div>

      <Dialog open={isHeightPickerOpen} onClose={handleHeightCancel}>
        <DialogTitle>Select Height</DialogTitle>
        <DialogContent>
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            height={200}
            padding="0 40px"
            style={{
              width: 240,
              overflowY: 'auto',
              cursor: 'drag',
              userSelect: 'none',
              scrollbarWidth: 'thin',
              msOverflowStyle: 'auto',
            }}
            onMouseDown={(e) => (e.currentTarget.style.cursor = 'grabbing')}
            onMouseUp={(e) => (e.currentTarget.style.cursor = 'grab')}
          >
            <Picker
              valueGroups={{
                feet: tempHeight.feet,
                inches: tempHeight.inches,
              }}
              optionGroups={{
                feet: Array.from({ length: 8 }, (_, i) => `${i + 3} ft`),
                inches: Array.from({ length: 12 }, (_, i) => `${i} in`),
              }}
              onChange={handleHeightChange}
              height={200}
              itemHeight={36}
              wheelMode="natural"
              style={{ width: 120 }}
            />
          </Box>
          <Box display="flex" justifyContent="flex-end" marginTop={2}>
            <Button onClick={handleHeightCancel}>Cancel</Button>
            <Button
              onClick={handleHeightSave}
              color="primary"
              variant="contained"
              style={{ marginLeft: 8 }}
            >
              Save
            </Button>
          </Box>
        </DialogContent>
      </Dialog>

      <div className={classes.fieldGroup} ref={chronicConditionsRef}>
        <Controller
          name="chronicConditions"
          control={control}
          rules={{ required: false }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              select
              variant="outlined"
              fullWidth
              label="Chronic Conditions"
              value={value || []}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={`${classes.element} ${classes.largeSelect}`}
              InputLabelProps={{
                shrink: true,
              }}
              SelectProps={{
                multiple: true,
                MenuProps: {
                  className: classes.selectMenu,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left',
                  },
                  getContentAnchorEl: null,
                },
                displayEmpty: true,
                renderValue: (selected) => {
                  if (!selected?.length) {
                    return (
                      <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                        Choose your Chronic Conditions
                      </span>
                    )
                  }
                  return (
                    <div className={classes.chipsContainer}>
                      {selected.map((value) => (
                        <div key={value} className={classes.chip}>
                          {value}
                        </div>
                      ))}
                    </div>
                  )
                },
              }}
            >
              <MenuItem value="" disabled>
                Choose your Chronic Conditions
              </MenuItem>
              {chronicConditions.map((condition) => (
                <MenuItem key={condition} value={condition}>
                  {condition}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </div>

      <div className={classes.fieldGroup} ref={medicationsRef}>
        <Controller
          name="hypertensionMedications"
          control={control}
          rules={{ required: false }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              select
              variant="outlined"
              fullWidth
              label="Medications"
              value={value || []}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={`${classes.element} ${classes.largeSelect}`}
              InputLabelProps={{
                shrink: true,
              }}
              SelectProps={{
                multiple: true,
                MenuProps: {
                  className: classes.selectMenu,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left',
                  },
                  getContentAnchorEl: null,
                },
                displayEmpty: true,
                renderValue: (selected) => {
                  if (!selected?.length) {
                    return (
                      <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                        Choose your Blood Pressure Medications
                      </span>
                    )
                  }
                  return (
                    <div className={classes.chipsContainer}>
                      {selected.map((value) => (
                        <div key={value} className={classes.chip}>
                          {value}
                        </div>
                      ))}
                    </div>
                  )
                },
              }}
            >
              <MenuItem value="" disabled>
                Choose your Blood Pressure Medications
              </MenuItem>
              {bloodPressureMedications.map((medication) => (
                <MenuItem key={medication} value={medication}>
                  {medication}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </div>

      <div className={classes.fieldGroup} ref={allergiesRef}>
        <Controller
          name="allergies"
          control={control}
          rules={{ required: false }}
          render={({
            field: { name, value, onChange },
            fieldState: { error },
          }) => (
            <TextField
              select
              variant="outlined"
              fullWidth
              label="Allergies"
              value={value || []}
              name={name}
              onChange={onChange}
              error={!!error}
              helperText={error?.message}
              FormHelperTextProps={{
                style: {
                  margin: '8px 0 0 0',
                  visibility: error ? 'visible' : 'hidden',
                  position: 'static',
                },
              }}
              className={`${classes.element} ${classes.largeSelect}`}
              InputLabelProps={{
                shrink: true,
              }}
              SelectProps={{
                multiple: true,
                MenuProps: {
                  className: classes.selectMenu,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left',
                  },
                  getContentAnchorEl: null,
                },
                displayEmpty: true,
                renderValue: (selected) => {
                  if (!selected?.length) {
                    return (
                      <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                        Choose your Allergies
                      </span>
                    )
                  }
                  return (
                    <div className={classes.chipsContainer}>
                      {selected.map((value) => (
                        <div key={value} className={classes.chip}>
                          {value}
                        </div>
                      ))}
                    </div>
                  )
                },
              }}
            >
              <MenuItem value="" disabled>
                Choose your Allergies
              </MenuItem>
              {allergens.map((allergen) => (
                <MenuItem key={allergen} value={allergen}>
                  {allergen}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </div>

      <div className={classes.buttonContainer}>
        <Button
          className={`${classes.button} ${classes.secondaryButton}`}
          onClick={handleSkip}
        >
          Skip
        </Button>
        <Button
          className={`${classes.button} ${classes.primaryButton}`}
          onClick={handleSubmit}
        >
          Save My Information
        </Button>
      </div>
    </>
  )
}

export const EditPatientForm = ({
  patient,
  editPatients,
  updatePatients,
  editDevices = true,
  isDeviceProfile,
  deviceImei,
  onComplete,
}) => {
  const formId = useId()
  const { smallMobile } = useResponseState()
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const [stepsSkipped, setStepsSkipped] = useState({
    step1: false,
    step2: false,
  })
  const [shouldShowForm, setShouldShowForm] = useState(null)
  const [patientData, setPatientData] = useState(null)
  const classes = useCustomStyles()

  // API functions for registration status
  const getPatientRegistrationStatus = async (patientId) => {
    try {
      const response = await fetch(
        `/routes/users/patient/${patientId}/registration-status`
      )
      if (response.ok) {
        const data = await response.json()
        return data.registrationCompleted
      }
      return false
    } catch (error) {
      console.error('Error getting registration status:', error)
      // Fallback to localStorage if API fails
      return localStorage.getItem('patientFormCompleted') === 'true'
    }
  }

  const updatePatientRegistrationStatus = async (patientId, completed) => {
    try {
      const response = await fetch(
        `/routes/users/patient/${patientId}/registration-status`,
        {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ registrationCompleted: completed }),
        }
      )
      if (response.ok) {
        // Also update localStorage as cache
        localStorage.setItem(
          'patientFormCompleted',
          completed ? 'true' : 'false'
        )
        return true
      }
      return false
    } catch (error) {
      console.error('Error updating registration status:', error)
      // Fallback to localStorage only if API fails
      localStorage.setItem('patientFormCompleted', completed ? 'true' : 'false')
      return false
    }
  }

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        if (deviceImei) {
          const response = await getPatientByImei(deviceImei)
          if (response && response.patient) {
            setPatientData(response.patient)
          }
        }
      } catch (error) {
        console.error('Error fetching patient data:', error)
      }
    }

    fetchPatientData()
  }, [deviceImei])

  useEffect(() => {
    const checkFormCompletion = async () => {
      if (patientData && patientData.id) {
        // Check registration status via API
        const isCompleted = await getPatientRegistrationStatus(patientData.id)
        if (isCompleted) {
          setShouldShowForm(false)
          onComplete?.(true)
        } else {
          setShouldShowForm(true)
        }
      } else {
        // Fallback to localStorage if no patient ID
        const isFormCompleted = localStorage.getItem('patientFormCompleted')
        if (isFormCompleted === 'true') {
          setShouldShowForm(false)
          onComplete?.(true)
        } else {
          setShouldShowForm(true)
        }
      }
      setIsLoading(false)
    }

    checkFormCompletion()
  }, [patientData])

  useEffect(() => {
    if (patientData) {
      const setFieldValue = (field, value) => {
        if (value !== undefined && value !== null) {
          methods.setValue(field, value)
        }
      }

      setFieldValue('birthdate', patientData.birthdate)
      setFieldValue('gender', patientData.gender)
      setFieldValue('ethnicity', patientData.ethnicity)
      setFieldValue('zip', patientData.patientZip || patientData.zip)
      setFieldValue('email', patientData.patientEmail || patientData.email)
      setFieldValue('weight', patientData.weight)
      setFieldValue('height', patientData.height)
      setFieldValue(
        'hypertensionMedications',
        patientData.hypertensionMedications
      )
      setFieldValue('chronicConditions', patientData.chronicConditions)
      setFieldValue('allergies', patientData.allergies)
    }
    setIsLoading(false)
  }, [patientData])

  const methods = useForm({
    mode: 'onChange',
    defaultValues: {
      birthdate: patientData?.birthdate || '',
      gender: patientData?.gender || '',
      ethnicity: patientData?.ethnicity || '',
      zip: patientData?.patientZip || patientData?.zip || '',
      email: patientData?.patientEmail || patientData?.email || '',
      weight: patientData?.weight || '',
      height: patientData?.height || '',
      chronicConditions: patientData?.chronicConditions || [],
      hypertensionMedications: patientData?.hypertensionMedications || [],
      allergies: patientData?.allergies || [],
    },
  })

  const handleSkip = async () => {
    if (currentStep === 1) {
      setStepsSkipped((prev) => ({ ...prev, step1: true }))
      setCurrentStep(2)
    } else {
      setStepsSkipped((prev) => ({ ...prev, step2: true }))
      const formData = methods.getValues()
      const localFormData = {}

      const step1Fields = ['birthdate', 'gender', 'ethnicity', 'zip', 'email']
      step1Fields.forEach((field) => {
        if (
          formData[field] &&
          formData[field].trim &&
          formData[field].trim() !== ''
        ) {
          localFormData[field] = formData[field]
          if (field === 'zip') localFormData.patientZip = formData[field]
          if (field === 'email') localFormData.patientEmail = formData[field]
        }
      })

      // Update registration status via API
      if (patientData && patientData.id) {
        await updatePatientRegistrationStatus(patientData.id, true)
      } else {
        localStorage.setItem('patientFormCompleted', 'true')
      }
      setShouldShowForm(false)
      onComplete?.(true, localFormData)
    }
  }

  const handleSubmit = async () => {
    if (currentStep === 1) {
      const isValid = await methods.trigger([
        'birthdate',
        'gender',
        'ethnicity',
        'zip',
        'email',
      ])

      if (isValid) {
        setCurrentStep(2)
      }
    } else {
      const isValid = await methods.trigger([
        'weight',
        'height',
        'chronicConditions',
        'hypertensionMedications',
        'allergies',
      ])

      if (isValid) {
        setStepsSkipped((prev) => ({ ...prev, step2: false }))
        await submitFormData(false)
        // Update registration status via API
        if (patientData && patientData.id) {
          await updatePatientRegistrationStatus(patientData.id, true)
        } else {
          localStorage.setItem('patientFormCompleted', 'true')
        }
        setShouldShowForm(false)
      }
    }
  }

  const submitFormData = async (isSkipped = false) => {
    const formData = methods.getValues()
    let isValid = true

    if (!isSkipped) {
      const fieldsToValidate =
        currentStep === 1
          ? ['birthdate', 'gender', 'ethnicity', 'zip', 'email']
          : [
              'weight',
              'height',
              'chronicConditions',
              'hypertensionMedications',
              'allergies',
            ]

      isValid = await methods.trigger(fieldsToValidate)
    }

    if (!isValid) return

    if (!patientData?._id) {
      console.error('No patient ID available')
      return
    }

    const dataToSubmit = {
      id: patientData._id,
      deviceImei,
    }

    const localFormData = {}

    if (formData.birthdate && formData.birthdate.trim() !== '') {
      dataToSubmit.birthdate = formData.birthdate
      localFormData.birthdate = formData.birthdate
    }

    if (formData.gender && formData.gender.trim() !== '') {
      dataToSubmit.gender = formData.gender
      localFormData.gender = formData.gender
    }

    if (formData.ethnicity && formData.ethnicity.trim() !== '') {
      dataToSubmit.ethnicity = formData.ethnicity
      localFormData.ethnicity = formData.ethnicity
    }

    if (formData.zip && formData.zip.trim() !== '') {
      dataToSubmit.zip = formData.zip
      localFormData.zip = formData.zip
    }

    if (formData.email && formData.email.trim() !== '') {
      dataToSubmit.email = formData.email
      localFormData.email = formData.email
    }

    if (formData.weight && formData.weight.trim() !== '') {
      dataToSubmit.weight = formData.weight
      localFormData.weight = formData.weight
    }

    if (formData.height && formData.height.trim() !== '') {
      dataToSubmit.height = formData.height
      localFormData.height = formData.height
    }

    const arrayFields = [
      'chronicConditions',
      'hypertensionMedications',
      'allergies',
    ]
    arrayFields.forEach((field) => {
      if (
        formData[field] &&
        Array.isArray(formData[field]) &&
        formData[field].length > 0
      ) {
        dataToSubmit[field] = formData[field]
        localFormData[field] = formData[field]
      }
    })

    try {
      setIsLoading(true)

      const response = await fetch('/routes/users/providerSavePatientChanges', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSubmit),
      })

      if (!response.ok) {
        throw new Error('Error sending data')
      }

      const responseData = await response.json()

      if (responseData && responseData.patient) {
        const updatedPatient = responseData.patient
        setPatientData(updatedPatient)

        methods.reset({
          birthdate: updatedPatient.birthdate || '',
          gender: updatedPatient.gender || '',
          ethnicity: updatedPatient.ethnicity || '',
          zip: updatedPatient.patientZip || updatedPatient.zip || '',
          email: updatedPatient.patientEmail || updatedPatient.email || '',
          weight: updatedPatient.weight || '',
          height: updatedPatient.height || '',
          chronicConditions: updatedPatient.chronicConditions || [],
          hypertensionMedications: updatedPatient.hypertensionMedications || [],
          allergies: updatedPatient.allergies || [],
        })
      }

      if (editPatients) {
        await updatePatients(dataToSubmit)
      }

      if (currentStep === 2) {
        // Update registration status via API
        if (patientData && patientData.id) {
          await updatePatientRegistrationStatus(patientData.id, true)
        } else {
          localStorage.setItem('patientFormCompleted', 'true')
        }
        setTimeout(() => {
          onComplete?.(false, localFormData)
        }, 300)
      }
    } catch (error) {
      console.error('Error when submitting form:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading || shouldShowForm === null) {
    return null
  }

  if (!shouldShowForm) {
    return null
  }

  return (
    <div className={classes.formWrapper}>
      <FormProvider {...methods}>
        <form className={classes.form} id={formId} noValidate>
          <div className={classes.headerContainer}>
            <Typography className={classes.title}>
              Enter your personal details
            </Typography>
            <Typography className={classes.subtitle}>
              Your information helps us offer care that's customized to you.
            </Typography>
          </div>

          {currentStep === 1 ? (
            <>
              <PersonalInformationStep />
              <div className={classes.buttonContainer}>
                <Button
                  className={`${classes.button} ${classes.secondaryButton}`}
                  onClick={handleSkip}
                >
                  Skip
                </Button>
                <Button
                  className={`${classes.button} ${classes.primaryButton}`}
                  onClick={handleSubmit}
                >
                  Next Step
                </Button>
              </div>
            </>
          ) : (
            <HealthInformationStep
              handleSubmit={handleSubmit}
              handleSkip={handleSkip}
            />
          )}
        </form>
      </FormProvider>
    </div>
  )
}
