import { Fragment } from 'react'
import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form'
import { FormControl, FormControlLabel, Switch } from '@material-ui/core'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import {
  Container,
  Skeleton,
  Typography,
  Box,
  Button,
  IconButton,
  TextField,
  MenuItem,
} from '@mui/material'
import { PatientTextField } from './PatientTextField'
import { useStyles } from '../components/common/style'
import { CARDIOWELL, TRANSTEK, WITHINGS, AD } from '../common/manufacters'
import { useTimezoneSelect, allTimezones } from 'react-timezone-select'
import { PatientMultipleSelectChip } from './PatientMultiSelectChip'
import { PatientSelect } from './PatientSelect'
import { chronicConditions } from './formOptions/chronicConditions'
import { allergens } from './formOptions/allergens'
import { maritalStatuses } from './formOptions/maritalStatuses'
import { education } from './formOptions/education'
import { employment } from './formOptions/employment'
import { languages } from './formOptions/languages'
import { genders } from './formOptions/genders'
import { ethnicities } from './formOptions/ethnicities'
import { incomes } from './formOptions/incomes'
import { connectedness } from './formOptions/connectedness'
import { bloodPressureMedications } from './formOptions/bloodPressureMedications'
import { useResponseState } from '../common/useResponsiveState'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AddMedicationIcon } from '../components/DeviceNotifications/Profile/Icons'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DesktopTimePicker } from '@mui/x-date-pickers/DesktopTimePicker'
import dayjs from 'dayjs'
import React, { useState } from 'react'

export const PatientForm = ({
  clinics,
  isLoading,
  timezoneRequired = false,
  addClinic = false,
  showWithings = false,
  editDevices = true,
  isDeviceProfile,
}) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useFormContext()
  const classes = useStyles()
  const { smallMobile } = useResponseState()

  const [editingMedicationIndex, setEditingMedicationIndex] = useState(null)
  const [selectedMedication, setSelectedMedication] = useState('')
  const [selectedTime, setSelectedTime] = useState(null)

  const selectedBpDevice = watch('selectedBpDevice')
  const selectedWeightDevice = watch('selectedWeightDevice')
  const { options: timeZoneOptions } = useTimezoneSelect({
    labelStyle: 'abbrev',
    allTimezones,
  })

  return (
    <Fragment>
      {isLoading ? (
        <Fragment>
          <Skeleton variant="rounded" width="100%" height={56} sx={{ mt: 2 }} />
          <Skeleton variant="rounded" width="100%" height={56} sx={{ mt: 2 }} />
          <Skeleton variant="rounded" width="100%" height={56} sx={{ mt: 2 }} />
          <Skeleton variant="rounded" width="100%" height={56} sx={{ mt: 2 }} />
          <Skeleton variant="rounded" width="100%" height={56} sx={{ mt: 2 }} />
          <Skeleton variant="rounded" width="100%" height={56} sx={{ mt: 2 }} />
          <Skeleton variant="rounded" width="100%" height={56} sx={{ mt: 2 }} />
          <Skeleton variant="rounded" width="100%" height={56} sx={{ mt: 2 }} />
        </Fragment>
      ) : isDeviceProfile ? (
        <Fragment>
          <Container style={{ padding: '0 0 32px 0' }}>
            <Container
              style={{
                display: 'flex',
                gap: 16,
                alignItems: 'center',
                padding: 0,
              }}
            >
              <Typography style={{ fontSize: 20, fontWeight: 500 }}>
                Personal information
              </Typography>
              <EditIcon
                fontSize="large"
                style={{
                  backgroundColor: '#E8EAF6',
                  padding: 5,
                  borderRadius: 100,
                }}
              />
            </Container>

            <PatientTextField
              name="firstName"
              label="First Name"
              rules={{
                required: 'Required field',
                maxLength: {
                  value: '30',
                  message: 'Maximum 30 characters allowed',
                },
              }}
              isMobile={smallMobile}
              required
            />
            <PatientTextField
              name="lastName"
              label="Last Name"
              rules={{ required: 'Required field' }}
              isMobile={smallMobile}
            />
            <Controller
              name="birthdate"
              control={control}
              render={({ field: { name, value, onChange } }) => (
                <FormControl
                  className={classes.formControl}
                  style={{
                    marginTop: '16px',
                    marginLeft: !smallMobile ? '15px' : '0',
                    marginRight: '0px',
                    width: !smallMobile ? '45%' : '100%',
                  }}
                  error={errors[name]}
                >
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      label="Date of Birth"
                      name={name}
                      value={value ? dayjs(value) : null}
                      onChange={(date) => {
                        if (date) {
                          onChange(date.format('YYYY-MM-DD'))
                        } else {
                          onChange(null)
                        }
                      }}
                      format="MM/DD/YYYY"
                      disableFuture
                      isMobile={smallMobile}
                    />
                  </LocalizationProvider>
                </FormControl>
              )}
            />
            <PatientSelect
              name="gender"
              label="Gender"
              id="gender"
              items={genders}
              isMobile={smallMobile}
            />
            <PatientTextField
              name="email"
              label="Email Address"
              isMobile={smallMobile}
            />
            <PatientTextField
              name="cellNumber"
              label="Cell Phone Number"
              isMobile={smallMobile}
            />
          </Container>
          <Container style={{ padding: '0 0 32px 0' }}>
            <Container
              style={{
                display: 'flex',
                gap: 16,
                alignItems: 'center',
                padding: 0,
              }}
            >
              <Typography style={{ fontSize: 20, fontWeight: 500 }}>
                Health information
              </Typography>
              <EditIcon
                fontSize="large"
                style={{
                  backgroundColor: '#E8EAF6',
                  padding: 5,
                  borderRadius: 100,
                }}
              />
            </Container>

            <PatientTextField
              name="weight"
              label="Weight, Lbs"
              isMobile={smallMobile}
            />
            <PatientTextField
              name="height"
              label="Height, Inch"
              isMobile={smallMobile}
            />
            <PatientMultipleSelectChip
              name="chronicConditions"
              label="Chronic Conditions"
              id="chronic-conditions"
              isMobile={smallMobile}
              items={chronicConditions}
            />
            <PatientMultipleSelectChip
              name="hypertensionMedications"
              label="Medications"
              id="hypertension-medications"
              isMobile={smallMobile}
              items={bloodPressureMedications}
            />
          </Container>
          <Container style={{ padding: '0' }}>
            <Container
              style={{
                display: 'flex',
                gap: 16,
                alignItems: 'center',
                padding: 0,
              }}
            >
              <Typography style={{ fontSize: 20, fontWeight: 500 }}>
                Adress information
              </Typography>
              <EditIcon
                fontSize="large"
                style={{
                  backgroundColor: '#E8EAF6',
                  padding: 5,
                  borderRadius: 100,
                }}
              />
            </Container>

            <PatientTextField
              name="country"
              label="Country"
              isMobile={smallMobile}
            />
            <PatientTextField name="city" label="City" isMobile={smallMobile} />
            <PatientTextField
              name="address"
              label="Address"
              isMobile={smallMobile}
            />
            <PatientTextField name="zip" label="Zip" isMobile={smallMobile} />
          </Container>
        </Fragment>
      ) : (
        <Fragment>
          <PatientTextField
            name="firstName"
            label="First Name"
            rules={{ required: 'Required field' }}
            isMobile={smallMobile}
            required
          />
          <PatientTextField
            name="lastName"
            label="Last Name"
            rules={{ required: 'Required field' }}
            isMobile={smallMobile}
          />
          <PatientTextField
            name="mrn"
            label="Medical Record #"
            isMobile={smallMobile}
          />
          <Controller
            name="birthdate"
            control={control}
            render={({ field: { name, value, onChange } }) => (
              <FormControl
                className={classes.formControl}
                style={{
                  marginTop: '16px',
                  marginLeft: !smallMobile ? '15px' : '0',
                  marginRight: '0px',
                  width: !smallMobile ? '45%' : '100%',
                }}
                error={errors[name]}
              >
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    label="Date of Birth"
                    name={name}
                    value={value ? dayjs(value) : null}
                    onChange={(date) => {
                      if (date) {
                        onChange(date.format('YYYY-MM-DD'))
                      } else {
                        onChange(null)
                      }
                    }}
                    format="MM/DD/YYYY"
                    disableFuture
                    isMobile={smallMobile}
                  />
                </LocalizationProvider>
              </FormControl>
            )}
          />
          <PatientSelect
            name="gender"
            label="Gender"
            id="gender"
            items={genders}
            isMobile={smallMobile}
          />
          <PatientSelect
            name="ethnicity"
            label="Ethnicity"
            id="ethnicity"
            items={ethnicities}
            isMobile={smallMobile}
          />
          <PatientSelect
            name="maritalStatus"
            label="Marital Status"
            id="marital-status"
            items={maritalStatuses}
            isMobile={smallMobile}
          />
          <PatientSelect
            name="education"
            label="Education"
            id="education"
            items={education}
            isMobile={smallMobile}
          />
          <PatientSelect
            name="employment"
            label="Employment"
            id="employment"
            items={employment}
            isMobile={smallMobile}
          />
          <PatientSelect
            name="income"
            label="Income"
            id="income"
            items={incomes}
            isMobile={smallMobile}
          />
          <PatientSelect
            name="language"
            label="Primary Language"
            id="language"
            items={languages}
            isMobile={smallMobile}
          />
          <PatientTextField
            name="phoneNumber"
            label="Home Phone Number"
            isMobile={smallMobile}
          />
          <PatientTextField
            name="cellNumber"
            label="Cell Phone Number"
            isMobile={smallMobile}
          />
          <PatientTextField
            name="email"
            label="Email Address"
            isMobile={smallMobile}
          />
          {editDevices && (
            <Fragment>
              <PatientSelect
                name="selectedBpDevice"
                label="Blood Pressure Device"
                id="bp-device"
                isMobile={smallMobile}
                items={[
                  { value: CARDIOWELL, label: 'Cardiowell' },
                  { value: TRANSTEK, label: 'TT Tele RPM' },
                  { value: AD, label: 'A&D' },
                  ...(showWithings
                    ? [{ value: WITHINGS, label: 'Withings' }]
                    : []),
                ]}
              />
              {selectedBpDevice === CARDIOWELL && (
                <PatientTextField
                  name="bpImei"
                  label="Blood Pressure IMEI"
                  isMobile={smallMobile}
                />
              )}
              {selectedBpDevice === TRANSTEK && (
                <PatientTextField
                  name="transtekBpImei"
                  label="Blood Pressure IMEI"
                />
              )}
              {selectedBpDevice === AD && (
                <PatientTextField
                  name="adBpImei"
                  label="Blood Pressure IMEI"
                  isMobile={smallMobile}
                />
              )}
              {selectedBpDevice === WITHINGS && (
                <PatientTextField
                  name="withingsBpDevice"
                  label="Blood Pressure IMEI"
                  rules={{ disabled: true }}
                  disabled={true}
                />
              )}
              <PatientSelect
                name="selectedWeightDevice"
                label="Weight Device"
                id="ws-device"
                isMobile={smallMobile}
                items={[
                  { value: CARDIOWELL, label: 'Cardiowell' },
                  { value: TRANSTEK, label: 'TT Tele RPM' },
                ]}
              />
              {selectedWeightDevice === CARDIOWELL && (
                <PatientTextField
                  name="weightImei"
                  label="Weight IMEI"
                  isMobile={smallMobile}
                />
              )}
              {selectedWeightDevice === TRANSTEK && (
                <PatientTextField
                  name="transtekWeightImei"
                  label="Weight IMEI"
                />
              )}
              <PatientTextField
                name="pulseImei"
                label="Pulse Oximeter IMEI"
                isMobile={smallMobile}
              />
              <PatientTextField
                name="glucoseImei"
                label="Glucose IMEI"
                isMobile={smallMobile}
              />
            </Fragment>
          )}
          <PatientTextField
            name="targetWeight"
            label="Target Weight"
            isMobile={smallMobile}
          />
          <PatientTextField
            name="address"
            label="Address"
            isMobile={smallMobile}
          />
          <PatientTextField name="city" label="City" isMobile={smallMobile} />
          <PatientTextField
            name="state"
            label="State (abbreviated)"
            isMobile={smallMobile}
          />
          <PatientTextField name="zip" label="Zip" isMobile={smallMobile} />
          <PatientSelect
            name="socialConnectedness"
            label="Social Connectedness"
            id="social-connectedness"
            items={connectedness}
            isMobile={smallMobile}
          />
          <PatientMultipleSelectChip
            name="allergies"
            label="Allergies"
            id="allergies"
            isMobile={smallMobile}
            items={allergens}
          />
          <PatientMultipleSelectChip
            name="chronicConditions"
            label="Chronic Conditions"
            id="chronic-conditions"
            isMobile={smallMobile}
            items={chronicConditions}
          />

          <PatientMultipleSelectChip
            name="hypertensionMedications"
            label="Medications"
            id="hypertension-medications"
            isMobile={smallMobile}
            items={bloodPressureMedications}
          />

          <Controller
            name="medicationTime"
            control={control}
            render={({ field: { value = [], onChange } }) => {
              const handleAdd = () => {
                if (!selectedMedication || !selectedTime) return
                const timeString = selectedTime.format('hh:mm A')
                const exists = value.some(
                  (item) =>
                    item.medication === selectedMedication &&
                    item.time === timeString
                )
                if (!exists) {
                  onChange([
                    ...value,
                    { medication: selectedMedication, time: timeString },
                  ])
                  setSelectedMedication('')
                  setSelectedTime(null)
                }
              }

              const handleEdit = (idx) => {
                setEditingMedicationIndex(idx)
                setSelectedMedication(value[idx].medication)
                setSelectedTime(dayjs(value[idx].time, 'hh:mm A'))
              }

              const handleSaveEdit = () => {
                if (editingMedicationIndex === null) return
                const timeString = selectedTime.format('hh:mm A')
                const updated = [...value]
                updated[editingMedicationIndex] = {
                  medication: selectedMedication,
                  time: timeString,
                }
                onChange(updated)
                setEditingMedicationIndex(null)
                setSelectedMedication('')
                setSelectedTime(null)
              }

              const handleDelete = (idx) => {
                const updated = [...value]
                updated.splice(idx, 1)
                onChange(updated)
              }

              const handleCancel = () => {
                setEditingMedicationIndex(null)
                setSelectedMedication('')
                setSelectedTime(null)
              }

              return (
                <Box mt={2} mb={2} px={{ xs: '0px', sm: '16px' }}>
                  <Box
                    display="flex"
                    gap={{ xs: '0px', sm: '8px' }}
                    mb={2}
                    flexDirection={{ xs: 'column', sm: 'row' }}
                    alignItems="flex-start"
                    width="100%"
                  >
                    <Box
                      display="flex"
                      flex={2}
                      gap={2}
                      minWidth={0}
                      width="100%"
                      flexDirection={{ xs: 'column', sm: 'row' }}
                    >
                      <TextField
                        select
                        label="Select Medication"
                        value={selectedMedication}
                        onChange={(e) => setSelectedMedication(e.target.value)}
                        variant="outlined"
                        size="huge"
                        fullWidth
                        sx={{
                          flex: 1,
                          maxWidth: { xs: '100%', sm: '200px' },
                          width: '100%',
                        }}
                      >
                        <MenuItem value="">Select Medication</MenuItem>
                        {Array.isArray(watch('hypertensionMedications')) &&
                          watch('hypertensionMedications').map((med) => (
                            <MenuItem key={med} value={med}>
                              {med}
                            </MenuItem>
                          ))}
                      </TextField>

                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 2,
                          flex: 1,
                          minWidth: 0,
                          width: '100%',
                        }}
                      >
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <DesktopTimePicker
                            label="Select Time"
                            value={selectedTime}
                            onChange={setSelectedTime}
                            ampm={true}
                            slotProps={{
                              textField: {
                                variant: 'outlined',
                                size: 'huge',
                                fullWidth: true,
                                style: { width: '100%' },
                              },
                            }}
                          />
                        </LocalizationProvider>
                        {editingMedicationIndex === null ? (
                          <IconButton
                            color="primary"
                            onClick={handleAdd}
                            disabled={!selectedMedication || !selectedTime}
                            sx={{
                              backgroundColor: '#e3f2fd',
                              '&:hover': { backgroundColor: '#bbdefb' },
                              alignSelf: 'center',
                              flex: '0 0 48px',
                              width: '48px',
                              height: '48px',
                            }}
                          >
                            <AddMedicationIcon style={{ color: '#3F51B5' }} />
                          </IconButton>
                        ) : (
                          <Box display="flex" gap={1} alignSelf="center">
                            <Button
                              variant="contained"
                              color="primary"
                              onClick={handleSaveEdit}
                              disabled={!selectedMedication || !selectedTime}
                            >
                              Save
                            </Button>
                            <Button variant="outlined" onClick={handleCancel}>
                              Cancel
                            </Button>
                          </Box>
                        )}
                      </Box>
                    </Box>
                  </Box>

                  <Box>
                    {value.length === 0 && (
                      <Typography color="textSecondary">
                        No medication times set
                      </Typography>
                    )}
                    {value.map((item, index) => {
                      const medication = item.medication
                      const time = item.time

                      return (
                        <Box
                          key={index}
                          sx={{
                            mb: 2,
                            width: '100%',
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              gap: 2,
                              alignItems: 'flex-start',
                              flexDirection: { xs: 'column', sm: 'row' },
                              flexWrap: { xs: 'nowrap', sm: 'wrap' },
                              justifyContent: 'space-between',
                            }}
                          >
                            <Box
                              sx={{
                                flex: 1,
                                minWidth: 0,
                                width: '100%',
                                maxWidth: { xs: '100%', sm: 200 },
                              }}
                            >
                              <TextField
                                fullWidth
                                name="hypertensionMedications"
                                variant="outlined"
                                label="Medications"
                                value={medication}
                                InputLabelProps={{
                                  shrink: true,
                                }}
                                InputProps={{
                                  readOnly: true,
                                  inputComponent: ({ ...props }) => (
                                    <Box
                                      component="div"
                                      sx={{
                                        backgroundColor: '#F1F1F1',
                                        borderRadius: '16px',
                                        padding: '4px 10px',
                                        margin: '17px 12px',
                                        display: 'inline-block',
                                        fontSize: '14px',
                                        whiteSpace: 'nowrap',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        width: '100%',
                                        maxWidth: '100%',
                                        border: 'none',
                                      }}
                                      {...props}
                                    >
                                      {medication}
                                    </Box>
                                  ),
                                }}
                              />
                            </Box>
                            <Box
                              sx={{
                                flex: 1,
                                minWidth: 0,
                                width: '100%',
                              }}
                            >
                              <Box
                                sx={{
                                  display: 'flex',
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  gap: 2,
                                  flex: 1,
                                  minWidth: 0,
                                  width: '100%',
                                }}
                              >
                                <TextField
                                  fullWidth
                                  name="time"
                                  variant="outlined"
                                  label="Select time"
                                  value={time}
                                  InputLabelProps={{
                                    shrink: true,
                                  }}
                                  InputProps={{
                                    readOnly: true,
                                  }}
                                >
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'space-between',
                                      width: '100%',
                                      height: '70px',
                                    }}
                                  >
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        fontSize: '14px',
                                        fontWeight: 400,
                                        color: 'rgba(0, 0, 0, 0.87)',
                                        width: '100%',
                                        margin: '17px 12px',
                                        height: '100px',
                                        maxHeight: '100%',
                                      }}
                                    >
                                      {time
                                        ? dayjs(time, 'hh:mm A').format(
                                            'hh:mm A'
                                          )
                                        : '08:00 AM'}
                                    </Typography>
                                  </Box>
                                </TextField>
                                <Box
                                  sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    flexShrink: 0,
                                    marginTop: '8px',
                                  }}
                                >
                                  <IconButton
                                    size="small"
                                    onClick={() => handleEdit(index)}
                                    sx={{
                                      padding: '4px',
                                      minWidth: '32px',
                                      height: '32px',
                                    }}
                                  >
                                    <EditIcon
                                      sx={{
                                        fontSize: 25,
                                        color: 'rgba(0, 0, 0, 0.54)',
                                      }}
                                    />
                                  </IconButton>
                                  <Typography
                                    style={{
                                      color: 'rgba(67, 67, 67, 0.52)',
                                      fontSize: '16px',
                                      fontWeight: 400,
                                    }}
                                  >
                                    |
                                  </Typography>
                                  <IconButton
                                    size="small"
                                    onClick={() => handleDelete(index)}
                                    sx={{
                                      padding: '4px',
                                      minWidth: '32px',
                                      height: '32px',
                                    }}
                                  >
                                    <DeleteIcon
                                      sx={{
                                        fontSize: 25,
                                        color: 'rgba(0, 0, 0, 0.54)',
                                      }}
                                    />
                                  </IconButton>
                                </Box>
                              </Box>
                            </Box>
                          </Box>
                        </Box>
                      )
                    })}
                  </Box>
                </Box>
              )
            }}
          />

          <PatientSelect
            name="timezone"
            label="Time Zone"
            id="timezone"
            isMobile={smallMobile}
            rules={{ required: 'Required field' }}
            required
            items={[
              ...(timezoneRequired
                ? []
                : [{ label: 'Use Local Time', value: 'local' }]),
              ...timeZoneOptions,
            ]}
          />
          {addClinic && (
            <PatientSelect
              name="clinic"
              label="Clinic"
              id="clinic"
              isMobile={smallMobile}
              rules={{ required: 'Required field' }}
              required
              items={clinics.map((clinic) => ({
                value: clinic.name,
                label: clinic.name,
              }))}
            />
          )}
          <Controller
            name="deviceNotificationsEnabled"
            control={control}
            render={({ field: { name, value, onChange } }) => (
              <FormControlLabel
                className={classes.formControl}
                name={name}
                style={{ marginLeft: '15px', marginTop: '30px' }}
                control={
                  <Switch
                    checked={value}
                    onChange={onChange}
                    inputProps={{ 'aria-label': 'controlled' }}
                  />
                }
                label={'SMS Consent'}
              />
            )}
          />
          <Controller
            name="showTestData"
            control={control}
            render={({ field: { name, value, onChange } }) => (
              <FormControlLabel
                className={classes.formControl}
                name={name}
                style={{ marginLeft: '15px', marginTop: '30px' }}
                control={
                  <Switch
                    checked={value}
                    onChange={onChange}
                    inputProps={{ 'aria-label': 'Show Test Data' }}
                  />
                }
                label={'Show Test Data'}
              />
            )}
          />
        </Fragment>
      )}
    </Fragment>
  )
}
