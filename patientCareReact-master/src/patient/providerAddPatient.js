export const providerAddPatient = async (data, providerID, clinic) => {
  const patient = {
    patientFirstName: data.firstName,
    patientLastName: data.lastName,
    patientMRN: data.mrn,
    email: data.email,
    patientHomeNumber: data.homeNumber,
    patientCellNumber: data.cellNumber,
    patientCity: data.city,
    patientState: data.state,
    patientAddress: data.address,
    patientZip: data.zip,
    patientTimeZone: data.timezone,
    patientBPIMEI: data.bpImei,
    patientTTBpIMEI: data.transtekBpImei,
    patientAdBpIMEI: data.adBpImei,
    patientWeightIMEI: data.weightImei,
    patientTTWeightIMEI: data.transtekWeightImei,
    patientGlucoseIMEI: data.glucoseImei,
    patientPulseIMEI: data.pulseImei,
    selectedBpDevice: data.selectedBpDevice,
    selectedWeightDevice: data.selectedWeightDevice,
    targetWeight: data.targetWeight,
    birthdate: data.birthdate,
    gender: data.gender,
    ethnicity: data.ethnicity,
    maritalStatus: data.maritalStatus,
    education: data.education,
    employment: data.employment,
    income: data.income,
    language: data.language,
    socialConnectedness: data.socialConnectedness,
    allergies: data.allergies,
    chronicConditions: data.chronicConditions,
    hypertensionMedications: data.hypertensionMedications,
  }

  return await fetch('/routes/users/providerAddPatient', {
    method: 'POST',
    body: JSON.stringify({
      ...patient,
      providerId: providerID,
      patientClinic: clinic,
    }),
    headers: { 'Content-Type': 'application/json' },
  })
}
