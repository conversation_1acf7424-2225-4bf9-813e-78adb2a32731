export const getBpThresholdLevel = (systolic, diastolic, threshold, pulse) => {
  if (!threshold) return 'default'

  if (pulse !== undefined && pulse !== null) {
    const pulseThreshold = threshold?.pulse || threshold
    if (
      pulseThreshold?.upperHigh?.value !== undefined &&
      pulse >= pulseThreshold.upperHigh.value
    ) {
      return 'crisis'
    }
    if (
      pulseThreshold?.upperMedium?.value !== undefined &&
      pulse >= pulseThreshold.upperMedium.value
    ) {
      return 'stageTwo'
    }
    if (
      pulseThreshold?.lowerMedium?.value !== undefined &&
      pulse <= pulseThreshold.lowerMedium.value
    ) {
      return 'stageOne'
    }
    if (
      pulseThreshold?.lowerHigh?.value !== undefined &&
      pulse <= pulseThreshold.lowerHigh.value
    ) {
      return 'crisis'
    }
    return 'normal'
  }

  const { systolic: systolicThresholds, diastolic: diastolicThresholds } =
    threshold
  if (!systolicThresholds || !diastolicThresholds) return 'default'

  if (systolic !== null && diastolic === null) {
    if (
      systolicThresholds.crisis?.enabled &&
      systolic >= systolicThresholds.crisis.value
    ) {
      return 'crisis'
    }
    if (
      systolicThresholds.stageTwo?.enabled &&
      systolic >= systolicThresholds.stageTwo.value
    ) {
      return 'stageTwo'
    }
    if (
      systolicThresholds.stageOne?.enabled &&
      systolic >= systolicThresholds.stageOne.value
    ) {
      return 'stageOne'
    }
    if (
      systolicThresholds.elevated?.enabled &&
      systolic >= systolicThresholds.elevated.value
    ) {
      return 'elevated'
    }
    if (
      systolicThresholds.normal?.enabled &&
      systolic < systolicThresholds.normal.value
    ) {
      return 'normal'
    }
  }

  if (systolic === null && diastolic !== null) {
    if (
      diastolicThresholds.crisis?.enabled &&
      diastolic >= diastolicThresholds.crisis.value
    ) {
      return 'crisis'
    }
    if (
      diastolicThresholds.stageTwo?.enabled &&
      diastolic >= diastolicThresholds.stageTwo.value
    ) {
      return 'stageTwo'
    }
    if (
      diastolicThresholds.stageOne?.enabled &&
      diastolic >= diastolicThresholds.stageOne.value
    ) {
      return 'stageOne'
    }
    if (
      diastolicThresholds.elevated?.enabled &&
      diastolic >= diastolicThresholds.elevated.value
    ) {
      return 'elevated'
    }
    if (
      diastolicThresholds.normal?.enabled &&
      diastolic < diastolicThresholds.normal.value
    ) {
      return 'normal'
    }
  }

  if (systolic !== null && diastolic !== null) {
    if (
      (systolicThresholds.crisis?.enabled &&
        systolic >= systolicThresholds.crisis.value) ||
      (diastolicThresholds.crisis?.enabled &&
        diastolic >= diastolicThresholds.crisis.value)
    ) {
      return 'crisis'
    }
    if (
      (systolicThresholds.stageTwo?.enabled &&
        systolic >= systolicThresholds.stageTwo.value) ||
      (diastolicThresholds.stageTwo?.enabled &&
        diastolic >= diastolicThresholds.stageTwo.value)
    ) {
      return 'stageTwo'
    }
    if (
      (systolicThresholds.stageOne?.enabled &&
        systolic >= systolicThresholds.stageOne.value) ||
      (diastolicThresholds.stageOne?.enabled &&
        diastolic >= diastolicThresholds.stageOne.value)
    ) {
      return 'stageOne'
    }
    if (
      (systolicThresholds.elevated?.enabled &&
        systolic >= systolicThresholds.elevated.value) ||
      (diastolicThresholds.elevated?.enabled &&
        diastolic >= diastolicThresholds.elevated.value)
    ) {
      return 'elevated'
    }
    if (
      (systolicThresholds.normal?.enabled &&
        systolic < systolicThresholds.normal.value) ||
      (diastolicThresholds.normal?.enabled &&
        diastolic < diastolicThresholds.normal.value)
    ) {
      return 'normal'
    }
  }

  return 'default'
}
