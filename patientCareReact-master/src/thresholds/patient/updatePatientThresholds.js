export const updatePatientThresholds = async (patientId, data) =>
  fetch('/routes/threshold/patient', {
    method: 'POST',
    body: JSON.stringify({
      patientId,
      ...data,
    }),
    headers: { 'Content-Type': 'application/json' },
  })
    .then((response) => {
      if (response.ok) {
        return response.json()
      }
      throw new Error(response.status)
    })
    .catch((error) => {
      console.error(error)
      return { error }
    })
