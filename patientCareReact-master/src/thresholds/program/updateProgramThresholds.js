export const updateProgamThresholds = (programId, data) =>
  fetch(`/routes/program/${programId}`, {
    method: 'POST',
    body: JSON.stringify(data),
    headers: { 'Content-Type': 'application/json' },
  })
    .then((response) => {
      if (response.ok) {
        return response.json()
      }
      throw new Error(response.status)
    })
    .catch((error) => {
      console.error(error)
      return { error }
    })
