export const getProgramThresholds = async (id) =>
  fetch(`/routes/program/${id}`, {
    withCredentials: true,
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.message === 'Success') {
        return data.program
      }
      return null
    })
    .catch((error) => {
      console.error(error)
      return null
    })
