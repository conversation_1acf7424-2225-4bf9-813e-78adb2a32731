import { Fragment } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import TextField from '@mui/material/TextField'
import Skeleton from '@mui/material/Skeleton'

export const ProgramNameForm = ({ isLoading }) => {
  const { control } = useFormContext()
  return (
    <Fragment>
      {isLoading ? (
        <Skeleton variant="rounded" width="100%" height={56} sx={{ mt: 2 }} />
      ) : (
        <Controller
          name="name"
          control={control}
          render={({ field: { name, value, onChange } }) => (
            <TextField
              required
              variant="outlined"
              label="Name"
              name={name}
              value={value}
              onChange={onChange}
            />
          )}
        />
      )}
    </Fragment>
  )
}

export const ProgramDescriptionForm = () => {
  const { control } = useFormContext()
  return (
    <Controller
      name="description"
      control={control}
      render={({ field: { name, value, onChange } }) => (
        <TextField
          required
          variant="outlined"
          label="Description"
          multiline
          rows={3}
          name={name}
          value={value}
          onChange={onChange}
        />
      )}
    />
  )
}
