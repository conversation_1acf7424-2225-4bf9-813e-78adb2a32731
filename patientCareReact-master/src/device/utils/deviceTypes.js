export const deviceTypes = [
  { value: 'bloodPressure', label: 'Blood Pressure' },
  { value: 'scale', label: 'Weight Scale' },
  { value: 'glucometer', label: 'Glucometer' },
  { value: 'oximeter', label: 'Oximeter' },
  { value: 'peakFlowMeter', label: 'Peak Flow Meter' },
  { value: 'spirometer', label: 'Spirometer' },
  { value: 'sleepTracker', label: 'Sleep Tracker' },
]

export const deviceTypesMap = new Map(
  deviceTypes.map(({ value, label }) => [value, label])
)

/**
 * Gets the display name for a device type
 * @param {Object} device - The device object
 * @param {string} device.type - The device type
 * @returns {string} The display name for the device type
 */
export const getDeviceType = (device = {}) => {
  const { type } = device
  return deviceTypesMap.get(type) || type || 'Unknown Device'
}
