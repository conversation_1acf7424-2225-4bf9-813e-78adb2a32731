import queryString from 'query-string'

export const getDevices = (getCustomersRequest) => {
  const requestParams = {
    ...getCustomersRequest,
    patientId: getCustomersRequest.patientId || null,
  }

  return fetch(`/routes/devices?${queryString.stringify(requestParams)}`, {
    withCredentials: true,
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
  }).then((response) => {
    if (response.ok) {
      return response.json()
    } else {
      throw new Error('Unknown error')
    }
  })
}
