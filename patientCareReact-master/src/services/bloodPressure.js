export const fetchBloodPressureData = async (imei) => {
  const response = await fetch(`/routes/transtek/bloodPressure/${imei}`, {
    withCredentials: true,
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
  })

  if (response.ok) {
    return response.json()
  }

  if (response.status === 404) {
    throw new Error('No blood pressure data found for this device')
  }

  throw new Error('Failed to fetch blood pressure data')
}
