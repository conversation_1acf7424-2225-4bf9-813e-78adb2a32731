export const getPatientByImei = async (imei) => {
  const response = await fetch(`/routes/users/getPatient/${imei}`, {
    withCredentials: true,
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
  })

  if (response.ok) {
    return response.json()
  }

  if (response.status === 404) {
    throw new Error('Patient not found')
  }

  throw new Error('Failed to fetch patient data')
}
