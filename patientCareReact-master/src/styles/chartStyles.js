/**
 * Chart component styles using Material-UI sx props
 */
import { CHART_COLORS } from '../constants/chartConstants'

/**
 * Tooltip container styles
 */
export const tooltipContainerStyles = {
  padding: 2,
  minWidth: 300,
  position: 'relative',
}

/**
 * AHA Logo styles
 */
export const ahaLogoContainerStyles = {
  position: 'absolute',
  top: 6,
  right: 6,
  zIndex: 10,
  '&:hover': {
    transform: 'scale(1.05)',
    transition: 'transform 0.2s ease-in-out',
  },
}

export const ahaLogoImageStyles = {
  width: '32px',
  height: '32px',
  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.15))',
  opacity: 0.9,
}

/**
 * Tooltip header styles
 */
export const tooltipHeaderStyles = {
  display: 'flex',
  justifyContent: 'space-between',
  marginBottom: 2,
  paddingBottom: 1,
  borderBottom: '1px solid #e0e0e0',
  paddingRight: 5,
}

export const tooltipHeaderTextStyles = {
  fontWeight: 'bold',
  fontSize: '16px',
}

/**
 * Tooltip category item styles
 */
export const categoryItemStyles = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginBottom: 1.5,
  paddingY: 0.5,
}

export const categoryColorDotStyles = {
  width: 16,
  height: 16,
  borderRadius: '50%',
  marginRight: 2,
  flexShrink: 0,
}

export const categoryRangeTextStyles = {
  fontSize: '14px',
  color: '#333',
}

export const categoryCategoryTextStyles = {
  fontSize: '14px',
  color: '#666',
  textAlign: 'right',
  minWidth: 120,
}

/**
 * Legend container styles
 */
export const legendContainerStyles = (durationDays, isMobile) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  justifyContent: 'center',
  paddingLeft: durationDays <= 31 ? (isMobile ? 2 : 3) : isMobile ? 1 : 2,
  paddingTop: isMobile ? 1 : 2,
  paddingBottom: isMobile ? 1 : 2,
  paddingRight: 0,
  backgroundColor: '#fff',
  height: '100%',
  borderLeft: '1px solid #f0f0f0',
})

export const legendItemContainerStyles = {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
}

export const legendItemStyles = {
  display: 'flex',
  alignItems: 'center',
  gap: 1,
  cursor: 'pointer',
  '&:hover': {
    opacity: 0.8,
  },
}

export const legendIconStyles = (color) => ({
  width: 16,
  height: 16,
  borderRadius: '50%',
  border: `2px solid ${color}`,
  backgroundColor: 'transparent',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: color,
  fontSize: '10px',
  fontWeight: 'bold',
})

export const legendTextStyles = (color) => ({
  fontSize: '16px',
  fontWeight: '500',
  color: color,
  whiteSpace: 'nowrap',
})

export const legendLineStyles = (color) => ({
  width: '100%',
  height: 2,
  backgroundColor: color,
  marginTop: 0.5,
})

/**
 * Chart container styles
 */
export const chartMainContainerStyles = {
  width: '100%',
  height: '100%',
  position: 'relative',
  display: 'flex',
  flexDirection: 'row',
}

export const yAxisContainerStyles = {
  width: 60,
  minWidth: 60,
  maxWidth: 60,
  height: '100%',
  position: 'sticky',
  zIndex: 2,
  background: '#fff',
}

export const scrollableChartContainerStyles = (
  isMobile,
  isAllTimeFilter,
  dataLength
) => ({
  flex: 1,
  height: '100%',
  overflowX:
    isMobile || (isAllTimeFilter && !isMobile) || dataLength <= 19
      ? 'hidden'
      : 'auto',
  overflowY: 'hidden',
  '&::-webkit-scrollbar': {
    height: '8px',
    display:
      isMobile || (isAllTimeFilter && !isMobile) || dataLength <= 19
        ? 'none'
        : 'block',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: CHART_COLORS.SCROLLBAR_THUMB,
    borderRadius: '4px',
    '&:hover': {
      backgroundColor: CHART_COLORS.SCROLLBAR_THUMB_HOVER,
    },
  },
})

export const chartContentStyles = (width) => ({
  width: width,
  height: '100%',
})

/**
 * Blood pressure charts container styles
 */
export const bpChartsContainerStyles = {
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  gap: 2,
}

export const bpChartItemStyles = {
  width: '100%',
  height: '50%',
}

/**
 * Heart rate legend specific styles
 */
export const hrLegendContainerStyles = (durationDays, isMobile) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  justifyContent: 'center',
  paddingLeft: durationDays <= 31 ? (isMobile ? 2 : 3) : isMobile ? 1 : 2,
  paddingTop: isMobile ? 1 : 2,
  paddingBottom: isMobile ? 1 : 2,
  paddingRight: 0,
  backgroundColor: '#fff',
  height: '100%',
  borderLeft: '1px solid #f0f0f0',
})

export const hrChartContainerStyles = {
  display: 'flex',
  flexDirection: 'row',
  width: '100%',
  height: '100%',
}

export const hrYAxisContainerStyles = {
  width: 60,
  minWidth: 60,
  maxWidth: 60,
  height: '100%',
  position: 'sticky',
  left: 0,
  zIndex: 2,
  background: '#fff',
}
