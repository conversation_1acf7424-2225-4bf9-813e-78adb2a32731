# Cardiowell Careportal

The Cardiowell Careportal contains the frontend code for Cardiowell's applications. 
It contains code for the following services: 
* Admin portal
* Provider portal
* Patient portal
* device updates Registration and Dashboard

View the app here: https://careportal.cardiowell.io/

## Getting Started

**Requirements**
* Node Version > v20.13.0
* Yarn > v1.22.22

**Installation**
1. Clone the repo
	```sh
	<NAME_EMAIL>:Cardiowell/patientCareReact-master.git
	```
2. Install Node Packages with Yarn
	```sh
	yarn
	```


**Setup with proxy server**
1. Setup and start the server application: [https://github.com/Cardiowell/cardiowell-backend](https://github.com/Cardiowell/cardiowell-backend)
2. Take note of the port the server application is running on. Default should be `http://localhost:8081`
3. create a `.env.local` in the main directory. The `API` variable should be pointed to the server application port:
	```

	API=http://localhost:8081 # Server application port

	WEB_UI=http://localhost:3000 # Frontend application port

	PROXY_PORT=3010 # Proxy Port

	```
4. Start the frontend application with 
	```sh
	yarn start
	```
5. start the proxy server with
	```sh
	yarn local-proxy
	```
6. Now all API requests should be redirected to the server application running on `http://localhost:8081`

## Deployment
1. Run `yarn build` in the main directory
2. Copy the generated `/build` folder
3. Replace the `build` folder in the main directory of [server application code repository](https://github.com/Cardiowell/cardiowell-backend)
4. Push the changes into the `master` branch of the server code
5. Navigate to the **cardiowell-application** project in Heroku: <br />[https://dashboard.heroku.com/apps/cardiowell-application](https://dashboard.heroku.com/apps/cardiowell-application)
6. Go the the **Deploy** tab, and scroll down to the **Manual Deploy** option
7. Choose the **master** branch in the dropdown, then hit **Deploy Branch**
8. View project at https://careportal.cardiowell.io/

## Available Scripts

In the project directory, you can run:  

### `yarn start`

Runs the app in the development mode with HTTPS enabled.

Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.

You will also see any lint errors in the console.

### `yarn start-no-https`
Runs the app in development mode without HTTPS.

Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.

You will also see any lint errors in the console.

### `yarn test`
  
Launches the test runner in the interactive watch mode.

### `yarn build`

Builds the app for production to the `build` folder.

It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.

  
### `yarn local-proxy`

Runs a proxy server, redirects frontend requests to a desired location set in the `.env.local` file
