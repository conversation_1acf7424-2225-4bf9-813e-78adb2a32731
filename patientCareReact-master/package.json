{"name": "patient-care", "version": "0.1.0", "private": true, "dependencies": {"@babel/runtime": "^7.24.0", "@date-io/date-fns": "^3.2.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@material-ui/core": "^4.11.0", "@material-ui/icons": "^4.9.1", "@mui/icons-material": "^5.15.12", "@mui/material": "^5.15.12", "@mui/x-data-grid": "^6.19.6", "@mui/x-date-pickers": "^6.19.7", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "ahooks": "^3.7.10", "axios": "^1.9.0", "cnbuilder": "^3.1.0", "datatables.net": "^1.10.20", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "decimal.js": "^10.4.3", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "filestack-react": "^3.1.0", "history": "^4.10.1", "html2canvas": "^1.0.0-rc.5", "http-proxy-middleware": "^2.0.6", "jquery": "^3.5.1", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "mdbreact": "^4.26.0", "moment": "2.24", "moment-timezone": "0.5.13", "query-string": "^9.0.0", "react": "^18.2.0", "react-bootstrap": "^1.0.1", "react-cookie": "^4.0.3", "react-dom": "^18.2.0", "react-google-autocomplete": "^2.7.5", "react-hook-form": "^7.51.0", "react-mobile-picker": "^1.1.1", "react-mobile-picker-scroll": "^0.2.14", "react-moment": "^0.9.7", "react-router-dom": "^5.1.2", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-timezone-select": "^3.2.5", "recharts": "^1.8.5", "socket.io-client": "^2.3.0", "timeme.js": "^2.1.0", "use-places-autocomplete": "^4.0.1", "use-state-with-callback": "^2.0.2", "uuid": "^10.0.0", "validator": "^13.11.0"}, "resolutions": {"moment": "2.24.0"}, "scripts": {"start": "cross-env BROWSER=none react-scripts start", "build": "cross-env GENERATE_SOURCEMAP=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "local-proxy": "node --env-file=.env.local local-proxy.js", "lint": "eslint 'src/**/*.{js,jsx}'"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "^9.9.1", "cross-env": "^7.0.3", "eslint": "^9.9.1", "eslint-config-prettier": "^9.1.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2", "globals": "^15.9.0", "prettier": "^3.5.3"}, "packageManager": "yarn@1.22.22", "proxy": "http://localhost:8081"}